<?php

namespace App\View\Components\Marketplace\Admin;

use App\Models\PlatformSchoolRequirements as Requirement;
use Illuminate\View\Component;

class EducatorInvite extends Component
{
    public $invitation_type;
    public $requirement_id;
    public $valid_at;

    public $is_main_invite;
    public $is_stand_by_invite;
    public $is_sub_invite;
    public $is_replacement_invite;
    public $user;
    public $requirement;

    public function __construct($invitation_type, $requirement_id, $valid_at = null)
    {
        $this->invitation_type = $invitation_type;
        $this->requirement_id = $requirement_id;
        $this->valid_at = $valid_at;

        $this->is_main_invite = $invitation_type === 'main_instructor';
        $this->is_stand_by_invite = $invitation_type === 'stand_by';
        $this->is_sub_invite = $invitation_type === 'sub' && $valid_at;
        $this->is_replacement_invite = $invitation_type === 'main_instructor' && $valid_at;

        $this->user = auth()->user();
        $this->requirement = Requirement::with(['subject.subjectArea', 'classType:id,name', 'timezone'])
                                        ->find($requirement_id);
    }

    public function render()
    {
        return view('components.marketplace.admin.educator-invite', [
            'is_main_invite' => $this->is_main_invite,
            'invitation_type' => $this->invitation_type,
            'is_stand_by_invite' => $this->is_stand_by_invite,
            'is_sub_invite' => $this->is_sub_invite,
            'is_replacement_invite' => $this->is_replacement_invite,
            'user' => $this->user,
            'requirement' => $this->requirement,
        ]);
    }
}
