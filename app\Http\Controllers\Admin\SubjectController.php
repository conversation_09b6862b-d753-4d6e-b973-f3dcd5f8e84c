<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;

use DB;
use Validator;
use Session;
use Illuminate\Http\Request;
use App\Http\Requests;
use App\Subject;
use App\Classes;
use App\Subject_media;
use App\sub_subjects;
use Hash;
use Mail;
use Crypt;
use  App\SubsubjectModel;
use App\Models\v1\BudgetState;
use App\Models\v1\Subject as SubjectModel;
use App\Models\v1\SubjectArea as SubjectAreaModel;
use App\CommomModel;
use Illuminate\Support\Facades\Storage;

DB::enableQueryLog();

class SubjectController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        if(get_childpermission(get_permission(session('Adminnewlogin')['type']),'managesubject','view')!=true){
            return redirect("/no-permission");
        }
        $where = [];
        $subject = Subject::all();
        $subject = DB::table("tbl_subjects")
            ->select("tbl_subjects.*", "c.class_name", "sm.file_name")
            ->leftjoin("tbl_classes as c", "tbl_subjects.grade_level", "=", "c.id")
            ->leftjoin("tbl_subject_medias as sm", "sm.subject_id", "=", "c.id")
            ->get();
        return view("admin.subject.index", compact("subject"));
    }

    public function v1Subjects(Request $request){
        $subjectArea = SubjectModel::where("subject_area_id", $request->id )->orderBy("title", "asc")->get();
        return response()->json(["data" => $subjectArea], 200);
    }

    public function v1SubjectAreas(Request $request){
        $fetch_budgets = $request->fetch_budgets == "true";
        $subjectAreaCollection = SubjectAreaModel::with(['subjects' => function ($query) use ($fetch_budgets) {
            $query->orderBy('title', 'asc');

            if ($fetch_budgets) {
                $userState = auth()->user()->state;

                // Get the matching state ID from budget_states
                $stateId = BudgetState::where('name', $userState)->value('id');
                // Only include subjects that have budgets for this state
                $query->with(['budgets' => function ($q) use ($stateId) {
                    $q->where('state_id', $stateId);
                }]);
            }
        }])->orderBy("subject_area", "asc");
        $subjectArea = $subjectAreaCollection->get();
        return response()->json(["data" => $subjectArea], 200);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function add()
    {
        $class = Classes::where("status", "1")->get();
        return view("admin.subject.add", compact("class"));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function save(Request $request)
    {
                $name = $request->name;

                $data["subject_name"] = $request->name;

                $data["grade_level"] = $request->grade;
                $data["description"] = $request->notes;
                $data["status"] = "1";
                $data["created_at"] = date("Y-m-d H:i:s");
                $data["updated_at"] = date("Y-m-d H:i:s");

                $save = Subject::insertGetId($data);

                if ($save) {
                    if ($request->hasfile("file_data")) {
                        $file = $request->file("file_data");
                        $extension = $file->getClientOriginalExtension();
                        $path = "video-" . time() . "." . $extension;
                        $destinationPath = public_path("/uploads/subject/");
                        $file->move($destinationPath, $path);
                        $obj["file_name"] = url("/uploads/subject/" . $path);
                        $obj["file_type"] = "video";
                        $obj["subject_id"] = $save;

                        $save = Subject_media::insertGetId($obj);
                    }

                    return response()->json([
                        "success" => true,
                        "message" => "Subject  successfully created",
                        "redirect" => url("/subject-list"),
                    ]);
                } else {
                    return response()->json([
                        "success" => false,
                        "message" => "Something went wrong",
                    ]);
                }

    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit(Request $request)
    {
        $id = Crypt::decryptString($request->id);
        $subject = Subject::where("id", $id)->first();
        $class = Classes::where("status", "1")->get();
        return view("admin.subject.edit", [
            "subject" => $subject,
            "class" => $class,
        ]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request)
    {

        $id = $request->id;
        $name = $request->name;

        if ($name) {
            $Exits = Subject::where("subject_name", "=", $name)
                ->where("id", "!=", $id)
                ->get();

            // if (count($Exits)) {
            //     return response()->json(['success' => false, 'message' => 'Subject already exits']);
            // } else {

            $data["subject_name"] = $request->name;
            // $data['class_id'] = $request->class;
            $data["grade_level"] = $request->grade;
            $data["description"] = $request->notes;
            $data["status"] = "1";
            $data["updated_at"] = date("Y-m-d H:i:s");
            $save = Subject::where("id", $id)->update($data);

            if ($save) {
                if ($request->hasfile("file_data")) {
                    $file = $request->file("file_data");
                    $extension = $file->getClientOriginalExtension();
                    $path = "video-" . time() . "." . $extension;
                    $destinationPath = public_path("/uploads/subject/");
                    $file->move($destinationPath, $path);
                    $obj["file_name"] = url("/uploads/subject/" . $path);
                    $obj["file_type"] = "video";
                    $obj["subject_id"] = $save;

                    $save = Subject_media::where("subject_id", $id)->update(
                        $obj
                    );
                }
                return response()->json([
                    "success" => true,
                    "message" => "Details successfully updated",
                    "redirect" => url("/subject-list"),
                ]);
            } else {
                return response()->json([
                    "success" => false,
                    "message" => "Something went wrong",
                ]);
            }
            // }
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }

    public function status_change(Request $request)
    {
        $id = $request->id;
        $record = Subject::where("id", $id)->first();
        if ($record->status == 1) {
            $data["status"] = "0";
            $res = Subject::where("id", $id)->update($data);
            $message = "Status Deactivated Successfully.";
        } else {
            $data["status"] = "1";
            $res = Subject::where("id", $id)->update($data);
            $message = "Status Activated Successfully.";
        }

        return response()->json(
            ["status" => true, "message" => @$message],
            200
        );
    }

    public function delete(Request $request)
    {
        $id = decrypt_str($request->id);
        if (isset($id)) {
            $record = Subject::where("id", $id)->first();
            if ($record) {
                $res = Subject::where("id", "=", $id)->delete();
                $res1 = subject_media::where("subject_id", "=", $id)->delete();
                if ($res) {
                    return response()->json([
                        "success" => true,
                        "message" => "Successfully Deleted",
                    ]);
                } else {
                    return response()->json([
                        "success" => false,
                        "message" => "Something went worng",
                    ]);
                }
            } else {
                return response()->json([
                    "success" => false,
                    "message" => "Record not found",
                ]);
            }
        }
    }


    public function subIndex($id){
        $data['id']=decrypt_str($id);
        $data['subject']=SubsubjectModel::where(['subject_id'=>decrypt_str($id)])->get();
        return view("admin.subject.addsubsubject")->with($data);
    }

    // Fuction to upload image via CKEDITOR.
    public function uploadImage(Request $request)
    {
        if ($request->hasFile('image') && $request->file('image')->isValid()) {
            $validated = $request->validate([
                'image' => 'required|mimes:jpeg,png,jpg,gif|max:2048',
            ]);

            $fileName = time() . '.' . $request->image->extension();
            $uploaded = uploads3image($fileName, $request->file('image'));

            if ($uploaded) {
                $url = Storage::disk('s3')->url($fileName);
                return response()->json(['url' => $url], 200);
            } else {
                return response()->json(['error' => 'Failed to upload image to S3'], 500);
            }
        }

        return response()->json(['error' => 'Invalid Image Upload'], 400);
    }

    public function submitsubsubject(Request $request)
    {

        $validator = Validator::make($request->all(), [
            "name" => "required",
        ]);

        if ($validator->fails()) {
            return response()->json(
                [
                    "success" => false,
                    "error" => "Please enter valid details",
                    "message" => $validator->errors(),
                ],
                400
            );
        }

        $data = $request->except([
            "_token",
            "id",
            "name"
        ]);

        $topic=$request->topic;
        $onlinefile=  $request->file_data_online;

        if($request->type=='add'){
            $namearray=[];
            if(count($request->name)>0){
                $j=0;
                foreach($request->name as $key=>$rowsname){
                    if (isset($onlinefile[$j])) {
                        if ($onlinefile[$j]) {
                            $online_doc = $onlinefile[$j];
                        }
                    }else{
                        $online_doc ='';
                    }
                    if (isset($file_data_in[$j])) {
                       if($file_data_in[$j]) {
                        $inperson_doc = $file_data_in[$j];
                       }
                    }else{
                        $inperson_doc ='';
                    }
                    $namearray[]=[
                        'subject_id' => $request->id,
                        'name' => $rowsname ?? '',
                        'inperson_doc' => $inperson_doc,
                        'online_doc' => $online_doc,
                        'topic' => $topic[$key] ?? '',
                        'status' => 1,
                        'created_at' => date("Y-m-d H:i:s"),
                        'updated_at' => date("Y-m-d H:i:s")];
                    $j++;
                }
            }
            $result = SubsubjectModel::insert($namearray);
            if ($result) {
                return response()->json([
                    "success" => true,
                    "message" => "Submitted successfully",
                ]);
            } else {
                return response()->json([
                    "success" => false,
                    "error" => "Something went wrong",
                ]);
            }
        }else{
            $namearray=[];
            if(count($request->name)>0){
                $i=0;
                foreach(array_filter($request->name) as $key=>$rowsname){
                   $subj= SubsubjectModel::where(['id'=>$key,'subject_id'=>$request->id])->first();
                   if(!empty($subj)){
                        if (isset($onlinefile[$i])) {
                            $online_doc = $onlinefile[$i];
                        }else{
                            $online_doc= $subj->online_doc;;
                        }
                        if (isset($file_data_in[$i])) {
                            if($file_data_in[$i]) {
                                $inperson_doc = $file_data_in[$i];
                            }
                        }else{
                            $inperson_doc = $subj->inperson_doc;
                        }
                        SubsubjectModel::where(['id'=>$key])->update([
                            'subject_id' => $request->id,
                            'name' => $rowsname ?? '',
                            'topic' => $topic[$key] ?? '',
                            'inperson_doc' => $inperson_doc,
                            'online_doc' => $online_doc
                        ]);
                    } else {
                        if (isset($onlinefile[$i])) {
                            if ($onlinefile[$i]) {
                                $online_doc = $onlinefile[$i];
                            }
                        }else{
                            $online_doc ='';
                        }
                        if (isset($file_data_in[$i])) {
                            if($file_data_in[$i]) {
                                $inperson_doc =$file_data_in[$i];
                            }
                        }else{
                            $inperson_doc ='';
                        }
                        SubsubjectModel::insert([
                            'subject_id' => $request->id,
                            'inperson_doc' => $inperson_doc,
                            'online_doc' => $online_doc,
                            'name' => $rowsname,
                            'topic' => $topic[$key] ?? '',
                            'status' => 1,
                            'created_at' => date("Y-m-d H:i:s"),
                            'updated_at' => date("Y-m-d H:i:s")
                        ]);
                    }
                   $i++;
                }
            }
            return response()->json([
                "success" => true,
                "message" => "Submitted successfully",
            ]);
        }
    }

    public function removerow(Request $request)
    {
        $id = $request->id;
        if (isset($id)) {
            $record = SubsubjectModel::where("id", $id)->first();
            if ($record) {
                $res = SubsubjectModel::where("id", "=", $id)->delete();

                if ($res) {
                    return response()->json([
                        "success" => true,
                        "message" => "Successfully deleted",
                    ]);
                } else {
                    return response()->json([
                        "success" => false,
                        "message" => "Something went worng",
                    ]);
                }
            } else {
                return response()->json([
                    "success" => false,
                    "message" => "Record not found",
                ]);
            }
        }
    }
}
