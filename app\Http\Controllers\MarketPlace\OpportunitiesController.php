<?php

namespace App\Http\Controllers\MarketPlace;

use App\Http\Controllers\Controller;
use App\Models\PlatformSchoolRequirements;
use App\Models\v1\UserOpportunityPreference;
use App\Services\EducatorInvitationService;
use App\Models\SchoolReviewApplicants as Proposal;
use Exception;
use Auth;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth as FacadesAuth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class OpportunitiesController extends Controller
{
    protected $inviteService;

    public function __construct(EducatorInvitationService $inviteService)
    {
        $this->inviteService = $inviteService;
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        try{
            $user = Auth::guard('instructor')->user();
            $instructorId = $user->id;
            $search = $request->query('search');
            $tabs = $request->query('tab');
            $sort = $request->query('sort') ?? 'updated_at';
            $sortDirection = $request->query('sort_direction', 'asc');
            $limit = $request->query('limit', 10);
            $filtersRaw = $request->query('filters');
            $filters = is_string($filtersRaw) ? json_decode($filtersRaw, true) : [];

            $query = PlatformSchoolRequirements::query();

            if (!empty($search)) {
                $query->whereHas('school', function ($q) use ($search) {
                    $q->where('first_name', 'like', "%{$search}%")
                        ->orWhere('last_name', 'like', "%{$search}%")
                        ->orWhere('full_name', 'like', "%{$search}%");
                });
            }

            if (!empty($filters)) {
                if (!empty($filters['preference'])) {
                    $query->where('schedule_type', $filters['preference']);
                }
                if (!empty($filters['grade'])) {
                    $query->where('grade_levels_id', 'like', "%{$filters['grade']}%");
                }
                if (!empty($filters['type'])) {
                    $query->where('class_type', $filters['type']);
                }
            }
            switch($tabs) {
                case 'all':
                    $query->whereDoesntHave('userPreferences', function ($query) use ($instructorId) {
                        $query->where('instructor_id', $instructorId)
                            ->where('status', 'archived');
                    });
                    break;
                case 'applied':
                    $query->whereHas('reviewApplicants', function($query) use ($instructorId) {
                        $query->where('instructor_id', $instructorId);
                    });
                    break;
                case 'invited':
                    $query->whereHas('invites', function($query) use ($instructorId) {
                        $query->where('user_id', $instructorId);
                    });
                    break;
                case 'accepted':
                    $query->whereRaw('0=1');
                    break;
                case 'sortlisted':
                    $query->whereHas('userPreferences', function ($query) use ($instructorId) {
                        $query->where('instructor_id', $instructorId)
                            ->where('status',  'saved');
                    });
                    break;
                case 'archived':
                    $query->whereHas('userPreferences', function ($query) use ($instructorId) {
                        $query->where('instructor_id', $instructorId)
                            ->where('status',  'archived');
                    });
                    
                    break;
            }

            $query->orderBy($sort, $sortDirection);
            $query->with(['school:id,school_name', 'subject.subjectArea', 'classType:id,name', 'currentUserPreference',
                'invites' => function ($q) use ($instructorId) {
                    $q->where('user_id', $instructorId);
                },
                'reviewApplicants' => function ($q) use ($instructorId) {
                    $q->where('instructor_id', $instructorId)
                    ->whereNotIn('status', ['rejected','expired','withdraw']); //'pending','accepted','rejected','expired','withdraw'
                },])
                ->where('status', 'open')
                ->select(['id', 'delivery_mode', 'school_id', 'subject_area_id', 'subject_id', 'grade_levels_id', 'class_type', 'schedule_type', 'start_date', 'end_date', 'schedule_1_days', 'schedule_2_days', 'regular_days', 'class_duration', 'total_budget', 'created_at']);
            $opportunities = $query->paginate($limit);
            return response()->json(['success' => true, 'message' => 'Opportunities retrieved successfully.', 'data' => $opportunities], 200);
        } catch (Exception $e) {
            Log::error('Error retrieving opportunities: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Something went wrong while retrieving opportunities.', 'error' => $e->getMessage()], 500);
        }
    }


    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        try {
            $user = Auth::guard('instructor')->user();
            $instructorId = $user->id;
            $data = PlatformSchoolRequirements::with(['school', 'subject', 'meetingLinks', 'subject.subjectArea', 'classType:id,name', 'currentUserPreference', 
            'invites' => function ($q) use ($instructorId) {
                    $q->where('user_id', $instructorId);
                },
                'reviewApplicants' => function ($q) use ($instructorId) {
                    $q->where('instructor_id', $instructorId)
                    ->whereNotIn('status', ['rejected','expired','withdraw']); //'pending','accepted','rejected','expired','withdraw'
                },])->find($id);
            return response()->json(['success' => true, 'message' => 'Opportunity retrieved successfully.', 'data' => $data], 200);
        } catch (Exception $e) {
            // dd($e->getMessage());
            Log::error('Error retrieving opportunity: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Something went wrong while retrieving opportunity.', 'error' => $e->getMessage()], 500);
        }
    }

    public function relatedOpportunities($id)
    {
        try {
            $data = PlatformSchoolRequirements::with(['school', 'subject', 'meetingLinks', 'subject.subjectArea', 'classType:id,name'])->find($id);
            $related = PlatformSchoolRequirements::where('subject_area_id', $data->subject_area_id)->where('status', 'open')->where('id','!=', $id)->get();
            return response()->json(['success' => true, 'message' => 'Opportunity retrieved successfully.', 'data' => $related], 200);
        } catch (Exception $e) {
            Log::error('Error retrieving opportunity: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Something went wrong while retrieving opportunity.', 'error' => $e->getMessage()], 500);
        }
    }

    public function setUserOpportunityPreference(Request $request) {
        $user = Auth::guard('instructor')->user();
        $validatedData = $request->validate([
            'id' => 'required|integer|exists:platform_school_requirements,id',
            'preference' => 'required|in:saved,archived,none|max:255',
        ]);
        $opportunityId = $validatedData['id'];
        $preference = $validatedData['preference'];
        $userId = $user->id;

        $opportunity = UserOpportunityPreference::where(['opportunity_id' => $opportunityId, 'instructor_id' => $userId])->first();

        if ($opportunity) {
            if ($preference == 'none') {
                $opportunity->delete();
                return response()->json(['success' => true, 'message' => 'Opportunity preference removed successfully.'], 200);
            }
            $opportunity->status = $preference;
            $opportunity->save();
            return response()->json(['success' => true, 'message' => 'Opportunity preference updated successfully.'], 200);
        } else if($preference != 'none') {
            $opportunity = new UserOpportunityPreference();
            $opportunity->opportunity_id = $opportunityId;
            $opportunity->instructor_id = $userId;
            $opportunity->status = $preference;
            $opportunity->save();

            return response()->json(['success' => true, 'message' => 'Opportunity preference created successfully.'], 201);
        }

        return response()->json(['success' => false, 'message' => 'Opportunity not found.'], 404);
    }


    public function withdraw($id)
{
    try {
        $existingProposal = Proposal::find($id);

        if (!$existingProposal) {
            return response()->json([
                'success' => false,
                'message' => 'Proposal not found'
            ], 404);
        }

        $existingProposal->status = 'withdraw'; // withdrawn
        $existingProposal->save();

        return response()->json([
            'success' => true,
            'message' => 'Application withdrawn successfully.'
        ], 200);

    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'message' => 'Something went wrong while withdrawing the application.',
            'error'   => $e->getMessage()
        ], 500);
    }
}


    public function sendApply(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'invite_id' => 'nullable|exists:platform_school_invites,id',
                'requirement_id' => 'required|exists:platform_school_requirements,id',
                'offer_description' => 'required',
            ]);

            if ($validator->fails()) {
                return response()->json(['errors' => $validator->errors()], 422);
            }

            $data = $validator->validated();
            $requirement = PlatformSchoolRequirements::find($data['requirement_id']);
            $educator = FacadesAuth::guard('instructor')->user();

            $proposal = $this->inviteService->addProposal($requirement, $educator, $data['offer_description'], $data['invite_id'] ?? null);
            if (!$proposal) {
                return response()->json(['error' => 'Apply is expired or already applied for this opportunity.'], 500);
            }
            return response()->json(['success' => true, 'message' => 'Apply sent successfully', 'data' => $proposal]);
        } catch (Exception $e) {
            Log::error('Error sending apply: ' . $e->getMessage());
            return response()->json(['error' => 'Something went wrong. Please try again later.'], 500);
        }
    }

}
