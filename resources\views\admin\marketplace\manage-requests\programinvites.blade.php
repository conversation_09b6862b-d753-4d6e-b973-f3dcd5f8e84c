@extends('admin.layouts.master')
@section('title')Program Invites List | Whizara @endsection
@section('content')
<style>
    th{
        text-align: left;
    }
    .badge {
        padding: 0.8em 0.8em;
    }
</style>
{{-- MAIN SECTION START --}}
<main class="content">
    <div class="container-fluid p-0">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item " aria-current="page">Manage Marketplace</li>
                <li class="breadcrumb-item " aria-current="page">Program Invites</li>
            </ol>
        </nav>

        <div class="table-responsive" id="applied-requests-table">
            <table class="table table-striped admin-dataTable" style="width:100%">
                <thead class="thead-dark">
                    <tr>
                        <th>Educator Name</th>
                        <th>Educator Email</th>
                        <th>Deadline</th>
                        <th>Invited At</th>
                        <th>Invited By</th>
                        <th>Invite Type</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    @if(!empty($programInvites) && $programInvites->count())
                        @foreach ($programInvites as $invite)
                            <tr>
                                <td><a href="{{ url('admin/k12connections/manage-educator', $invite->user->id) }}">{{ $invite->educator->first_name }} {{ $invite->educator->last_name }}</a></td>
                                <td>{{ $invite->educator->email }}</td>
                                <td>{{ $invite->deadline_date }}</td>
                                <td>{{ $invite->created_at }}</td>
                                <td>{{ $invite->invited_by }}</td>
                                <td>{{ $invite->type }}</td>
                                <td>
                                    <select name="status" id="status" class="form-control" data-inviteId="{{ $invite->id }}" data-field="status" onchange="changeInviteStatus('{{ route('admin.marketplace-updateInviteStatus') }}', '{{ $invite->id }}', this.value)">
                                        <option value="pending" {{ $invite->status == 'pending' ? 'selected' : '' }}>Pending</option>
                                        <option value="accepted" {{ $invite->status == 'accepted' ? 'selected' : '' }}>Accepted</option>
                                        <option value="rejected" {{ $invite->status == 'rejected' ? 'selected' : '' }}>Rejected</option>
                                        <option value="withdraw" {{ $invite->status == 'withdraw' ? 'selected' : '' }}>Withdrawn</option>
                                    </select>
                                </td>
                                <td>
                                    <button class="btn btn-sm btn-primary" @if($invite->status != 'pending') disabled @endif onclick="withdrawInvite('{{ route('admin.marketplace-updateInviteStatus') }}', '{{ $invite->id }}')">Withdraw</button>
                                </td>
                            </tr>
                        @endforeach
                    @else
                        <tr>
                            <td colspan="10">No data found.</td>
                        </tr>
                    @endif
                </tbody>
            </table>
        </div>
        {{-- END --}}

        {{-- EDIT PROFILE SECTION END --}}
    </div>
</main>
{{-- MAIN SECTION END --}}
@endsection

@section('scripts')
<script>
    // Change Invite Status
    async function changeInviteStatus(url, id, status) {
        try {
            const response = await $.ajax({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                url: url,
                method: "POST",
                data: {
                    invite_id: id,
                    status: status
                }
            });
            if (response) {
                alertify.success(response.message);
            } else {
                alertify.error(response.message);
            }
        } catch (error) {
            alertify.error(error.responseJSON.message);
        }
    }

    // Withdraw Invite
    async function withdrawInvite(url, id) {
        try {
            const response = await $.ajax({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                url: url,
                method: "POST",
                data: {
                    invite_id: id,
                    status: 'withdraw'
                }
            });

            if (response) {
                alertify.success(response.message);
                $(`[data-inviteId="${id}"data-field="status"]`).html('Withdrawn');
            } else {
                alertify.error(response.message);
            }
        } catch (error) {
            alertify.error(error.responseJSON.message);
        }
    }
</script>
@endsection
