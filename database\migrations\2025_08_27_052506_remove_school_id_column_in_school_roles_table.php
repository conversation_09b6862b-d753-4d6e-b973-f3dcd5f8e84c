<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class RemoveSchoolIdColumnInSchoolRolesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('school_roles', function (Blueprint $table) {
             $table->dropUnique('school_roles_school_id_name_unique');
            $table->dropColumn('school_id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('school_roles', function (Blueprint $table) {
            $table->unsignedBigInteger('school_id')->comment('FK: schools.id');
            $table->unique(['school_id', 'name']);
            $table->foreign('school_id')->references('id')->on('schools')->onDelete('cascade');
        });
    }
}
