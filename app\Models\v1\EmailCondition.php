<?php

namespace App\Models\v1;

use Illuminate\Database\Eloquent\Model;

class EmailCondition extends Model
{
    protected $table = 'email_conditions';
    protected $fillable = [
        'model_name', 'relation_name', 'field_name', 'operator',
        'value', 'description', 'priority', 'logical_operator', 'active'
    ];

    public function schedules()
    {
        return $this->belongsToMany(EmailSchedule::class, 'email_schedule_conditions', 'condition_id', 'schedule_id')
                    ->withTimestamps();
    }
}
