<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateFeaturedEducatorMatricesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('featured_educator_matrices', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('educator_id');
            $table->date('start_date');     // last feature start
            $table->date('end_date');       // till today
            $table->integer('program_count')->default(0);
            $table->integer('class_count')->default(0);
            $table->decimal('avg_rating', 3, 2)->default(0.00);        // rating in this period
            $table->decimal('lifetime_avg_rating', 3, 2)->default(0.00); // overall rating
            $table->decimal('weighted_score', 10, 2)->default(0.00);   // final score
            $table->unsignedBigInteger('updated_by'); // loggedin admin user
            $table->timestamps();

            $table->foreign('educator_id')->references('id')->on('new_onboarding_instructor')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('featured_educator_matrices');
    }
}
