<?php

namespace App\Models\v1;

use Illuminate\Database\Eloquent\Model;
use App\OnboardingInstructor;
use App\Models\PlatformSchoolRequirements;


class UserOpportunityPreference extends Model
{
    protected $fillable = [
        'instructor_id',
        'opportunity_id',
        'status',
    ];

    // Relationships
    public function instructor()
    {
        return $this->belongsTo(OnboardingInstructor::class, 'instructor_id');
    }

    public function opportunity()
    {
        return $this->belongsTo(PlatformSchoolRequirements::class, 'opportunity_id');
    }
}
