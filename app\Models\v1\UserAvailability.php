<?php

namespace App\Models\v1;

use Illuminate\Database\Eloquent\Model;
use App\OnboardingInstructor;

class UserAvailability extends Model
{
    protected $table = 'user_availability_v1';

    protected $fillable = [
        'user_id',
        'day_of_week',
        'start_time',
        'end_time',
    ];

    public function instructor()
    {
        return $this->belongsTo(OnboardingInstructor::class, 'user_id');
    }
}
