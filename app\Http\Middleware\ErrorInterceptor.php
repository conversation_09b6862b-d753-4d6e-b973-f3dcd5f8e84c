<?php

namespace App\Http\Middleware;

use App\Models\ErrorLog;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;
use Symfony\Component\HttpFoundation\Response;
use Throwable;

class ErrorInterceptor
{
    /**
     * Handle an incoming request.
     *
     * @param \Illuminate\Http\Request $request
     * @param \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response) $next
     * @return \Symfony\Component\HttpFoundation\Response
     */
    public function handle(Request $request, Closure $next): Response
    {
        try {
            $response = $next($request);
            $this->checkAndLogError($request, $response);
            return $response;
        } catch (Throwable $e) {
            $this->safelyLogError($request, $e, $e->getCode() ?: 500);

            // Handle validation errors separately
            if ($e instanceof ValidationException) {
                $errors = $e->validator->errors();
                return $request->expectsJson()
                    ? response()->json(['errors' => $errors], 422)
                    : redirect()->back()->withErrors($errors)->withInput();
            }

            return $request->expectsJson()
                ? response()->json(
                    ['error' => 'An unexpected error occurred.', 'message' => $e->getMessage()],
                    500
                )
                : redirect()->back()->with('error', 'An unexpected error occurred.');
        }
    }

    /**
     * Check response status & log errors if applicable.
     */
    protected function checkAndLogError(Request $request, Response $response): void
    {
        $status = $response->getStatusCode();
        $data = json_decode((string) $response->getContent(), true);

        if ($status >= 400 || ($data && isset($data['success']) && $data['success'] === false)) {
            $this->safelyLogError(
                $request,
                new \RuntimeException('HTTP Error: '.$status),
                $status
            );
        }
    }

    /**
     * Wrap logError with its own try/catch so it cannot break the main request.
     */
    protected function safelyLogError(Request $request, Throwable $e, int $statusCode): void
    {
        try {
            $this->logError($request, $e, $statusCode);
        } catch (Throwable $logError) {
            \Log::error(
                'ErrorInterceptor failed to log error',
                [
                    'original_error' => $e->getMessage(),
                    'logging_error' => $logError->getMessage(),
                    'url' => $request->fullUrl(),
                ]
            );
        }
    }

    /**
     * Actually log the error to DB if not duplicate.
     */
    protected function logError(Request $request, Throwable $e, int $statusCode): void
    {
        $errorData = [
            'message'        => $e->getMessage(),
            'exception_type' => get_class($e),
            'request_data'   => json_encode($request->all(), JSON_THROW_ON_ERROR),
            'url'            => $request->fullUrl(),
            'status_code'    => $statusCode,
        ];

        $isDuplicate = ErrorLog::where('message', $errorData['message'])
            ->where('exception_type', $errorData['exception_type'])
            ->where('request_data', $errorData['request_data'])
            ->where('url', $errorData['url'])
            ->exists();

        if (!$isDuplicate) {
            ErrorLog::create(array_merge(
                $errorData,
                [
                    'stack_trace' => $e->getTraceAsString(),
                    'user_id'     => auth()->id(),
                ]
            ));

            \Log::error(
                $e->getMessage(),
                ['exception' => get_class($e), 'url' => $errorData['url'], 'status' => $statusCode]
            );
        }
    }
}
