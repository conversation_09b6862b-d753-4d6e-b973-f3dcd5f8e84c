@extends('admin.layouts.master')

@section('title')Platform School List | Whizara @endsection

@section('content')
<style>
  :root,
  [data-bs-theme=light] {
    --bs-primary-bg-subtle: #cfe2ff;
    --bs-body-color: #212529;
    --bs-body-bg: #fff;
    --bs-border-color: #dee2e6;
    --bs-border-width: 1px;
    --bs-border-radius: 0.375rem;
    --bs-primary-text-emphasis: #052c65;
  }

  .accordion {
    --bs-accordion-color: var(--bs-body-color);
    --bs-accordion-bg: var(--bs-body-bg);
    --bs-accordion-transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out, border-radius 0.15s ease;
    --bs-accordion-border-color: var(--bs-border-color);
    --bs-accordion-border-width: var(--bs-border-width);
    --bs-accordion-border-radius: var(--bs-border-radius);
    --bs-accordion-inner-border-radius: calc(var(--bs-border-radius) - (var(--bs-border-width)));
    --bs-accordion-btn-padding-x: 1.25rem;
    --bs-accordion-btn-padding-y: 1rem;
    --bs-accordion-btn-color: var(--bs-body-color);
    --bs-accordion-btn-bg: var(--bs-accordion-bg);
    --bs-accordion-btn-icon: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23212529'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
    --bs-accordion-btn-icon-width: 1.25rem;
    --bs-accordion-btn-icon-transform: rotate(-180deg);
    --bs-accordion-btn-icon-transition: transform 0.2s ease-in-out;
    --bs-accordion-btn-active-icon: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23052c65'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
    --bs-accordion-btn-focus-border-color: #86b7fe;
    --bs-accordion-btn-focus-box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
    --bs-accordion-body-padding-x: 1.25rem;
    --bs-accordion-body-padding-y: 1rem;
    --bs-accordion-active-color: var(--bs-primary-text-emphasis);
    --bs-accordion-active-bg: var(--bs-primary-bg-subtle);
  }

  .accordion-item:first-of-type {
    border-top-left-radius: var(--bs-accordion-border-radius);
    border-top-right-radius: var(--bs-accordion-border-radius);
  }

  .accordion-item {
    color: var(--bs-accordion-color);
    background-color: var(--bs-accordion-bg);
    border: var(--bs-accordion-border-width) solid var(--bs-accordion-border-color);
  }

  .accordion-button {
    position: relative;
    display: flex;
    align-items: center;
    width: 100%;
    padding: var(--bs-accordion-btn-padding-y) var(--bs-accordion-btn-padding-x);
    font-size: 1rem;
    color: var(--bs-accordion-btn-color);
    text-align: left;
    background-color: var(--bs-accordion-btn-bg);
    border: 0;
    border-radius: 0;
    overflow-anchor: none;
    transition: var(--bs-accordion-transition);
  }

  .accordion-button:not(.collapsed)::after {
    background-image: var(--bs-accordion-btn-active-icon);
    transform: var(--bs-accordion-btn-icon-transform);
  }

  .accordion-button:not(.collapsed) {
    color: var(--bs-accordion-active-color);
    background-color: var(--bs-accordion-active-bg);
    box-shadow: inset 0 calc(-1 * var(--bs-accordion-border-width)) 0 var(--bs-accordion-border-color);
  }

  .accordion-header {
    margin-bottom: 0;
  }

  .accordion-button {
    position: relative;
    display: flex;
    align-items: center;
    padding: var(--bs-accordion-btn-padding-y) var(--bs-accordion-btn-padding-x);
    font-size: 1rem;
    color: var(--bs-accordion-btn-color);
    text-align: left;
    background-color: var(--bs-accordion-btn-bg);
    border: 0;
    border-radius: 0;
    overflow-anchor: none;
    transition: var(--bs-accordion-transition);
  }

  .accordion-button:not(.collapsed)::after {
    background-image: var(--bs-accordion-btn-active-icon);
    transform: var(--bs-accordion-btn-icon-transform);
  }

  .accordion-button::after {
    flex-shrink: 0;
    width: var(--bs-accordion-btn-icon-width);
    height: var(--bs-accordion-btn-icon-width);
    margin-left: auto;
    content: "";
    background-image: var(--bs-accordion-btn-icon);
    background-repeat: no-repeat;
    background-size: var(--bs-accordion-btn-icon-width);
    transition: var(--bs-accordion-btn-icon-transition);
  }

  .accordion-item:first-of-type .accordion-button {
    border-top-left-radius: var(--bs-accordion-inner-border-radius);
    border-top-right-radius: var(--bs-accordion-inner-border-radius);
  }

  .accordion-button:focus {
    z-index: 3;
    border-color: var(--bs-accordion-btn-focus-border-color);
    outline: 0;
    box-shadow: var(--bs-accordion-btn-focus-box-shadow);
  }

  .accordion-body {
    padding: var(--bs-accordion-body-padding-y) var(--bs-accordion-body-padding-x);
  }

  .budget_states_inputs_form_control {
    width: 47% !important;
  }
</style>
<?php $res = get_permission(session('Adminnewlogin')['type']); ?>
<!-- MAIN SECTION START -->
<main class="content">
  <div class="container-fluid p-0">
      <div class="container">
      <div class="row mt-4 mb-2 d-flex">
        <form id="budget_states_inputs_form" action="{{ route('admin.update-budget-school') }}" method="POST" class="w-100 d-flex">
          @csrf
          <div class="d-flex align-items-center">
            <label for="case_management" class="mb-0 mr-2">Case Management</label>
            <input type="number" value="{{ $budget->case_management }}" id="case_management" name="case_management" class="form-control budget_states_inputs_form_control" min="0" step="1" style="width: 175px;" value="{{ $caseManagement->case_management ?? 0 }}" />
          </div>
          <div class="d-flex align-items-center ml-3">
            <label for="inperson" class="mb-0 mr-2">In Person</label>
            <input type="number" id="inperson" name="in_person" value="{{ $budget->in_person }}" class="form-control budget_states_inputs_form_control budget_states_input" min="0" step="1" style="width: 175px;" />
          </div>
          <div class="d-flex align-items-center ml-3">
            <label for="bilingual_inc" class="mb-0 mr-2">bilingual_inc</label>
            <input type="number" id="bilingual_inc" name="bilingual_inc" value="{{ $budget->bilingual_inc }}" class="form-control bilingual_inc budget_states_inputs_form_control" min="0" step="1" style="width: 175px;" />
          </div>
          <div class="d-flex align-items-center ml-3">
            <label for="sped_rec_comp" class="mb-0 mr-2">sped_rec_comp</label>
            <input type="number" id="sped_rec_comp" name="sped_rec_comp" value="{{ $budget->sped_rec_comp }}" class="form-control bilingual_inc budget_states_inputs_form_control" min="0" step="1" style="width: 175px;" />
          </div>

          <button type="button" id="budget_inputs_save" class="btn btn-primary btn-rounded ml-3">Submit</button>
        </form>
      </div>
      <!-- //csv's -->
      <div class="row mt-4 mb-2 d-flex justify-content-end  align-items-center">
        <div class="d-flex justify-content-between align-items-center">
          <!-- Import CSV Button -->
          <div class="mr-1">
            <form id='import_budget_form'>
              <input type="file" name="csv" accept=".csv" id="import_budget_content" data-url="{{ route('admin.import_budget_content') }}" hidden required class="form-control-file mb-2" />
              <button type="button" id="import_budget_content_button" class="btn btn-success btn-rounded mb-2">Import CSV</button>
            </form>
          </div>

          <!-- Export CSV Button -->
          <div class="mr-1">
            <form action="/admin/budget-content/export/1" id="export_csv_form" method="GET">
              <button type="submit" class="btn btn-success btn-rounded mb-2">Export CSV</button>
            </form>
          </div>

        </div>
      </div>
    </div>


    <div class="accordion" id="subjectAreaAccordion">
      @foreach($subjectAreas as $subjectArea)
      <div class="accordion-item">
        <h2 class="accordion-header" id="heading{{$subjectArea->id}}">
          <button class="accordion-button SubjectAreaAccordionButton collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse{{$subjectArea->id}}" aria-expanded="false" data-id="{{$subjectArea->id}}" aria-controls="collapse{{$subjectArea->id}}">
            {{$subjectArea->subject_area}}
          </button>
        </h2>
        <div id="collapse{{$subjectArea->id}}" class="accordion-collapse collapse" aria-labelledby="heading{{$subjectArea->id}}" data-bs-parent="#accordionExample">
          <div class="accordion-body">
            <div id="subjects-{{$subjectArea->id}}" class="accordion"></div>
            <div id="loading-{{$subjectArea->id}}" style="display: none;">Loading...</div>
          </div>
        </div>
      </div>
      @endforeach
    </div>
  </div>
</main>
<!-- MAIN SECTION END -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
@endsection


@section('scripts')
<script>
  document.addEventListener('DOMContentLoaded', function() {

    $('#subjectAreaAccordion .accordion-button.SubjectAreaAccordionButton').on('click', function() {
      const button = $(this);
      const subjectAreaId = button.data('id');
      const target = `#subjects-${subjectAreaId}`;
      const loading = `#loading-${subjectAreaId}`;

      // If already loaded, skip
      if ($(target).children().length > 0) return;

      $(loading).show();
      $.ajaxSetup({
        headers: {
          'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
      });

      $.ajax({
        url: `school-budget-by-subject-area/${subjectAreaId}`,
        type: 'GET',
        success: function(subjects) {
          $(loading).hide();
          if (subjects.length === 0) {
            $(target).append('<li class="list-group-item">No subjects found.</li>');
          } else {
            subjects.forEach(subject => {

              const subject_budget = subject.school_subject_budget;
              if (subject.title) {
                $(target).append(`
                        <div class="accordion-item">
                          <h2 class="accordion-header" id="heading${subject.id}">
                            <button class="accordion-button SubjectAccordionButton collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse${subject.id}" aria-expanded="false" data-id="${subject.id}" aria-controls="collapse${subject.id}">
                              ${subject.title}
                            </button>
                          </h2>
                          <div id="collapse${subject.id}" class="accordion-collapse collapse" aria-labelledby="heading${subject.id}" data-bs-parent="#accordionExample">
                            <div class="accordion-body">
                              <table>
                                <thead>
                                  <tr>
                                    <th> Base Pay (0-3 yrs) </th>
                                    <th> Incremental Pay (3-6 yrs) </th>
                                    <th> Incremental Pay (6-10 yrs) </th>
                                    <th> Incremental Pay (10+ yrs) </th>
                                    <th> Master's Degree Increment </th>
                                    <th> Doctorate Increment </th>
                                    <th> Non-Teaching Time Compensation </th>

                                    <th> Curriculum increment </th>
                                  </tr>
                                </thead>
                                <tbody>
                                  <tr>
                                    <td class="text-center">${subject_budget?.base_pay_0_3 || 0}</td>
                                    <td class="text-center">${subject_budget?.pay_3_6 || 0}</td>
                                    <td class="text-center">${subject_budget?.pay_6_10 || 0}</td>
                                    <td class="text-center">${subject_budget?.pay_10_plus || 0}</td>
                                    <td class="text-center">${subject_budget?.masters_inc || 0}</td>
                                    <td class="text-center">${subject_budget?.doctorate_inc || 0}</td>
                                    <td class="text-center">${subject_budget?.non_tech_time || 0}</td>
                                    <td class="text-center">${subject_budget?.curriculum_inc || 0}</td>
                                  </tr>
                                </tbody>
                              </table>
                            </div>
                          </div>
                        </div>
                      `);


              }

            });
          }
        },
        error: function() {
          $(loading).hide();
          $(target).append('<li class="list-group-item text-danger">Error loading subjects.</li>');
        }
      });
    })

    $('#import_budget_content_button').on('click', function() {
      $('#import_budget_content').click();
    });
    $('#import_budget_content').on('change', function() {
      const state = $(".budget_state_dropdown").val();
      if (this.files.length === 0) {
        return;
      }
      const file = this.files[0];
      const url = $(this).data('url');
      const formData = new FormData();
      formData.append('csv', file);
      formData.append('state', state);

      $.ajaxSetup({
        headers: {
          'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
      });
      $.ajax({
        url: url,
        type: 'POST',
        data: formData,
        contentType: false,
        processData: false,
        success: function(response) {
          alert('File imported successfully!');
          location.reload(); // Reload the page to see the changes
          $('#import_budget_form').trigger('reset'); // Reset the form
        },
        error: function(xhr, status, error) {
          alert('Error importing file: ' + error);
          $('#import_budget_form').trigger('reset'); // Reset the form
        }
      });
    });
  });

  $(document).on("click", "#budget_inputs_save", function(e) {
    e.preventDefault();
    console.log("dfsdfsdfsd");
    var formdata = $("#budget_states_inputs_form").serialize();
    $.ajaxSetup({
      headers: {
        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
      }
    });
    $.ajax({
      headers: {
        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
      },
      url: '{{ route("admin.budget_states_update") }}', // 🔁 Replace this with your actual Laravel route
      type: 'POST',
      data: formdata,
      success: function(response) {

        if (response.status === "success") {
          alertify.success('Status Update Successfully');
          location.reload();

        }

      },
      error: function(xhr) {
        console.log('Error:', xhr.responseText);
      }
    });


  });

  //this is after changing the state dropdown
  $(document).on("change", ".budget_state_dropdown", function() {

    const budget_select_dropdown_value = $(".budget_state_dropdown").val();
    $('#export_csv_form').attr('action', `/admin/budget-content/export/${budget_select_dropdown_value}`);

    $.ajaxSetup({
      headers: {
        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
      }
    });

    $.ajax({
      url: "{{ route('admin.budget_single_state_data') }}",
      type: 'POST',
      data: {
        id: budget_select_dropdown_value,
      },

      success: function(response) {
        const data = response.budget_state_data;
        $("#budget_state_id").val(data.id);
        const inputs = $('.budget_states_inputs_form_control'); // all four <input> elements

        // Keep the order you asked for
        const order = ['case_management', 'in_person', 'bilingual_inc', 'sped_rec_comp'];

        // Fill the inputs
        order.forEach((key, i) => {
          if (inputs[i]) {
            $(inputs[i]).val(data[key] ?? ''); // fallback to empty string if key missing
          }
        });
        $('#subjectAreaAccordion .accordion').each(function () {
          $(this).html(''); // Clear the subjects for each subject area
        })
        $('#subjectAreaAccordion  .accordion-button').each(function () {
          $(this).addClass('collapsed'); // Remove the collapsed class
        });
        $('#subjectAreaAccordion  .accordion-collapse').each(function () {
          $(this).removeClass('show'); // Remove the collapsed class
        });
      },
      error: function(xhr, status, error) {
        alert('Error importing file: ' + error);

      }
    });

  });


  //this is after clicking on the sinkall button
  $(document).on("click", "#sink_all", function() {

    const state_id = $(".budget_state_dropdown").val();

    $.ajaxSetup({
      headers: {
        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
      }
    });

    $.ajax({
      url: "{{ route('admin.syncToAllStates') }}",
      type: 'POST',
      data: {
        id: state_id,
      },

      success: function(response) {





      },
      error: function(xhr, status, error) {
        alert('Error importing file: ' + error);

      }
    });








  })



  //  $(document).on("click", "#save_budget_states", function (e) {
  //     e.preventDefault();

  //     var formdata = $("#budget_states_form").serialize();
  //       $.ajaxSetup({
  //             headers: { 'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content') }
  //         });
  //     $.ajax({
  //         headers: {
  //             'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
  //         },
  //         url: APP_URL +"/admin/budget_states_update", // 🔁 Replace this with your actual Laravel route
  //         type: 'POST',
  //         data: formdata,
  //         success: function (response) {
  //           if(response.status==="success"){
  //           alertify.success('Status Update Successfully');
  //           $("#confirmUpdateModal").modal("hide");
  //           $('#confirmUpdateModal').on('hidden.bs.modal', function () {
  //           location.reload();
  // });



  //           }
  //         },
  //         error: function (xhr) {
  //             console.log('Error:', xhr.responseText);
  //         }
  //     });
  // });
</script>

@endsection
