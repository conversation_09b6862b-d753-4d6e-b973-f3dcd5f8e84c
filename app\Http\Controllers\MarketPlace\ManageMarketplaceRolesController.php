<?php

namespace App\Http\Controllers\MarketPlace;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Roles;
use App\Permission;
use Exception;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\Log;

class ManageMarketplaceRolesController extends Controller
{
    //******************Roles-Index//******************
    public function index(Request $request)
    {
        $admin = DB::table("tbl_roles")->where("id", "!=", "5")->where("id", "!=", "6")->where("id", "!=", "7")->where("deleted_status", "!=", 1)->orderBy("id", "asc")->get();
        return view("admin.marketplace.access-controll.roles.index", compact("admin"));
    }
    //******************Roles-Index//******************

    // ******************Add-Roles******************
    public function addRoles(Request $request)
    {
        return view("admin.marketplace.access-controll.roles.add");
    }
    // ******************Add-Roles******************

    // ******************Save-Roles******************
    public function saveRoles(Request $request)
    {
        $name = $request->name;
        $status = $request->input("status");
        if ($name != "") {
            $districtExits = Roles::where("name", "=", $name)->get();
            if (count($districtExits)) {
                return response()->json(["success" => false, "message" => "Role already exits"]);
            } else {
                $data["name"] = $request->name;
                $data["status"] = $request->input("status");
                $data["created_at"] = date("Y-m-d H:i:s");
                $data["updated_at"] = date("Y-m-d H:i:s");
                $save = Roles::insertGetId($data);

                if ($save) {
                    $managment = DB::table("tbl_modules")->orderBy("sort_order", "asc")->get();
                    foreach ($managment as $key => $value) {
                        $objP["user_id"] = $save;
                        $objP["module"] = $value->type;
                        $objP["permission_setting"] = "[]";
                        $saveP = Permission::insertGetId($objP);
                    }
                }

                if ($save) {
                    return response()->json(["success" => true, "message" => "Role successfully created"]);
                } else {
                    return response()->json(["success" => false, "message" => "Something went wrong"]);
                }
            }
        }
    }
    // ******************Save-Roles******************

    // ******************Edit-Roles******************
    public function editRoles(Request $request)
    {
        $id = Crypt::decryptString($request->id);
        $admin = Roles::where("id", $id)->first();
        return view("admin.marketplace.access-controll.roles.edit", ["admin" => $admin]);
    }
    // ******************Edit-Roles******************

    // ******************Update-Roles******************
    public function updateRoles(Request $request)
    {
        $obj = [];
        $obj["name"] = $request->input("name");
        $obj["status"] = $request->input("status");
        $id = $request->input("id");
        Roles::where("id", $id)->update($obj);
        return response()->json(["success" => true, "message" => "Successfully update"]);
    }
    // ******************Update-Roles******************

    // ******************Status-Roles******************
    public function status_change(Request $request)
    {
        $id = $request->id;
        $record = Roles::where("id", $id)->first();
        if ($record->status == 1) {
            $data["status"] = "0";
            $res = Roles::where("id", $id)->update($data);
            $message = "Status Deactivated Successfully.";
        } else {
            $data["status"] = "1";
            $res = Roles::where("id", $id)->update($data);
            $message = "Status Activated Successfully.";
        }
        return response()->json(["status" => true, "message" => @$message], 200);
    }
    // ******************Status-Roles******************

    // ******************Delete-Roles******************
    public function deleteRoles(Request $request)
    {
        $id = decrypt_str($request->id);

        if (isset($id)) {
            $record = Roles::where("id", $id)->first();
            if ($record) {
                $data["deleted_status"] = "1";
                $data["status"] = "0";

                $res = Roles::where("id", $id)->update($data);
                if ($res) {
                    return response()->json(["success" => true, "message" => "Successfully Deleted"]);
                } else {
                    return response()->json(["success" => false, "message" => "Something went worng"]);
                }
            } else {
                return response()->json(["success" => false, "message" => "Record not found"]);
            }
        }
    }
    // ******************Delete-Roles******************

    // ******************Open-Permission-Page******************
    public function openPermissionPage(Request $request)
    {
        $encodedId = $request->id;
        try {
            $roleId = Crypt::decryptString($encodedId);
        } catch (\Throwable $e) {
            $roleId = $encodedId; // fallback if not encrypted
        }

        $modules = DB::table("tbl_modules")->where("status", "1")->orderBy("sort_order", "asc")->get();
        $existingPermissions = DB::table("tbl_permissions")->where("user_id", $roleId)->get()->keyBy('module');

        return view("admin.marketplace.access-controll.roles.permission", [
            "modules" => $modules,
            "roleId" => $roleId,
            "existingPermissions" => $existingPermissions
        ]);
    }
    // ******************Open-Permission-Page******************

    // ******************Add-Permission******************
    public function addPermission(Request $request)
    {
        $roleId = $request->input("roleId");

        try {
            // Gather incoming permissions ONLY for modules that were changed in this request
            $incoming = $request->all();
            unset($incoming['_token'], $incoming['roleId']);

            // Extract list of explicitly touched features
            $touchedFeatures = $request->input('touched_features', []);
            if (!is_array($touchedFeatures)) {
                $touchedFeatures = [];
            }

            Log::info('Permission submission for role: ' . $roleId, [
                'touched_features' => $touchedFeatures,
                'payload' => $incoming,
            ]);

            DB::beginTransaction();

            // Only update features that were explicitly touched by the user this submit
            foreach ($touchedFeatures as $featureKey) {
                $permissions = $request->input($featureKey, []);
                if (!is_array($permissions)) {
                    $permissions = [];
                }

                // Normalize entries (unique + reindex)
                $permissions = array_values(array_unique($permissions));
                $permissionJson = json_encode($permissions);

                $where = [
                    'user_id' => $roleId,
                    'module' => $featureKey,
                ];

                $exists = DB::table('tbl_permissions')->where($where)->exists();

                if ($exists) {
                    // Update only the targeted module; save empty array when nothing is checked
                    DB::table('tbl_permissions')->where($where)->update([
                        'permission_setting' => $permissionJson,
                    ]);
                } else {
                    // Create row for this module if it was explicitly modified in this request
                    Permission::insert([
                        'user_id' => $roleId,
                        'module' => $featureKey,
                        'permission_setting' => $permissionJson,
                    ]);
                }
            }

            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();
            return response()->json(["success" => false, "message" => "Permission not added: " . $e->getMessage()]);
        }

        return response()->json(["success" => true, "message" => "Permission added successfully"]);
    }
    // ******************Add-Permission******************
}
