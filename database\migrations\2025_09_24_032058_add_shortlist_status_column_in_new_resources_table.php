<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddShortlistStatusColumnInNewResourcesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('new_resources', function (Blueprint $table) {
            $table->tinyInteger('shortlist_status')->default(0)->after('status')->comment('0 for not shortlisted, 1 for shortlisted');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('new_resources', function (Blueprint $table) {
            $table->dropColumn('shortlist_status');
        });
    }
}
