<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddLatAndLangColumnInPlatformSchoolRequirementsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('platform_school_requirements', function (Blueprint $table) {
            $table->string('lat')->nullable()->after('country');
            $table->string('lng')->nullable()->after('lat');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('platform_school_requirements', function (Blueprint $table) {
            $table->dropColumn(['lat', 'lng']);
        });
    }
}
