@extends('admin.layouts.master')
@section('title') Add Role | Whizara @endsection
@section('content')
<main class="content">
    <div class="container-fluid p-0">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{url('admin-dashboard')}}" class="text-primary">{{ __('messages.dashboard') }}</a></li>
                <li class="breadcrumb-item active" aria-current="page">Manage Marketplace</li>
                <li class="breadcrumb-item active" aria-current="page">Access Control</li>
                <li class="breadcrumb-item active float-right" aria-current="page"><a href="{{url('admin/k12connections/manage-marketplace-roles')}}">{{ __('messages.rolelist') }}</a></li>
                <li class="breadcrumb-item active" aria-current="page">Add role</li>
            </ol>
        </nav>

        <div class="row justify-content-center">
            <div class="col-lg-12 col-md-9">
                <div class="row">
                    <div class="col-lg-12">
                        <div class="card">
                            <form method="post" id="addMarketplaceRole" enctype='multipart/form-data'>
                                <div class="card-header border-bottom">
                                    <h5 class="mb-0">Add role</h5>
                                </div>
                                {{ csrf_field() }}
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6 form-group">
                                            <label class="form-label">{{ __('messages.rolename') }}</label>
                                            <input type="text" id="name"  class="form-control" name="name">
                                            <span id="name_error" class="err"></span>
                                        </div>
                                        <div class="col-md-6 form-group">
                                            <label class="form-label">{{ __('messages.status') }}</label>
                                            <select name="status" id="status" class="form-control">
                                                <option  value="1">{{ __('messages.active') }}</option>
                                                <option  value="0">{{ __('messages.deactive') }}</option>
                                            </select>
                                            <span id="status_error" class="err"></span>
                                        </div>
                                        <div class="col-md-12 d-flex justify-content-md-end justify-content-between mt-3">
                                            <a href="{{redirect()->getUrlGenerator()->previous()}}" class="btn btn-secondary mr-2">{{ __('messages.cancel') }}</a>
                                            <button type="button" id="addMarketplaceRoleButton" class="btn btn-primary">{{ __('messages.add') }}</button>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>
@endsection