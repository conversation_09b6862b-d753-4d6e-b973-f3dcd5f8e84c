<?php

use Carbon\Carbon;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class ActionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::table('tbl_modules')->insert([
            'title' => 'Status Actions',
            'type' => 'statusactions',
            'mod_action' => json_encode([
                'under review',
                'change request',
                'resubmit request',
                'approved without contract',
                'approved',
                'activate',
                'declined',
                'rejected',
                'unsure',
                'withdraw',
            ]),
            'status' => 1,
            'sort_order' => 15,
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
        ]);
    }
}
