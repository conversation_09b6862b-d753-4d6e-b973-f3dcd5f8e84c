<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateApplicantObservationNotesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('applicant_observation_notes', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('applicant_id')->comment('References new_onboarding_instructor(id)');
            $table->foreign('applicant_id')->references('id')->on('new_onboarding_instructor')->onDelete('cascade');
            $table->text('note');
            $table->enum('status', ['Pending', 'Resolved'])->default('Pending');
            $table->unsignedBigInteger('created_by')->comment('User ID who created the note');
            $table->foreign('created_by')->references('id')->on('users')->onDelete('cascade');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('applicant_observation_notes');
    }
}
