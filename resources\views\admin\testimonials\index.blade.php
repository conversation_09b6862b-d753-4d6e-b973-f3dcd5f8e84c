@extends('admin.layouts.master')

@section('title') Testimonial List | Whizara @endsection

@section('content')
    <?php $res = get_permission(session('Adminnewlogin')['type']); ?>
    <!-- MAIN SECTION START -->
    <main class="content">
        <div class="container-fluid p-0">
            <!-- BREADCRUMB START -->
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    @if (isset($res['dashboard']))
                        @if (array_key_exists('dashboard', $res))
                            @if (in_array('add', json_decode($res['dashboard'], true)))
                                <li class="breadcrumb-item"><a href="{{ url('admin-dashboard') }}"
                                        class="text-primary">{{ __('messages.dashboard') }}</a></li>
                            @endif
                        @endif
                    @endif

                    <li class="breadcrumb-item active" aria-current="page">Manage Testimonial</li>

                </ol>
            </nav>
            <!-- BREADCRUMB END -->

            <!-- MONTHLY INVOICES SECTION START -->
            <div class="table-responsive filterdata">
                <table id="dataTable" class="table table-striped" style="width:100%">
                    <thead class="thead-dark">
                        <tr>
                            <th>Id</th>
                            <th>Image</th>
                            <th>Name</th>
                            <th>Designation</th>
                            <th>Testimonial</th>
                            <th>Created date</th>
                            <th>{{ __('messages.action') }}</th>
                        </tr>
                    </thead>
                    <tbody>
                        @if (!empty($testimonial) && $testimonial->count())
                            @foreach ($testimonial as $data)
                                <tr>
                                    <td>{{ $data->id }}</td>
                                    <td><img src="{{ asset('public/testimonial/' . $data->image) }}" width="100px"></td>
                                    <td>{{ $data->name }}</td>
                                    <td>{{ $data->designation }}</td>
                                    <td>{{ $data->description }}</td>
                                    <td>{{ getAdminTimestamp($data->created_at) }}</td>
                                    <td>
                                        <div class="w-100 d-flex justify-content-center align-items-center">
                                            <a href="{{ url('edit-testimonial/' . encrypt_str($data->id)) }}">
                                                <button class="btn btn-rounded btn-block btn-xs btn-outline-secondary"><i class="fa fa-pencil" aria-hidden="true"></i></button>
                                            </a>
                                            &nbsp;
                                            <a class="delete_data_testimonial" href="#" data-id="{{ encrypt_str($data->id) }}">
                                                <button class="btn btn-rounded btn-block btn-xs btn-outline-danger"><i class="fa fa-trash" aria-hidden="true"></i></button>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        @else
                            <tr>
                                <td colspan="10">No data found.</td>
                            </tr>
                        @endif
                    </tbody>
                </table>
            </div>
            <!-- END -->

            <!-- EDIT PROFILE SECTION END -->
        </div>
    </main>
    <!-- MAIN SECTION END -->
@endsection