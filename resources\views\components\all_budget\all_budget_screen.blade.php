<style>
    .heading_text {

        color: #004CBD;
        font-size: 18px !important;

    }

    .new_card_text {
        font-size: 14px;
    }


    .bold {
        font-weight: bold;
    }

    input.instructional_days::placeholder {
        color: #C7C8C9;
    }

    .custom-select-wrapper {
        position: relative;
        display: inline-block;
        width: 200px;
    }


    .custom-select-arrow {
        position: absolute;
        top: 50%;
        right: 31px;
        transform: translateY(-50%);
        pointer-events: none;
    }

    select.delivery_mode {
        width: 100%;
        padding: 10px 40px 10px 15px;
        border-radius: 32px !important;
        border: 1px solid black;
        background-color: white;
        font-size: 16px;
        cursor: pointer;

        /* Fully hide native dropdown arrow across all major browsers */
        appearance: none;
        -webkit-appearance: none;
        -moz-appearance: none;
        background-image: none;
    }

    .budget_management_input {
        width: 149% !important;
        margin-left: -55px;
    }

    .input_absolute {
        top: 8px;
        left: 75px;
        right: 25px;
        width: max-content;
        background-color: white;
        border: none;
    }

    .input_absolute_students {
        top: 8px;
        left: 50px;
        right: 25px;
        width: max-content;
        background-color: white;
        border: none;
    }

    .select2-container--bootstrap4 .select2-selection:hover {
        outline: none !important;
        box-shadow: none !important;
        border-color: black !important;
        /* or any neutral color you want */
    }
</style>
<div class="d-flex p-4 justify-content-between my-5 shadow-box" style="border-radius:12px;">
    <div>
        <h4>Spanish (AP)</h4>
        <div class="d-flex">
            <div class="d-flex gap-1">
                <p style="font-weight:bold;font-size:13px">Educator Profile:</p>
                <p style="font-size:13px">Certified</p>

            </div>

        </div>
        <div class="d-flex">
            <div class="d-flex gap-1">
                <p style="font-weight:bold;font-size:13px">Created on:</p>
                <p style="font-size:13px">04/26/2024</p>

            </div>

        </div>

    </div>
    <div class="d-flex align-items-center">
        <div class="d-flex align-items-center justify-content-center px-3 py-2" style="border:2px solid #004CBD;border-radius:30px">

            <h6 style="color:#004CBD" class="mb-0 me-3">Total Estimated Budget</h6>
            <p style="color:#004CBD">$4,500</p>

        </div>
    </div>
    <div class="d-flex align-items-center">
        <div class="d-flex align-items-center gap-3">
            <button
                disabled
                style="background-color: #8aaedf; color: #e0e0e0; border-radius: 30px;"
                class="px-3 py-1">
                Post Requirement
            </button>
            <div>
                <div class="dropdown">
                    <button style="border:none" class="dropdown-toggle drop_down_button" type="button"
                        id="dropdownMenuButton" data-bs-toggle="dropdown"
                        aria-expanded="false">
                        <svg style="border:none!important" width="17" height="16" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M8.5 3.25C8.91421 3.25 9.25 2.91421 9.25 2.5C9.25 2.08579 8.91421 1.75 8.5 1.75C8.08579 1.75 7.75 2.08579 7.75 2.5C7.75 2.91421 8.08579 3.25 8.5 3.25Z" stroke="#004CBD" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                            <path d="M8.5 8.75C8.91421 8.75 9.25 8.41421 9.25 8C9.25 7.58579 8.91421 7.25 8.5 7.25C8.08579 7.25 7.75 7.58579 7.75 8C7.75 8.41421 8.08579 8.75 8.5 8.75Z" stroke="#004CBD" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                            <path d="M8.5 14.25C8.91421 14.25 9.25 13.9142 9.25 13.5C9.25 13.0858 8.91421 12.75 8.5 12.75C8.08579 12.75 7.75 13.0858 7.75 13.5C7.75 13.9142 8.08579 14.25 8.5 14.25Z" stroke="#004CBD" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                        </svg>



                    </button>
                    <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                        <li><a class="dropdown-item" href="#">Post Class</a>
                            <hr style="margin:0rem 0rem!important">
                        </li>
                        <li><a class="dropdown-item" href="#">Edit Budget</a>
                            <hr style="margin:0rem 0rem!important">

                        </li>
                        <li><a class="dropdown-item" href="#">Discard Budget</a>
                            <hr style="margin:0rem 0rem!important">
                        </li>
                        <li><a class="dropdown-item" href="#">Duplicate</a>

                        </li>
                    </ul>
                </div>
            </div>
        </div>

    </div>




</div>
<div class="d-flex p-4 justify-content-between my-5 shadow-box" style="border-radius:12px;">
    <div>
        <h4>Spanish (AP)</h4>
        <div class="d-flex">
            <div class="d-flex gap-1">
                <p style="font-weight:bold;font-size:13px">Educator Profile:</p>
                <p style="font-size:13px">Certified</p>

            </div>

        </div>
        <div class="d-flex">
            <div class="d-flex gap-1">
                <p style="font-weight:bold;font-size:13px">Created on:</p>
                <p style="font-size:13px">04/26/2024</p>

            </div>

        </div>

    </div>
    <div class="d-flex align-items-center">
        <div class="d-flex align-items-center justify-content-center px-3 py-2" style="border:2px solid #004CBD;border-radius:30px">

            <h6 style="color:#004CBD" class="mb-0 me-3">Total Estimated Budget</h6>
            <p style="color:#004CBD">$4,500</p>

        </div>
    </div>
    <div class="d-flex align-items-center">
        <div class="d-flex align-items-center gap-3">
            <button
                disabled
                style="background-color: #8aaedf; color: #e0e0e0; border-radius: 30px;"
                class="px-3 py-1">
                Post Requirement
            </button>
            <div>
                <div class="dropdown">
                    <button style="border:none" class="dropdown-toggle drop_down_button" type="button"
                        id="dropdownMenuButton" data-bs-toggle="dropdown"
                        aria-expanded="false">
                        <svg style="border:none!important" width="17" height="16" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M8.5 3.25C8.91421 3.25 9.25 2.91421 9.25 2.5C9.25 2.08579 8.91421 1.75 8.5 1.75C8.08579 1.75 7.75 2.08579 7.75 2.5C7.75 2.91421 8.08579 3.25 8.5 3.25Z" stroke="#004CBD" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                            <path d="M8.5 8.75C8.91421 8.75 9.25 8.41421 9.25 8C9.25 7.58579 8.91421 7.25 8.5 7.25C8.08579 7.25 7.75 7.58579 7.75 8C7.75 8.41421 8.08579 8.75 8.5 8.75Z" stroke="#004CBD" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                            <path d="M8.5 14.25C8.91421 14.25 9.25 13.9142 9.25 13.5C9.25 13.0858 8.91421 12.75 8.5 12.75C8.08579 12.75 7.75 13.0858 7.75 13.5C7.75 13.9142 8.08579 14.25 8.5 14.25Z" stroke="#004CBD" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                        </svg>



                    </button>
                    <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                        <li><a class="dropdown-item" href="#">Post Class</a>
                            <hr style="margin:0rem 0rem!important">
                        </li>
                        <li><a class="dropdown-item" href="#">Edit Budget</a>
                            <hr style="margin:0rem 0rem!important">

                        </li>
                        <li><a class="dropdown-item" href="#">Discard Budget</a>
                            <hr style="margin:0rem 0rem!important">
                        </li>
                        <li><a class="dropdown-item" href="#">Duplicate</a>

                        </li>
                    </ul>
                </div>
            </div>
        </div>

    </div>




</div>

 <div class="modal fade budgetcalculatormodal"  tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle">
                <div class="modal-dialog modal-dialog-centered" style="max-width:1106px!important;" role="document">
                    <div class="modal-content p-3">
                        <div class="modal-header" style="border-bottom:none">
                            <div class="d-flex align-items-center">
                               

                                <p class="ms-3  new_card_text heading_text bold mb-0">Calculate budget for your requirement</p>

                            </div>


                            <button type="button" class="close_budgetcontent_modal" data-dismiss="modal" aria-label="Close" style="background: none; border: none; font-size: 1.5rem; line-height: 1; color: #000;">
                                <span aria-hidden="true" style="font-size:2rem;color:#787777">&times;</span>
                            </button>

                        </div>
                        <div class="">
                            <p  style="font-size:14px;width:85%;margin-left:3%">Enter required details to calculate the budget. You can change the values to adjust as per desired budget.
                                You can save budget as well and use it later for posting a class.</p>
                        </div>
                        <!-- <div class="container">
                            <hr class="mt-5" style="height:1px; width:100%; border:none; background-color:grey;">
                        </div> -->
                        <div class="mx-5">
                          <hr class="mt-5" style="height:1px; width:100%; border:none; background-color:grey;">

                        </div>

                        <div class="modal-body">
                            <div class="row d-flex justify-content-between">

                                <div class="col-md-6">
                                    <!-- <div class="search-container my-4 column-gap d-flex align-items-center" style="border:1px solid black">
                                        <div class="search-box">
                                            <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M6.5 13C4.68333 13 3.146 12.3707 1.888 11.112C0.63 9.85333 0.000667196 8.316 5.29101e-07 6.5C-0.000666138 4.684 0.628667 3.14667 1.888 1.888C3.14733 0.629333 4.68467 0 6.5 0C8.31533 0 9.853 0.629333 11.113 1.888C12.373 3.14667 13.002 4.684 13 6.5C13 7.23333 12.8833 7.925 12.65 8.575C12.4167 9.225 12.1 9.8 11.7 10.3L17.3 15.9C17.4833 16.0833 17.575 16.3167 17.575 16.6C17.575 16.8833 17.4833 17.1167 17.3 17.3C17.1167 17.4833 16.8833 17.575 16.6 17.575C16.3167 17.575 16.0833 17.4833 15.9 17.3L10.3 11.7C9.8 12.1 9.225 12.4167 8.575 12.65C7.925 12.8833 7.23333 13 6.5 13ZM6.5 11C7.75 11 8.81267 10.5627 9.688 9.688C10.5633 8.81333 11.0007 7.75067 11 6.5C10.9993 5.24933 10.562 4.187 9.688 3.313C8.814 2.439 7.75133 2.00133 6.5 2C5.24867 1.99867 4.18633 2.43633 3.313 3.313C2.43967 4.18967 2.002 5.252 2 6.5C1.998 7.748 2.43567 8.81067 3.313 9.688C4.19033 10.5653 5.25267 11.0027 6.5 11Z" fill="black" />
                                            </svg>

                                            <input type="text" id="searcsubject" class="search" placeholder="Search Subject" name="search" style="border:none;padding-left:1rem">
                                        </div>



                                    </div> -->

                                    
                                    <div class="row d-flex align-items-center">
                                        <div class="row d-flex justify-content-between my-4">
                                            <div class="col-lg-4 d-flex align-items-center">
                                                <label for="deliveryMode" class="form-label platform_school_label d-inline-block">Requirement Type<span class="text-danger">*</span></label>
                                            </div>

                                            <div class="col-lg-4 position-relative custom-select-wrapper " style="width:250px;margin-right:48px">
                                                <select class="delivery_mode error_calculate_budget" style="border-radius:32px!important;border:1px solid black;padding:3% 6%">
                                                    <option value="" disabled selected hidden class="placeholder-option">Class</option>

                                                    <option>safasfasf</option>
                                                    <option>sdsfsdfdsfs</option>

                                                </select>
                                                <div class="custom-select-arrow">
                                                    <svg width="8" height="4" viewBox="0 0 8 4" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <path fill-rule="evenodd" clip-rule="evenodd" d="M4 2.98204L7.33341 0L8 0.596323L4.33329 3.87652C4.24489 3.95559 4.125 4 4 4C3.875 4 3.75511 3.95559 3.66671 3.87652L0 0.596323L0.666588 0L4 2.98204Z" fill="black"></path>
                                                    </svg>
                                                </div>
                                            </div>


                                        </div>
                                        <div class="row d-flex justify-content-between my-3" >
                                            <div class="col-lg-4 d-flex align-items-center">
                                                <label for="deliveryMode" class="form-label platform_school_label d-inline-block">Subject<span class="text-danger">*</span></label>
                                            </div>

                                            <div class="col-lg-4 position-relative custom-select-wrapper " style="width:250px;margin-right:48px">
                                                <!-- <select class="delivery_mode error_calculate_budget" style="border-radius:32px!important;border:1px solid black;padding:2% 6%">
                                                    <option value="" disabled selected hidden class="placeholder-option">Search Subject</option>
                                                    <option>safasfasf</option>
                                                    <option>sdsfsdfdsfs</option>

                                                </select> -->
                                                <select class="form-select sub_subject select22 new_budget" id="sub_subject" name="sub_subject" style="outline:none;">
                                                    <option value="" selected hidden>Select</option>
                                                    @foreach ($subjectArea as $subjects)
                                                    @if (!empty($subjects->subject_area) && $subjects->subjects->isNotEmpty())
                                                    <optgroup label="{{ $subjects->subject_area }}">
                                                        @foreach ($subjects->subjects as $subject)
                                                        @if (!empty($subject->title))
                                                        @php
                                                        $modifiedCode = strlen($subject->subject_code) == 4 ? '0' . $subject->subject_code : $subject->subject_code;
                                                        @endphp
                                                        <option value="{{ $modifiedCode }}">{{ $subject->title }}</option>
                                                        @endif
                                                        @endforeach
                                                    </optgroup>
                                                    @endif
                                                    @endforeach
                                                </select>


                                                <div class="custom-select-arrow">
                                                    <svg width="8" height="4" viewBox="0 0 8 4" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <path fill-rule="evenodd" clip-rule="evenodd" d="M4 2.98204L7.33341 0L8 0.596323L4.33329 3.87652C4.24489 3.95559 4.125 4 4 4C3.875 4 3.75511 3.95559 3.66671 3.87652L0 0.596323L0.666588 0L4 2.98204Z" fill="black"></path>
                                                    </svg>
                                                </div>
                                            </div>


                                        </div>
                                        <div>

                                        </div>
                                        <div>

                                        </div>
                                        <div class=" col-md-4 mt-4">
                                            <label class="form-label platform_school_label m-0 d-inline-block">Educator Profile<span class="text-danger">*</span></label>
                                        </div>
                                        <!-- //credentialed_or_non_credentialed -->
                                        <div class="col-md-6 mt-4">
                                            <div class="col d-inline-flex align-items-center">

                                                <div class="form-check-inline d-flex ">
                                                    <input class="form-check-input custom-input credentialed" checked type="radio" name="credential_type" id="classType" value="credentialed" style="height:18px!important   ">
                                                    <label class="form-check-label custom-lab" for="classType" style="color:black!important">Credentialed</label>

                                                </div>

                                                <div class="form-check-inline d-flex align-items-center ">
                                                    <input class="form-check-input custom-input non_credentialed" type="radio" name="credential_type" id="nonTeachingType" value="non_credentialed" style="height:18px">
                                                    <label class="form-check-label custom-lab" for="nonTeachingType" style="color:black!important">Non-credentialed</label>
                                                </div>
                                            </div>
                                        </div>

                                    </div>


                                    <!-- special education required -->
                                    <div class="row d-flex align-items-center my-3">
                                        <div class=" col-md-4">
                                            <label class="form-label platform_school_label m-0 d-inline-block">Special education certification required<span class="text-danger">*</span></label>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="col d-inline-flex align-items-center">

                                                <div class="form-check-inline d-flex">
                                                    <input class="form-check-input custom-input credentialed" checked="" type="radio" name="special_education" id="classType" value="credentialed" style="height:18px!important   ">
                                                    <label class="form-check-label custom-lab" for="classType" style="color:black!important">No</label>

                                                </div>

                                                <div class="form-check-inline d-flex align-items-center">
                                                    <input class="form-check-input custom-input non_credentialed" type="radio" name="special_education" id="nonTeachingType" value="non_credentialed" style="height:18px">
                                                    <label class="form-check-label custom-lab" for="nonTeachingType" style="color:black!important">Yes</label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- //delivery mode -->
                                    <div class="row d-flex align-items-center my-3">
                                        <div class=" col-md-4">
                                            <label class="form-label platform_school_label m-0 d-inline-block">Delivery Mode<span class="text-danger">*</span></label>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="col d-inline-flex align-items-center">

                                                <div class="form-check-inline d-flex">
                                                    <input class="form-check-input custom-input credentialed" checked="" type="radio" name="delivery_mode_budget" id="classType" value="credentialed" style="height:18px!important   ">
                                                    <label class="form-check-label custom-lab" for="classType" style="color:black!important">Online</label>

                                                </div>

                                                <div class="form-check-inline d-flex align-items-center">
                                                    <input class="form-check-input custom-input non_credentialed" type="radio" name="delivery_mode_budget" id="nonTeachingType" value="non_credentialed" style="height:18px">
                                                    <label class="form-check-label custom-lab" for="nonTeachingType" style="color:black!important">Hybrid</label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>


                                </div>
                                <div class="col-md-5">
                                    <div class="col-lg-12">
                                        <div class="row d-flex justify-content-between my-4">
                                            <div class="col-lg-4 d-flex align-items-center">
                                                <label for="deliveryMode" class="form-label platform_school_label d-inline-block">Years of Experience<span class="text-danger">*</span></label>
                                            </div>

                                            <div class="col-lg-4 position-relative custom-select-wrapper ">
                                                <select class="delivery_mode error_calculate_budget" style="border-radius:32px!important;border:1px solid black">
                                                    <option value="" disabled selected hidden class="placeholder-option">0-3 years</option>
                                                    <option>sdsfsdfdsfs</option>

                                                </select>
                                                <div class="custom-select-arrow">
                                                    <svg width="8" height="4" viewBox="0 0 8 4" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <path fill-rule="evenodd" clip-rule="evenodd" d="M4 2.98204L7.33341 0L8 0.596323L4.33329 3.87652C4.24489 3.95559 4.125 4 4 4C3.875 4 3.75511 3.95559 3.66671 3.87652L0 0.596323L0.666588 0L4 2.98204Z" fill="black" />
                                                    </svg>
                                                </div>
                                            </div>

                                        </div>
                                    </div>
                                    <div class="col-lg-12">
                                        <div class="col-lg-12">

                                            <div class="row d-flex justify-content-between my-4">
                                                <div class="col-lg-6 d-flex align-items-center">
                                                    <label for="deliveryMode" class="form-label platform_school_label d-inline-block">Number of instructional days<span class="text-danger">*</span></label>
                                                </div>

                                                <div class="col-lg-4 position-relative">
                                                    <input
                                                        placeholder="Enter"
                                                        class="w-100 px-3 py-2 instructional_days budget_management_input error_calculate_budget"
                                                        style="border-radius:48px; border:1p solid #000000; color:#000000;" />
                                                    <div class=" input_absolute  position-absolute">
                                                        days
                                                    </div>


                                                </div>

                                            </div>


                                        </div>


                                    </div>

                                    <div class="col-lg-12">
                                        <div class="row d-flex justify-content-between my-4">
                                            <div class="col-lg-4 d-flex align-items-center">
                                                <label for="deliveryMode" class="form-label platform_school_label d-inline-block">Class duration (in hours)<span class="text-danger">*</span></label>
                                            </div>

                                            <div class="col-lg-4 position-relative custom-select-wrapper ">
                                                <select class="delivery_mode error_calculate_budget" style="border-radius:32px!important;border:1px solid black">
                                                    <option value="" disabled selected hidden class="placeholder-option">Select</option>
                                                    <option>safasfasf</option>
                                                    <option>sdsfsdfdsfs</option>

                                                </select>
                                                <div class="custom-select-arrow">
                                                    <svg width="8" height="4" viewBox="0 0 8 4" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <path fill-rule="evenodd" clip-rule="evenodd" d="M4 2.98204L7.33341 0L8 0.596323L4.33329 3.87652C4.24489 3.95559 4.125 4 4 4C3.875 4 3.75511 3.95559 3.66671 3.87652L0 0.596323L0.666588 0L4 2.98204Z" fill="black" />
                                                    </svg>
                                                </div>
                                            </div>

                                        </div>
                                    </div>
                                    <div class="col-lg-12">
                                        <div class="col-lg-12">

                                            <div class="row d-flex justify-content-between my-4">
                                                <div class="col-lg-6 d-flex align-items-center">
                                                    <label for="deliveryMode" class="form-label platform_school_label d-inline-block">Number of non-instructional hours<span class="text-danger">*</span></label>
                                                </div>

                                                <div class="col-lg-4 position-relative">
                                                    <input
                                                        placeholder="Enter"
                                                        class="w-100 px-3 py-2 instructional_days budget_management_input error_calculate_budget"
                                                        style="border-radius:48px; border:1p solid #000000; color:#000000;" />
                                                    <div class=" input_absolute  position-absolute">
                                                        hours
                                                    </div>


                                                </div>

                                            </div>


                                        </div>


                                    </div>
                                    <div class="col-lg-12">
                                        <div class="row d-flex justify-content-between my-4">
                                            <div class="col-lg-4 d-flex align-items-center">
                                                <label for="deliveryMode" class="form-label platform_school_label d-inline-block">Language of Instruction<span class="text-danger">*</span></label>
                                            </div>

                                            <div class="col-lg-4 position-relative custom-select-wrapper ">
                                                <select class="delivery_mode error_calculate_budget" style="border-radius:32px!important;border:1px solid black">
                                                    <option value="" disabled selected hidden class="placeholder-option">English</option>
                                                    <option>safasfasf</option>
                                                    <option>sdsfsdfdsfs</option>

                                                </select>
                                                <div class="custom-select-arrow">
                                                    <svg width="8" height="4" viewBox="0 0 8 4" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <path fill-rule="evenodd" clip-rule="evenodd" d="M4 2.98204L7.33341 0L8 0.596323L4.33329 3.87652C4.24489 3.95559 4.125 4 4 4C3.875 4 3.75511 3.95559 3.66671 3.87652L0 0.596323L0.666588 0L4 2.98204Z" fill="black" />
                                                    </svg>
                                                </div>
                                            </div>

                                        </div>
                                    </div>
                                    <div class="col-lg-12">
                                        <div class="col-lg-12">

                                            <div class="row d-flex justify-content-between">
                                                <div class="col-lg-6 d-flex align-items-center">
                                                    <label for="deliveryMode" class="form-label platform_school_label d-inline-block">Expected class size<span class="text-danger">*</span></label>

                                                </div>

                                                <div class="col-lg-4 position-relative">
                                                    <input
                                                        placeholder="Enter"
                                                        class="w-100 px-3 py-2 instructional_days budget_management_input error_calculate_budget"
                                                        style="border-radius:48px; border:1p solid #000000; color:#000000;" />
                                                    <div class=" input_absolute_students  position-absolute">
                                                        students
                                                    </div>



                                                </div>

                                            </div>


                                        </div>
                                        <p style="font-size:12px">(Max: 30 students)</p>


                                    </div>
                                </div>

                            </div>
                            <div class="d-flex align-items-center mt-5">
                                <input type="checkbox" class="error_calculate_budget" style="accent-color: #004CBD;">
                                <p class="ms-2" style="color:black;font-size:12px;font-weight:500">Educator will use my school-provided curriculum and teaching materials on the school’s LMS</p>

                            </div>

                            <hr class="mt-5" style="height:1px; width:100%; border:none; background-color:grey;">
                            <div class="d-flex justify-content-end amount_budget_management my-5">
                                <div class="d-flex gap-2 ">
                                    <div id="calculate_budget_button" class="border border-success px-3 py-2" style="background-color:#004CBD;color:white;border-radius:33px;">
                                        Calculate Budget
                                    </div>
                                    <div class="px-3 py-2" style="border:2px solid #004CBD;border-radius:33px;color:004CBD">
                                        $4500- $5500
                                    </div>


                                </div>

                            </div>
                            <hr class="mt-5" style="height:1px; width:100%; border:none; background-color:grey;">

                        </div>
                        <div class="modal-footer py-3 mx-4" style="border-top: 0px; display: flex; justify-content: space-between; width: 95%;background-color:#F2F6FB;border-radius:12px">
                            <div>
                                <!-- <p style="color:black;font-weight:bold">Looks good? Let’s post this class.</p>
                                <p>You can edit before posting</p> -->
                            </div>
                            <div>
                                <button type="button" id="post_requirement_submit_budget" class="btn px-2 py-3" data-dismiss="modal" style="border-radius:21px;border:1px solid black;padding:9px 15px!important;background-color:#004CBD;color:white;font-weight:700">Post Requirement</button>
                                <button class="py-2 px-3"
                                    type="button"
                                    style="border-radius: 21px; padding: 6px 15px !important; border:1px solid black;color:black">
                                    Save Budget
                                </button>

                            </div>

                        </div>

                    </div>
                </div>
            </div>