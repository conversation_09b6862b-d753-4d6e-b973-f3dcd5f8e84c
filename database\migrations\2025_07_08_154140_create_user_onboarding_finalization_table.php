<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateUserOnboardingFinalizationTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('user_onboarding_finalization_v1', function (Blueprint $table) {
            $table->id()->comment('PK: Unique onboarding finalization ID');
            $table->foreignId('user_id')
                ->unique()
                ->constrained('new_onboarding_instructor')
                ->onDelete('cascade')
                ->comment('FK to new_onboarding_instructor.id');
            $table->unsignedInteger('hours_per_week')->comment('Weekly hours available');
            $table->boolean('add_more_subjects')->default(false)->comment('Add more subjects?');
            $table->boolean('open_to_substitute_opportunity')->default(false)->comment('Open to substitute teaching?');
            $table->string('phone_number', 20)->comment('Phone number');
            $table->char('country_code', 5)->comment('Country code e.g. +91');
            $table->string('progress', 255)->comment('Progress notes');
            $table->enum('status', ['incomplete', 'completed'])
                ->default('incomplete')
                ->comment('Onboarding status');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('user_onboarding_finalization_v1');
    }
}
