{{-- User Requirements Table --}}
<div class="card">
    <div class="card-header">User Requirements</div>
    <div class="card-body p-0">
        @if($processedRequirements->count() > 0)
        <div class="table-responsive">
            <table class="table table-striped admin-dataTable table-bordered mb-0">
                <thead class="thead-dark">
                    <tr>
                        <th scope="col" style="text-align: left;">ID</th>
                        <th scope="col" style="text-align: left;">Delivery Mode</th>
                        <th scope="col" style="text-align: left;">Grade Levels</th>
                        <th scope="col" style="text-align: left;">Subject Area</th>
                        <th scope="col" style="text-align: left;">Subject</th>
                        <th scope="col" style="text-align: left;">Class Type</th>
                        <th scope="col" style="text-align: left;">Invites</th>
                        <th scope="col" style="text-align: left;">Potential Applicants</th>
                        <th scope="col" style="text-align: left;">SPED</th>
                        <th scope="col" style="text-align: left;">ESOL</th>
                        <th scope="col" style="text-align: left;">Curriculum</th>
                        <th scope="col" style="text-align: left;">Status</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($processedRequirements as $requirement)
                        <tr>
                            <td><a href="{{ route('admin.marketplace-viewRequirements', ['id' => $requirement['encrypted_id']]) }}">{{ $requirement['id'] }}</a></td>
                            <td>
                                <span class="badge badge-{{ $requirement['delivery_mode'] == 'online' ? 'primary' : 'success' }}">
                                    {{ ucfirst($requirement['delivery_mode']) }}
                                </span>
                            </td>
                            <td>{{ $requirement['grade_levels'] }}</td>
                            <td>{{ $requirement['subject_area'] }}</td>
                            <td>{{ $requirement['subject'] }}</td>
                            <td>{{ $requirement['class_type'] }}</td>
                            <td class="text-center">{{ $requirement['total_invites'] }} @if($requirement['accepted_invites'] > 0)({{ $requirement['accepted_invites'] }})@endif </td>
                            <td class="text-center">{{ $requirement['potential_applicants'] }}</td>
                            <td class="text-center">
                                @if($requirement['special_education_required'])
                                    <i class="fas fa-check-circle text-success" title="Special Education Required"></i>
                                @else
                                    <i class="fas fa-times-circle text-muted" title="Special Education Not Required"></i>
                                @endif
                            </td>
                            <td class="text-center">
                                @if($requirement['esol_required'])
                                    <i class="fas fa-check-circle text-success" title="ESOL Required"></i>
                                @else
                                    <i class="fas fa-times-circle text-muted" title="ESOL Not Required"></i>
                                @endif
                            </td>
                            <td class="text-center">
                                @if($requirement['curriculum_provided'])
                                    <i class="fas fa-check-circle text-success" title="Curriculum Provided"></i>
                                @else
                                    <i class="fas fa-times-circle text-muted" title="No Curriculum Provided"></i>
                                @endif
                            </td>
                            <td>
                                <span class="badge badge-{{ $requirement['status'] == 'active' ? 'success' : ($requirement['status'] == 'draft' ? 'warning' : 'secondary') }}">
                                    {{ ucfirst($requirement['status']) }}
                                </span>
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
        @else
            <p class="text-muted m-3">No requirements added yet.</p>
        @endif
    </div>
</div>