<?php

namespace App\Models;

use App\OnboardingInstructor;
use Illuminate\Database\Eloquent\Model;

class k12ConnectionClasses extends Model
{
    protected $table = 'k12_connection_classes';
    protected $fillable = [
        'program_id',
        'main_instructor_id',
        'sub_instructor_id',
        'meeting_id',
        'day',
        'class_date',
        'start_time',
        'end_time',
        'status',
        'reassigned_to',
        'sub_request_id',
        'document',
        'invite_id',
        'is_replacement',
        'parent_id',
        'makeup_class_id',
        'proctor_log_id',
        'class_log_id',
        'payment_log_id',
        'status_updated_at',
    ];

    
    public function proctorLog()
    {
        return $this->belongsTo(k12ConnectionProctorFeedback::class, 'proctor_log_id');
    }
    public function classLog()
    {
        return $this->belongsTo(k12ConnectionClassLogs::class, 'class_log_id');
    }

    public function programs()
    {
        return $this->belongsTo(k12ConnectionPrograms::class, 'program_id');
    }
    
    public function mainUser()
    {
        return $this->belongsTo(OnboardingInstructor::class, 'main_instructor_id');
    }

    public function subUser()
    {
        return $this->belongsTo(OnboardingInstructor::class, 'sub_instructor_id');
    }
}