<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateMessagingAndChatbotTables extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Drop existing if needed
        Schema::dropIfExists('chat_attachments');
        Schema::dropIfExists('chats');
        Schema::dropIfExists('attachments');
        Schema::dropIfExists('chatbot_chats');

        // Messages (Chats)
        Schema::create('messages', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('sender_id');
            $table->enum('sender_type', ['educator', 'school', 'admin']);
            $table->uuid('recipient_id');
            $table->enum('recipient_type', ['educator', 'school', 'admin']);
            $table->text('content');
            $table->enum('message_type', ['private', 'program', 'requirement'])->default('private');
            $table->unsignedBigInteger('reference_id')->nullable();
            $table->uuid('parent_id')->nullable()->index();

            $table->enum('status', ['sent', 'delivered', 'read'])->default('sent');
            $table->timestamp('delivered_at')->nullable();
            $table->timestamp('read_at')->nullable();

            $table->timestamps();
            $table->softDeletes();

            $table->index(['sender_id', 'recipient_id']);
        });

        // Attachments
        Schema::create('attachments', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->string('file_url', 255);
            $table->string('file_name', 255)->nullable();
            $table->string('mime_type', 100)->nullable();
            $table->integer('file_size')->nullable(); // in KB or bytes
            $table->timestamps();
            $table->softDeletes();
        });

        // Pivot: Message ↔ Attachments
        Schema::create('message_attachments', function (Blueprint $table) {
            $table->uuid('message_id');
            $table->uuid('attachment_id');

            $table->primary(['message_id', 'attachment_id']);

            $table->foreign('message_id')
                ->references('id')->on('messages')
                ->onDelete('cascade');

            $table->foreign('attachment_id')
                ->references('id')->on('attachments')
                ->onDelete('cascade');
        });

        // Chatbot Interactions
        Schema::create('chatbot_interactions', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->string('user_id', 255);

            // since you said platforms are "educator" and "school"
            $table->enum('platform', ['educator', 'school']);

            $table->text('user_message');
            $table->text('session_token')->nullable();
            $table->text('bot_response')->nullable();

            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('message_attachments');
        Schema::dropIfExists('attachments');
        Schema::dropIfExists('messages');
        Schema::dropIfExists('chatbot_interactions');
    }
}
