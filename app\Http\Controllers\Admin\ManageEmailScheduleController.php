<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\EmailSchedule;
use App\EmailCondition;
use App\Helpers\DataTableHelper;
use App\Models\v1\EmailTemplate;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class ManageEmailScheduleController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        if ($request->ajax()) {
            $params = DataTableHelper::getParams($request);

            if ($params['columnName'] == 'id' || empty($params['columnName'])) {
                $params['columnName'] = 'email_schedules.id';
            }

            $qry = EmailSchedule::with('createdBy')
                ->orderBy($params['columnName'], $params['columnSortOrder'] ?? 'desc');

            $qry->where(function ($que) use ($params) {
                DataTableHelper::applySearchFilter($que, $params['searchValue'], $params['columns']);
            });

            [$count, $result] = DataTableHelper::applyPagination($qry, $params['row'], $params['rowperpage']);

            $data = [];
            foreach ($result as $schedule) {
                $data[] = [
                    'id' => $schedule->id,
                    'name' => $schedule->name,
                    'recurrence' => ucfirst($schedule->recurrence),
                    'recipients_to' => $this->formatRecipients($schedule->recipients_to),
                    'status' => $this->generateStatusBadge($schedule->status),
                    'action' => $this->generateActionButtons($schedule->id)
                ];
            }
            return DataTableHelper::generateResponse($params['draw'], $count, $data);
        }
        return view('admin.marketplace.email-schedules.index');
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        try {
            $schedule = EmailSchedule::findOrFail($id);
            $schedule->delete();
            return response()->json(['success' => true, 'message' => 'Email schedule deleted successfully']);
        } catch (Exception $e) {
            return response()->json(['success' => false, 'message' => 'Failed to delete email schedule'], 500);
        }
    }

    /**
     * Format recipients array for display
     *
     * @param mixed $recipients
     * @return string
     */
    private function formatRecipients($recipients)
    {
        if (empty($recipients)) {
            return '<span class="text-muted">No recipients</span>';
        }
        $recipientsArray = is_array($recipients) ? $recipients : json_decode($recipients, true);
        if (!is_array($recipientsArray) || count($recipientsArray) === 0) {
            return '<span class="text-muted">No recipients</span>';
        }
        $displayRecipients = array_slice($recipientsArray, 0, 3);
        $recipientsText = implode(', ', $displayRecipients);
        if (count($recipientsArray) > 3) {
            $recipientsText .= ' <span class="text-muted">+' . (count($recipientsArray) - 3) . ' more</span>';
        }
        return $recipientsText;
    }

    /**
     * Generate status badge HTML
     *
     * @param string $status
     * @return string
     */
    private function generateStatusBadge($status)
    {
        $badgeClass = $status === 'enabled' ? 'success' : 'secondary';
        return '<span class="badge badge-' . $badgeClass . '">' . ucfirst($status) . '</span>';
    }

    /**
     * Generate action buttons HTML
     *
     * @param int $scheduleId
     * @return string
     */
    private function generateActionButtons($scheduleId)
    {
        $editUrl = url('admin/k12connections/manage-email-schedules/' . $scheduleId . '/edit');
        $deleteUrl = url('admin/k12connections/manage-email-schedules/' . $scheduleId);
        return '
            <a href="' . $editUrl . '" class="btn btn-outline-secondary btn-sm" title="Edit">
                <i class="fa fa-pencil" aria-hidden="true"></i>
            </a>
            <button class="btn btn-outline-danger btn-sm delete-schedule" data-id="' . $scheduleId . '" data-url="' . $deleteUrl . '" title="Delete">
                <i class="fa fa-trash" aria-hidden="true"></i>
            </button>
        ';
    }
}
