<style>
  select.form-select.w-100.k_time_select {
      color: #494848;
      padding: 8px 8px 8px 20px;
      border: 2px solid #000000;
      border-radius: 23px;
      margin-bottom: 15px;
      max-width: 123px;
  }
</style>
@php
    $days = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];

    $timeOptions = [];
    $start = strtotime('00:00');
    $end = strtotime('23:30');

    while ($start <= $end) {
        $timeOptions[] = date('h:i A', $start); // 12-hour format with AM/PM
        $start = strtotime('+30 minutes', $start);
    }
@endphp

<ul class="days_group flex flex-column">
    @foreach ($days as $day)
    @php
      $availability_entries = isset($availability) ? $availability->where('day_of_week', $day) : [];
    @endphp
        <li class="day_container w-100">
            <div class="day_switch row align-items-start justify-content-between">
                <div class="col-md-4 pt-1 d-flex gap-3 justify-content-start align-items-center">
                    <div class="toggle_container">
                        <label class="toggler_switch" for="{{ $day }}_toggle">
                            <input type="checkbox" id="{{ $day }}_toggle" hidden name="day_available[]"
                                value="{{ $day }}" @if($availability_entries->count() > 0) checked @endif>
                        </label>
                    </div>
                    <span class="day_label"> {{ ucfirst($day) }} </span>
                </div>
                <div class="col-md-7 details_container gap-2 d-flex flex-column time_entries" data-id="{{ $day }}">
                    @if ($availability_entries->count() > 0)
                        @foreach ($availability_entries as $key => $entry)
                        <div class="d-flex justify-content-between gap-2 align-items-center time_entry">
                            <input type="hidden" name="availability[{{ $key }}][id]" value="{{ $entry->id }}">
                            <input type="hidden" name="availability[{{ $key }}][day_of_week]" value="{{ $day }}">
                            <select name="availability[{{ $key }}][start_time]" class="form-select w-100 k_time_select mb-0" data-required="true">
                                @foreach ($timeOptions as $time)
                                    @php
                                        $parsedInput = \Carbon\Carbon::createFromFormat('h:i A', $time)->format('H:i:s');
                                    @endphp
                                    <option value="{{ $parsedInput }}" @if($parsedInput == $entry->start_time) selected @endif>{{ $time }}</option>
                                @endforeach
                            </select>
                            <span class="to_label"> to </span>
                            <select name="availability[{{ $key }}][end_time]" class="form-select w-100 k_time_select mb-0" data-required="true">
                                @foreach ($timeOptions as $time)
                                @php
                                    $parsedInput = \Carbon\Carbon::createFromFormat('h:i A', $time)->format('H:i:s');
                                @endphp
                                    <option value="{{ $parsedInput }}" @if($parsedInput == $entry->end_time) selected @endif>{{ $time }}</option>
                                @endforeach
                            </select>
                            <span class="remove_time_entry text-dark" style="cursor: pointer; min-width: 24px;">
                                <i class="fa fa-times mx-0"></i>
                            </span>
                        </div>
                        @endforeach
                    @endif
                </div>
                <div class="col-md-1 mt-1 add_button_availability @if($availability_entries->count() == 0) d-none @endif align-items-center justify-content-center">
                    <div id="days_add_more_button" data-container="{{ $day }}"
                        class="circle-icon p-1 d-flex justify-content-center align-items-center rounded-circle border"
                        style="width: 24px; height: 24px;">
                        <i class="fa fa-plus mx-0 text-primary"></i>
                    </div>
                </div>
            </div>
        </li>
    @endforeach
</ul>

<div class="d-none justify-content-between gap-2 align-items-center time_entry" id="time_entries_clone">
    <input type="hidden" data-name="availability[0][id]">
    <input type="hidden" data-name="availability[0][day_of_week]" value="{{ $day }}">
    <select data-name="availability[0][start_time]" class="form-select w-100 k_time_select mb-0" data-required="true">
        @foreach ($timeOptions as $time)
            @php
                $parsedInput = \Carbon\Carbon::createFromFormat('h:i A', $time)->format('H:i:s');
            @endphp
            <option value="{{ $parsedInput }}">{{ $time }}</option>
        @endforeach
    </select>
    <span class="to_label"> to </span>
    <select data-name="availability[0][end_time]" class="form-select w-100 k_time_select mb-0" data-required="true">
        @foreach ($timeOptions as $time)
            @php
                $parsedInput = \Carbon\Carbon::createFromFormat('h:i A', $time)->format('H:i:s');
            @endphp
            <option value="{{ $parsedInput }}">{{ $time }}</option>
        @endforeach
    </select>
    <span class="remove_time_entry text-dark" style="cursor: pointer; min-width: 24px;">
        <i class="fa fa-times mx-0"></i>
    </span>
</div>