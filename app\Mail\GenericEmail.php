<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class GenericEmail extends Mailable
{
    use Queueable, SerializesModels;

    protected $template;
    protected $context;

    public function __construct($template, $context = [])
    {
        $this->template = $template;
        $this->context = $context;
    }

    public function build()
    {
        // Replace variables in template body if needed
        $content = 
            $this->template->layout->header_html .
            '<hr style="margin:20px 0;border:0;border-top:1px solid #ccc;">' .
            $this->template->body_html .
            '<hr style="margin:20px 0;border:0;border-top:1px solid #ccc;">' .
            $this->template->layout->footer_html;
        $body = $this->replaceVariables($content, $this->context);

        return $this->subject($this->template->subject)
            ->html($body);
    }

    protected function replaceVariables(string $body, array $context): string
    {
        foreach ($context as $key => $value) {
            $body = str_replace('{{' . $key . '}}', $value, $body);
        }
        return $body;
    }
}
