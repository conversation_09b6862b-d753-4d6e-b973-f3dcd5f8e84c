<?php $__env->startSection('title'); ?>
    Edit Email Schedule | Whizara
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<style>
    span.select2.select2-container.select2-container--bootstrap4 {
        width: 100% !important;
    }
    .err {
        color: red;
        font-size: 0.875rem;
    }
    .recipient-tag {
        display: inline-block;
        background: #007bff;
        color: white;
        padding: 4px 8px;
        margin: 2px;
        border-radius: 4px;
        font-size: 12px;
    }
    .recipient-tag .remove-tag {
        margin-left: 5px;
        cursor: pointer;
        font-weight: bold;
    }
</style>

<!-- MAIN SECTION START -->
<main class="content">
    <div class="container-fluid p-0">
        <!-- BREADCRUMB START -->
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="<?php echo e(url('admin/k12connections/manage-email-schedules')); ?>">Email Schedules</a></li>
                <li class="breadcrumb-item active" aria-current="page">Edit Email Schedule</li>
            </ol>
        </nav>
        <!-- BREADCRUMB END -->

        <!-- EDIT EMAIL SCHEDULE START -->
        <form id="edit_email_schedule_form" enctype='multipart/form-data'>
            <div class="row justify-content-center">
                <div class="col-lg-12 col-md-9">
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="card">
                                <div class="card-header border-bottom">
                                    <h5 class="mb-0">Edit Email Schedule</h5>
                                </div>
                                <?php echo e(csrf_field()); ?>

                                <?php echo e(method_field('PUT')); ?>

                                <div class="card-body">
                                    <!-- Basic Information -->
                                    <div><h5 class="mb-3">Basic Information</h5></div>
                                    <div class="row border-bottom pb-3 mb-4">
                                        <div class="col-md-6 form-group">
                                            <label for="name">Schedule Name</label><span class="text-danger">*</span>
                                            <input type="text" class="form-control" id="name" name="name" value="<?php echo e($schedule->name); ?>" placeholder="Enter schedule name">
                                            <span id="name_error" class="err"></span>
                                        </div>
                                        
                                        <div class="col-md-6 form-group">
                                            <label for="status">Status</label><span class="text-danger">*</span>
                                            <select class="form-control" id="status" name="status">
                                                <option value="enabled" <?php echo e($schedule->status === 'enabled' ? 'selected' : ''); ?>>Enabled</option>
                                                <option value="disabled" <?php echo e($schedule->status === 'disabled' ? 'selected' : ''); ?>>Disabled</option>
                                            </select>
                                            <span id="status_error" class="err"></span>
                                        </div>
                                        
                                        <div class="col-md-6 form-group">
                                            <label for="recurrence">Recurrence</label><span class="text-danger">*</span>
                                            <select class="form-control" id="recurrence" name="recurrence">
                                                <option value="once" <?php echo e($schedule->recurrence === 'once' ? 'selected' : ''); ?>>Once</option>
                                                <option value="daily" <?php echo e($schedule->recurrence === 'daily' ? 'selected' : ''); ?>>Daily</option>
                                                <option value="weekly" <?php echo e($schedule->recurrence === 'weekly' ? 'selected' : ''); ?>>Weekly</option>
                                                <option value="monthly" <?php echo e($schedule->recurrence === 'monthly' ? 'selected' : ''); ?>>Monthly</option>
                                                <option value="yearly" <?php echo e($schedule->recurrence === 'yearly' ? 'selected' : ''); ?>>Yearly</option>
                                            </select>
                                            <span id="recurrence_error" class="err"></span>
                                        </div>
                                        
                                        <div class="col-md-6 form-group">
                                            <label for="event_name">Event Name</label>
                                            <input type="text" class="form-control" id="event_name" name="event_name" value="<?php echo e($schedule->event_name); ?>" placeholder="Optional event name">
                                            <span id="event_name_error" class="err"></span>
                                        </div>
                                    </div>

                                    <!-- Recipients Section -->
                                    <div><h5 class="mb-3">Recipients</h5></div>
                                    <div class="row border-bottom pb-3 mb-4">
                                        <div class="col-md-6 form-group">
                                            <label for="recipients_to_input">Recipients To</label>
                                            <div class="input-group">
                                                <input type="text" class="form-control" id="recipients_to_input" placeholder="Enter recipient (e.g., all_educator, school, user:admin)">
                                                <div class="input-group-append">
                                                    <button type="button" class="btn btn-primary" id="add_recipient_to">Add</button>
                                                </div>
                                            </div>
                                            <div id="recipients_to_tags" class="mt-2"></div>
                                            <input type="hidden" name="recipients_to" id="recipients_to_hidden">
                                            <small class="form-text text-muted">Examples: all_educator, school, user:super_admin</small>
                                        </div>
                                        
                                        <div class="col-md-6 form-group">
                                            <label for="recipients_cc_input">Recipients CC</label>
                                            <div class="input-group">
                                                <input type="text" class="form-control" id="recipients_cc_input" placeholder="Enter CC recipient">
                                                <div class="input-group-append">
                                                    <button type="button" class="btn btn-primary" id="add_recipient_cc">Add</button>
                                                </div>
                                            </div>
                                            <div id="recipients_cc_tags" class="mt-2"></div>
                                            <input type="hidden" name="recipients_cc" id="recipients_cc_hidden">
                                        </div>
                                    </div>

                                    <!-- Templates Section -->
                                    <div><h5 class="mb-3">Email Templates</h5></div>
                                    <div class="row border-bottom pb-3 mb-4">
                                        <div class="col-md-12 form-group">
                                            <label for="templates">Select Templates</label>
                                            <select class="form-control select2" id="templates" name="templates[]" multiple>
                                                <?php $__currentLoopData = $templates; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $template): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <option value="<?php echo e($template->id); ?>" 
                                                        <?php echo e(in_array($template->id, $schedule->templates->pluck('id')->toArray()) ? 'selected' : ''); ?>>
                                                        <?php echo e($template->title); ?>

                                                    </option>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </select>
                                        </div>
                                    </div>

                                    <!-- Conditions Section -->
                                    <div><h5 class="mb-3">Email Conditions</h5></div>
                                    <div class="row pb-3 mb-4">
                                        <div class="col-md-12 form-group">
                                            <label for="conditions">Select Conditions</label>
                                            <select class="form-control select2" id="conditions" name="conditions[]" multiple>
                                                <?php $__currentLoopData = $conditions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $condition): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <option value="<?php echo e($condition->id); ?>"
                                                        <?php echo e(in_array($condition->id, $schedule->conditions->pluck('id')->toArray()) ? 'selected' : ''); ?>>
                                                        <?php echo e($condition->description ?? $condition->model_name); ?>

                                                    </option>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </select>
                                        </div>
                                    </div>

                                    <!-- Submit Buttons -->
                                    <div class="col-md-12 d-flex justify-content-md-end justify-content-between mt-3">
                                        <a href="<?php echo e(url('admin/k12connections/manage-email-schedules')); ?>" class="btn btn-secondary mr-2">Cancel</a>
                                        <button type="button" id="update_email_schedule" class="btn btn-primary">Update Schedule</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
        <!-- EDIT EMAIL SCHEDULE END -->
    </div>
</main>
<!-- MAIN SECTION END -->
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
$(document).ready(function() {
    // Initialize Select2
    $('.select2').select2({
        theme: 'bootstrap4',
        placeholder: 'Select options...',
        allowClear: true
    });

    // Initialize recipients from existing data
    let recipientsTo = <?php echo json_encode($schedule->recipients_to ?? [], 15, 512) ?>;
    let recipientsCc = <?php echo json_encode($schedule->recipients_cc ?? [], 15, 512) ?>;

    // Initialize recipient tags display
    updateRecipientTags('to');
    updateRecipientTags('cc');

    // Add recipient TO
    $('#add_recipient_to').click(function() {
        const recipient = $('#recipients_to_input').val().trim();
        if (recipient && !recipientsTo.includes(recipient)) {
            recipientsTo.push(recipient);
            updateRecipientTags('to');
            $('#recipients_to_input').val('');
        }
    });

    // Add recipient CC
    $('#add_recipient_cc').click(function() {
        const recipient = $('#recipients_cc_input').val().trim();
        if (recipient && !recipientsCc.includes(recipient)) {
            recipientsCc.push(recipient);
            updateRecipientTags('cc');
            $('#recipients_cc_input').val('');
        }
    });

    // Update recipient tags display
    function updateRecipientTags(type) {
        const recipients = type === 'to' ? recipientsTo : recipientsCc;
        const container = type === 'to' ? '#recipients_to_tags' : '#recipients_cc_tags';
        const hiddenInput = type === 'to' ? '#recipients_to_hidden' : '#recipients_cc_hidden';
        
        let html = '';
        recipients.forEach((recipient, index) => {
            html += `<span class="recipient-tag">
                ${recipient}
                <span class="remove-tag" data-type="${type}" data-index="${index}">&times;</span>
            </span>`;
        });
        
        $(container).html(html);
        $(hiddenInput).val(JSON.stringify(recipients));
    }

    // Remove recipient tag
    $(document).on('click', '.remove-tag', function() {
        const type = $(this).data('type');
        const index = $(this).data('index');
        
        if (type === 'to') {
            recipientsTo.splice(index, 1);
            updateRecipientTags('to');
        } else {
            recipientsCc.splice(index, 1);
            updateRecipientTags('cc');
        }
    });

    // Allow Enter key to add recipients
    $('#recipients_to_input').keypress(function(e) {
        if (e.which === 13) {
            e.preventDefault();
            $('#add_recipient_to').click();
        }
    });

    $('#recipients_cc_input').keypress(function(e) {
        if (e.which === 13) {
            e.preventDefault();
            $('#add_recipient_cc').click();
        }
    });

    // Update email schedule
    $('#update_email_schedule').click(function() {
        const formData = new FormData($('#edit_email_schedule_form')[0]);
        
        // Add recipients arrays to form data
        formData.set('recipients_to', JSON.stringify(recipientsTo));
        formData.set('recipients_cc', JSON.stringify(recipientsCc));

        $.ajax({
            url: '<?php echo e(url("admin/k12connections/manage-email-schedules/" . $schedule->id)); ?>',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    alertify.success(response.message);
                    if (response.redirect) {
                        window.location.href = response.redirect;
                    }
                } else {
                    alertify.error(response.message);
                    if (response.errors) {
                        displayValidationErrors(response.errors);
                    }
                }
            },
            error: function(xhr) {
                alertify.error('An error occurred while updating the schedule');
                if (xhr.responseJSON && xhr.responseJSON.errors) {
                    displayValidationErrors(xhr.responseJSON.errors);
                }
            }
        });
    });

    // Display validation errors
    function displayValidationErrors(errors) {
        $('.err').text('');
        $.each(errors, function(field, messages) {
            $('#' + field + '_error').text(messages[0]);
        });
    }
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\whizara\whizara\resources\views/admin/marketplace/email-schedules/edit.blade.php ENDPATH**/ ?>