@extends('admin.layouts.master')

@section('title')Manage School Roles | Whizara @endsection

@section('content')
<div class="container">
    <h2>Manage Role: {{ $role->name }}</h2>
    <form method="POST" action="{{ route('admin.update-school-roles', $role->id) }}">
        @csrf

        <!-- Role Info -->
        <div class="mb-3">
            <label for="name" class="form-label">Role Name</label>
            <input type="text" name="name" value="{{ $role->name }}" class="form-control" />
        </div>
        
        <div class="mb-3">
            <label for="description" class="form-label">Role Description</label>
            <textarea name="description" class="form-control">{{ $role->description }}</textarea>
        </div>

        <!-- Actions Checkboxes -->
        <h4>Assign Permissions</h4>
        <div class="row">
            @foreach($allActions as $action)
                <div class="col-md-3 mb-2">
                    <div class="form-check">
                        <input 
                            type="checkbox" 
                            name="actions[]" 
                            value="{{ $action->id }}" 
                            class="form-check-input"
                            id="action_{{ $action->id }}"
                            {{ $role->actions->contains($action->id) ? 'checked' : '' }}
                        >
                        <label class="form-check-label" for="action_{{ $action->id }}">
                            {{ $action->description }}
                        </label>
                    </div>
                </div>
            @endforeach
        </div>

        <!-- Submit -->
        <button type="submit" class="btn btn-success mt-3">Save Changes</button>
        <a href="{{ route('admin.list-school-roles') }}" class="btn btn-secondary mt-3">Cancel</a>
    </form>
</div>
@endsection