<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\v1\EmailLayout;
use App\Models\v1\EmailTemplate;
use Illuminate\Support\Facades\Auth;

class ManageEmailController extends Controller
{
    /**
     * Show index with tabs for layouts & templates
     */
    public function index()
    {
        $layouts = EmailLayout::with('createdBy', 'updatedBy')->get();
        $templates = EmailTemplate::with('createdBy', 'updatedBy')->get();
        return view('admin.marketplace.email-templates.index', compact('layouts', 'templates'));
    }

    /**
     * Edit layout form
     */
    public function editLayout($id)
    {
        $layout = EmailLayout::findOrFail($id);
        return view('admin.marketplace.email-templates.edit-layout', compact('layout'));
    }

    /**
     * Update layout
     */
    public function updateLayout(Request $request, $id)
    {
        $layout = EmailLayout::findOrFail($id);

        $request->validate([
            'title' => 'required|string|max:255',
            'slug' => 'required|string|max:255|unique:email_layouts,slug,' . $layout->id,
            'header' => 'nullable|string',
            'footer' => 'nullable|string',
            'tags[]' => 'nullable'
        ]);

        $layout->update([
            'title' => $request->title,
            'slug' => $request->slug,
            'header' => $request->header,
            'footer' => $request->footer,
            'tags' => $request->tags,
            'updated_by' => Auth::id(),
        ]);

        return redirect()->route('admin.email-template.index')->with('success', 'Layout updated successfully');
    }

    /**
     * Edit template form
     */
    public function editTemplate($id)
    {
        $template = EmailTemplate::findOrFail($id);
        $layouts = EmailLayout::all();

        return view('admin.marketplace.email-templates.edit-template', compact('template', 'layouts'));
    }

    /**
     * Update template
     */
    public function updateTemplate(Request $request, $id)
    {
        $template = EmailTemplate::findOrFail($id);

        $request->validate([
            'title' => 'required|string|max:255',
            'slug' => 'required|string|max:255|unique:email_templates,slug,' . $template->id,
            'subject' => 'required|string|max:255',
            'content' => 'required|string',
            'layout_id' => 'nullable|exists:email_layouts,id',
            'tags[]' => 'nullable'
        ]);

        $template->update([
            'title' => $request->title,
            'slug' => $request->slug,
            'subject' => $request->subject,
            'content' => $request->content,
            'layout_id' => $request->layout_id,
            'tags' => $request->tags,
            'updated_by' => Auth::id(),
        ]);

        return redirect()->route('admin.email-template.index')->with('success', 'Template updated successfully');
    }
}
