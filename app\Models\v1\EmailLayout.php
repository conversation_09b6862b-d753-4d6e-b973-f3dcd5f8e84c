<?php

namespace App\Models\v1;

use App\User;
use Illuminate\Database\Eloquent\Model;

class EmailLayout extends Model
{
    protected $fillable = [
        'title', 'slug', 'header_html', 'footer_html', 'tags', 'created_by', 'updated_by'
    ];

    protected $casts = [
        'tags' => 'array',
    ];

    public function templates()
    {
        return $this->hasMany(EmailTemplate::class, 'layout_id');
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updatedBy()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }
}
