<?php

namespace App\V2\Core\Services;

use App\Models\v1\Otp;
use Carbon\Carbon;

class OtpService
{
    /**
     * Generate a new OTP
     */
    public function generate(string $userId, string $userType = 'school'): Otp
    {
        return Otp::create([
            'user_id'   => $userId,
            'user_type' => $userType,
        ]);
    }

    /**
     * Get latest valid OTP
     */
    public function getValidOtp(string $userId, string $userType = 'school'): ?Otp
    {
        return Otp::where('user_id', $userId)
            ->where('user_type', $userType)
            ->where('expires_at', '>', Carbon::now())
            ->latest()
            ->first();
    }

    /**
     * Resend: reuse OTP but extend expiry, else generate new
     */
    public function resend(string $userId, string $userType = 'school'): Otp
    {
        $otp = $this->getValidOtp($userId, $userType);

        if ($otp) {
            // Extend expiry time (refresh validity)
            $otp->expires_at = Carbon::now()->addMinutes(10);
            $otp->save();

            return $otp;
        }

        return $this->generate($userId, $userType);
    }

    /**
     * Verify OTP
     */
    public function verify(string $userId, string $otpCode, string $userType = 'school'): bool
    {
        $otp = Otp::where('user_id', $userId)
            ->where('user_type', $userType)
            ->where('otp_code', $otpCode)
            ->latest()
            ->first();

        if ($otp && !$otp->isExpired()) {
            // delete after success to make OTP single-use
            $otp->delete();
            return true;
        }

        return false;
    }

    /**
     * Invalidate all active OTPs for a user
     */
    public function invalidateAll(string $userId, string $userType = 'school'): int
    {
        return Otp::where('user_id', $userId)
            ->where('user_type', $userType)
            ->delete();
    }

    /**
     * Delete all expired OTPs
     */
    public function cleanup(): int
    {
        return Otp::where('expires_at', '<', Carbon::now())->delete();
    }


    /**
     * Count active OTPs
     */
    public function countActive(string $userId, string $userType = 'school'): int
    {
        return Otp::where('user_id', $userId)
            ->where('user_type', $userType)
            ->where('expires_at', '>', Carbon::now())
            ->count();
    }

    /**
     * Force regenerate
     */
    public function regenerate(string $userId, string $userType = 'school'): Otp
    {
        $this->invalidateAll($userId, $userType);
        return $this->generate($userId, $userType);
    }
}
