<?php
namespace App\V2\Core\Helpers;

use Illuminate\Http\JsonResponse;

class ApiResponse
{
    public static function success($data = [], string $message = "Success", int $code = 200): JsonResponse
    {
        return response()->json([
            "success" => true,
            "message" => $message,
            "data"    => $data
        ], $code);
    }

    public static function error(string $message = "Error", int $code = 500, $errors = []): JsonResponse
    {
        return response()->json([
            "success" => false,
            "message" => $message,
            "errors"  => $errors
        ], $code);
    }
}
