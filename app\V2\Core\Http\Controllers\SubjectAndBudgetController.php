<?php
namespace App\V2\Core\Http\Controllers;

use App\Models\v1\{Subject, SubjectArea};
use App\V2\Core\Helpers\ApiResponse;
use App\Http\Controllers\Controller;

class SubjectAndBudgetController extends Controller
{
    public function getAllSubjects() {
        try {
            $subjects = SubjectArea::with('subjects')->get();

            if ($subjects->isEmpty()) {
                return ApiResponse::error("No subjects found.", 404);
            }

            return ApiResponse::success($subjects, "Subjects retrieved successfully.");
        } catch (\Exception $e) {

            return ApiResponse::error("Something went wrong.", 500, 
                config('app.debug') ? [$e->getMessage()] : []
            );
        }
    }
}