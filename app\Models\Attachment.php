<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Attachment extends Model
{
    use SoftDeletes;

    protected $table = 'attachments';

    protected $fillable = [
        'file_url',
        'file_name',
        'mime_type',
        'file_size',
    ];

    /**
     * Messages that use this attachment
     */
    public function messages(): BelongsToMany
    {
        return $this->belongsToMany(Chat::class, 'message_attachments', 'attachment_id', 'message_id');
    }
}


