<style>
    h2.text-lg.font-semibold {
        font-size: 18px;
        font-weight: 500;
    }

    /* table tr td label {
    font-size: 12px;
} */
    .main_left_slide_tblst tr td,
    .main_left_slide_tblon tr td {
        padding-top: 18px;
    }

    table.main_left_slide_tblst tr td:first-child {
        width: 19.5%;
    }

    table.main_left_slide_tblon tr td:first-child {
        width: 36%;
    }

    table.main_left_slide_tblst tbody tr:nth-of-type(even) {
        background-color: white !important;
    }

    table.main_left_slide_tblon tbody tr:nth-of-type(even) {
        background-color: white !important;
    }

    table tr td .flex input {
        border: none !important;
    text-decoration: underline;
    text-underline-offset: 4px;
    }

    table.main_left_slide_tblon {
        width: 100%;
    }

    table.main_left_slide_tblon tr td:first-child {
        width: 35%;
    }

    table.main_left_slide_tblst tr td:first-child {
        width: 40%;
    }

    textarea.w-full.border.border-gray-300.rounded-lg.p-2 {
        font-size: 13px;
        color: black !important;
    }

    .left_slide_inpts {
        width: 27%;
    }

    table.main_left_slide_tblst {
        width: 86% !important;
    }

    select.left_slide_inpts {
        border: none !important;
    }

    a.hir_contct_dtls_dwnld svg {
        width: 13px;
        margin-left: 6px;
    }

    a.hir_contct_dtls_dwnld {
        color: #004CBD;
        font-size: 14px;
        border: 1.5px solid #004CBD;
        border-radius: 50px;
        padding: 10px;
    }

    /* accordiant----------------- */
    table.acrdnt_tbl {
        text-align: center;
        font-size: 12px;
    }

    table.acrdnt_tbl tr:nth-child(even) {
        background-color: #f2f2f2 !important;
    }

    table.acrdnt_tbl tr th,
    td {
        padding: 10px;
    }

    table.acrdnt_tbl,
    th {
        border: 1.5px solid #C7C8C9;
    }

    .accordion-button:focus-visible {
        outline: none;
        background: white;
        color: black;
    }

    .accordion-button:focus {
        box-shadow: none;
    }

    p.hir_ttl_btn {
        color: #004CBD;
    border: 1.5px solid #004CBD;
    display: inline;
    padding: 5px 20px;
    border-radius: 50px;
    color: #004CBD !important;
    display: table;
    }

    .acrdnt_tbl {
        border-radius: 10px !important;
    }

    /* botton content ---------------- */
    .contact_dtilsd_btm ul li {
        display: block;
        margin: 10px;
    }

    ul.btm-contc_pint li span {
        margin-left: 25px;
        color: #FF7F00;
        border: 1px solid #FF7F00;
        border-radius: 50px;
        padding: 1px 10px;
    }

    .accordion-button:not(.collapsed) {
        background-color: white;
    }

    button.accordion-button {
        color: black !important;
    }
    .accordion{
        margin-top: 60px;
    }

    @media (max-width: 600px) {
        a.hir_contct_dtls_dwnld svg {
            width: 10px;
        }

        a.hir_contct_dtls_dwnld {
            color: #004CBD;
            border: 1.5px solid #004CBD;
            border-radius: 50px;
            padding: 10px;
            font-size: 9px;
        }

        table.main_left_slide_tblon tr td:first-child {
            width: 30%;
        }

        table.acrdnt_tbl th {
            font-size: 12px;
        }

        p.hir_ttl_btn {
            color: #004CBD;
            border: 1.5px solid #004CBD;
            display: inline;
            padding: 5px 8px;
            font-size: 14px;
            border-radius: 50px;
        }

        table.main_left_slide_tblst tr td:first-child {
            width: 27%;
        }

        .left_slide_inpts {
            width: 50%;
        }

        table.main_left_slide_tblst {
            width: 100% !important;
        }

    }
    .left_slide_inpts {
  -webkit-appearance: none;  /* For Safari & Chrome */
  -moz-appearance: none;     /* For Firefox */
  appearance: none;          /* Standard */
  background: transparent;   /* Remove background icon */
  padding-right: 30px;       /* Add padding to balance the space */
}

/* Hide dropdown arrow in Edge/IE */
.left_slide_inpts::-ms-expand {
  display: none;
}

/* Contract Information, Content, and Versions Styling */
.contract-info-section {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
}

.contract-content-section {
    background-color: #f8f9fa;
    padding: 20px;
    border: 1px solid #dee2e6;
    border-radius: 8px;
}

.contract-versions-section .version-item {
    background-color: #ffffff;
    border: 1px solid #dee2e6;
    transition: box-shadow 0.2s ease;
}

.contract-versions-section .version-item:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.no-versions-section {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    text-align: center;
}

.no-contract-data {
    background-color: #f8f9fa;
    border: 2px dashed #dee2e6;
    border-radius: 12px;
    margin: 40px 0;
}

.no-contract-data svg {
    opacity: 0.5;
}

.badge-info {
    background-color: #17a2b8;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
}

</style>
@php
    // Check if there are any records in school_requirement_contracts table for this requirement ID
    $requirementContracts = \App\SchoolRequirementContract::with(['versions'])
        ->where('requirement_id', $data->id)
        ->get();
    $hasContractData = $requirementContracts->count() > 0;
@endphp

<div class="row">
    <div class="col-lg-8">
        @if($hasContractData)
            {{-- Contract Information Section --}}
            <h3 class="fw-bold mb-4 text-dark">
                Contract Information
            </h3>
            @foreach($requirementContracts as $contract)
                <div class="contract-info-section mb-4 p-3 border rounded">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>Legal Name:</strong> {{ $contract->legal_first_name }} {{ $contract->legal_last_name }}</p>
                            <p><strong>Email:</strong> {{ $contract->email }}</p>
                            <p><strong>Phone:</strong> {{ $contract->phone }}</p>
                            <p><strong>Job Title:</strong> {{ $contract->job_title }}</p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>Client Name:</strong> {{ $contract->client_name }}</p>
                            <p><strong>Address:</strong> {{ $contract->address }}</p>
                            <p><strong>Status:</strong> <span class="badge badge-info">{{ ucfirst($contract->status) }}</span></p>
                            @if($contract->has_purchase_order)
                                <p><strong>Purchase Order:</strong> {{ $contract->purchase_order_ref }}</p>
                            @endif
                        </div>
                    </div>
                </div>
            @endforeach

            {{-- Contract Content Section --}}
            <h3 class="fw-bold mb-4 text-dark">
                Contract Content
            </h3>
            <div class="contract-content-section mb-4">
                <p>Contract content details for requirement: <strong>{{ $data->requirement_name }}</strong></p>
                <p>Start Date: {{ date('m/d/Y', strtotime($data->start_date)) }}</p>
                <p>End Date: {{ date('m/d/Y', strtotime($data->end_date)) }}</p>
                <p>Total Hours: {{ $data->totalHours ?? 'N/A' }}</p>
            </div>

            {{-- Contract Versions Section --}}
            <h3 class="fw-bold mb-4 text-dark">
                Contract Versions
            </h3>
            @if($requirementContracts->flatMap->versions->count() > 0)
                <div class="contract-versions-section mb-4">
                    @foreach($requirementContracts as $contract)
                        @foreach($contract->versions as $version)
                            <div class="version-item p-3 border rounded mb-2">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <strong>Version {{ $version->version_number }}</strong>
                                        <p class="mb-1 text-muted">Created: {{ $version->created_at->format('m/d/Y H:i') }}</p>
                                        @if($version->notes)
                                            <p class="mb-0"><em>{{ $version->notes }}</em></p>
                                        @endif
                                    </div>
                                    <div>
                                        @if($version->file_url)
                                            <a href="{{ generateSignedUrl($version->file_url) }}" download class="hir_contct_dtls_dwnld">
                                                Download {{ $version->version_number }}
                                                <svg xmlns="http://www.w3.org/2000/svg" width="13px" viewBox="0 -2 30 30" fill="#004CBD">
                                                    <path d="M132,728 L104,728 C103.521,728 103,728.521 103,729 L103,736 C103,737.104 103.896,738 105,738 C106.104,738 107,737.104 107,736 L107,732 L129,732 L129,736 C129,737.104 129.896,738 131,738 C132.104,738 133,737.104 133,736 L133,729 C133,728.458 132.604,728 132,728 L132,728 Z M122,746 L120,746 L120,737 C120,735.896 119.104,735 118,735 C116.896,735 116,735.896 116,737 L116,746 L114,746 C113.608,746 112.705,745.905 112.313,746.3 C111.921,746.693 111.921,747.332 112.313,747.726 L117.255,753.717 C117.464,753.927 117.742,754.017 118.016,754.002 C118.29,754.017 118.567,753.927 118.776,753.717 L123.718,747.726 C124.11,747.332 124.11,746.693 123.718,746.3 C123.326,745.905 122.705,746 122,746 L122,746 Z"/>
                                                </svg>
                                            </a>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    @endforeach
                </div>
            @else
                <div class="no-versions-section p-3 border rounded mb-4">
                    <p class="text-muted mb-0">No contract versions available.</p>
                </div>
            @endif
        @else
            {{-- No Data Found Section --}}
            <div class="no-contract-data text-center py-5">
                <div class="mb-4">
                    <svg width="64" height="64" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" fill="#6c757d"/>
                    </svg>
                </div>
                <h4 class="text-muted mb-2">No Contract Data Found</h4>
                <p class="text-muted">No contract information, content, or versions are available for this requirement.</p>
            </div>
        @endif

        {{-- Original Contract Terms Section (from school_instructor_hiring) --}}
        <h3 class="fw-bold mb-4 text-dark">
            Instructor Hiring Contract Terms
        </h3>
        <table style="" class="main_left_slide_tblon">
            <tr class="mb-4 main_left_slide">
                <td><label class="block text-sm font-medium mb-1">
                        Payment Option*
                    </label></td>
                <td>
                    <select class="left_slide_inpts w-full border border-gray-300 rounded-lg p-2" disabled>
                        <option value="hourly" @if ($hire->payment_option == 'hourly') selected @endif>Hourly</option>
                        <option value="fixed" @if ($hire->payment_option == 'fixed') selected @endif>Fixed</option>
                    </select>
                </td>
            </tr>
            <tr class="mb-4 main_left_slide">
                <td><label class="block text-sm font-medium mb-1">
                        Pay/hour*
                    </label></td>
                <td>
                    <div class="flex items-center relative">
                        <input class="left_slide_inpts w-full border border-gray-300 rounded-lg p-2" type="text"
                            value="${{ $hire->pay_hour }}/Hour" readonly>
                    </div>
                </td>
            </tr>
            <tr class="mb-4 main_left_slide">
                <td><label class="block text-sm font-medium mb-1">
                        Total contract cost
                    </label></td>
                <td>
                    <div class="flex items-center">
                        <input class="left_slide_inpts w-full border border-gray-300 rounded-lg p-2" type="text"
                            value="${{ $hire->total_contract_cost }}" readonly>
                    </div>
                </td>
            </tr>
        </table>
        <table style="" class="main_left_slide_tblst">
            <tr class="mb-4 main_left_slide">
                <td><label class="block text-sm font-medium mb-1">
                        Additional Contract Terms
                    </label></td>
                <td>
                    <textarea class="w-full border border-gray-300 rounded-lg p-2" name="additional_contract_terms"
                        placeholder="Enter any additional contract details you would like the educator to be aware about." rows="4">{{ $hire->additional_contract_terms }}</textarea>
                </td>
            </tr>
            <tr class="mb-4 main_left_slide">
                <td></td>
                <td>
                    <a data-bs-toggle="tooltip" data-bs-placement="bottom" title="Contract Terms" href="{{ generateSignedUrl($hire->contract_file) }}" download
                        class="hir_contct_dtls_dwnld">Contract Terms.pdf <svg
                            xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
                            xmlns:sketch="http://www.bohemiancoding.com/sketch/ns" width="20px" viewBox="0 -2 30 30"
                            version="1.1" fill="#004CBD">

                            <g id="SVGRepo_bgCarrier" stroke-width="0" />

                            <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round" />

                            <g id="SVGRepo_iconCarrier">
                                <title>download</title>
                                <desc>Created with Sketch Beta.</desc>
                                <defs> </defs>
                                <g id="Page-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"
                                    sketch:type="MSPage">
                                    <g id="Icon-Set-Filled" sketch:type="MSLayerGroup"
                                        transform="translate(-103.000000, -728.000000)" fill="#004CBD">
                                        <path
                                            d="M132,728 L104,728 C103.521,728 103,728.521 103,729 L103,736 C103,737.104 103.896,738 105,738 C106.104,738 107,737.104 107,736 L107,732 L129,732 L129,736 C129,737.104 129.896,738 131,738 C132.104,738 133,737.104 133,736 L133,729 C133,728.458 132.604,728 132,728 L132,728 Z M122,746 L120,746 L120,737 C120,735.896 119.104,735 118,735 C116.896,735 116,735.896 116,737 L116,746 L114,746 C113.608,746 112.705,745.905 112.313,746.3 C111.921,746.693 111.921,747.332 112.313,747.726 L117.255,753.717 C117.464,753.927 117.742,754.017 118.016,754.002 C118.29,754.017 118.567,753.927 118.776,753.717 L123.718,747.726 C124.11,747.332 124.11,746.693 123.718,746.3 C123.326,745.905 122.705,746 122,746 L122,746 Z"
                                            id="download" sketch:type="MSShapeGroup"> </path>
                                    </g>
                                </g>
                            </g>

                        </svg>
                    </a>
                </td>
            </tr>
        </table>

        <div class="accordion" id="accordionExample">
            <div class="accordion-item">
                <h2 class="accordion-header" id="headingOne">
                    <button class="accordion-button hire_accordion" type="button" data-bs-toggle="collapse"
                        data-bs-target="#collapseOne" aria-expanded="true" aria-controls="collapseOne">
                        How do hourly contracts work?
                    </button>
                </h2>
                <div id="collapseOne" class="accordion-collapse collapse show" aria-labelledby="headingOne"
                    data-bs-parent="#accordionExample">
                    <div class="accordion-body">
                        The educator hours are tracked based on the work done. Once you start an hourly contract, the
                        system
                        records the hours worked, and payments are processed weekly. Every Firday, you’ll be invoiced
                        for
                        the previous week’s hours, and your default billing method will be charged automatically. If
                        there’s
                        an issue, you have until next week’s Friday to raise a dispute.
                    </div>
                </div>
            </div>
            <div class="accordion-item">
                <h2 class="accordion-header" id="headingTwo">
                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                        data-bs-target="#collapseTwo" aria-expanded="false" aria-controls="collapseTwo">
                        How you will be charged?
                    </button>
                </h2>
                <div id="collapseTwo" class="accordion-collapse collapse" aria-labelledby="headingTwo"
                    data-bs-parent="#accordionExample">
                    <div class="accordion-body">
                        <p>Whizara's cost is computed based on the below data.</p>
                        <div class="acrdnt_tbl_hir">
                            <table class="acrdnt_tbl">
                                <thead>
                                    <tr>
                                        <th>Total number of hours
                                            of class scheduled</th>
                                        <th>Class management
                                            license fee</th>
                                        <th>Whizara class management license fee</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>>= 135</td>
                                        <td>$1,800 or 10% of educator fee,
                                            whichever is more</td>
                                        <td>$1,800</td>
                                    </tr>
                                    <tr>
                                        <td>67.5-134.99</td>
                                        <td>$1,350 or 13.50% of educator fee,
                                            whichever is more</td>
                                        <td>$1,350</td>
                                    </tr>
                                    <tr>
                                        <td>45-67.49</td>
                                        <td>$1,200 or 16% of educator fee,
                                            whichever is more</td>
                                        <td>$1,200</td>
                                    </tr>
                                    <tr>
                                        <td>33.75-44.99</td>
                                        <td>$1,125 or 19.50% of educator fee,
                                            whichever is more</td>
                                        <td>$1,125</td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <33.75 </td>
                                        <td>$1,000 or 25% of educator fee,
                                            whichever is more</td>
                                        <td>$1,000</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="hir_amt_total mt-4">
                            @php
                                $totalHours = $data->totalHours;
                                if ($totalHours >= 135) {
                                    $totalCost = '188';
                                } elseif ($totalHours >= 67.5 && $totalHours < 135) {
                                    $totalCost = '1,350';
                                } elseif ($totalHours >= 45 && $totalHours < 67.5) {
                                    $totalCost = '1,200';
                                } elseif ($totalHours >= 33.75 && $totalHours < 45) {
                                    $totalCost = '1,125';
                                } else {
                                    $totalCost = '1,000';
                                }
                            @endphp
                            <p class="hir_ttl_btn">Total cost to be paid to Whizara: ${{ $totalCost }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="contact_dtilsd_btm">
            <h3 class="fw-bold text-dark mt-5">
                Contract History
            </h3>
            <ul class="btm-contc_pint">
                {{-- <li><strong>Contract Start Date:</strong> {{ date('d/m/Y', strtotime($hire->created_at)) }}</li> --}}
                <li><strong>Contract Sent Date:</strong> {{ date('m/d/Y', strtotime($hire->created_at)) }}</li>
                <li><strong>Contract Signed Date:</strong> {{ $hire->status == 'accepted' ? date('m/d/Y', strtotime($hire->updated_at)) : '' }}</li>
                <li><strong>Contract Status:</strong> <span>{{ ucfirst($hire->status) }}</span></li>
                @if ($hire->status == 'accepted')
                    <li><strong>Accepted Date:</strong> {{ date('m/d/Y', strtotime($hire->updated_at)) }}</li>
                    <li><strong>Start Date:</strong> {{ date('m/d/Y', strtotime($hire->requirements->start_date)) }}</li>
                    <li><strong>End Date:</strong> {{ date('m/d/Y', strtotime($hire->requirements->end_date)) }}</li>
                @endif
            </ul>
        </div>
    </div>

    <div class="col-lg-4 profile-section">
        <div class="card profile-sct_crd">
            <div class="image-container">
                <img data-bs-toggle="tooltip" data-bs-placement="top" title="{{ $user->first_name . ' ' . $user->last_name }}" src="{{ !empty($user->step5) ? generateSignedUrl($user->step5->profile_image) : url('website/img/pre.png') }}"
                    alt="image" style="outline: 2px solid #004CBD;">
            </div>
            <div class="basic-details">
                <p class="mb-2 fw-500 text-dark fs-18">{{ $user->first_name . ' ' . $user->last_name }}</p>
                <p class="mb-2 fs-13 fw-500 text-dark" style="font-size: 0.9rem">
                    <span>
                        {{ $user->city . ', ' . $user->state }}
                    </span>
                    <span class="mb-1 text-dark fw-normal" style="text-wrap-mode: nowrap;">(Pacific Time (PT))</span>
                </p>
                <div class="basic-details_btns" onclick="loadUser('{{encrypt_str($user->id)}}', '{{encrypt_str($data->id)}}', '{{encrypt_str($applicant->id)}}')">
                    <a class="btn btn1 profile_crd_icons">View Profile
                        <svg xmlns="http://www.w3.org/2000/svg"
                            width="20px" viewBox="0 0 24 24" fill="none">

                            <g id="SVGRepo_bgCarrier" stroke-width="0" />

                            <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round" />

                            <g id="SVGRepo_iconCarrier">
                                <path
                                    d="M9.75 12C9.75 10.7574 10.7574 9.75 12 9.75C13.2426 9.75 14.25 10.7574 14.25 12C14.25 13.2426 13.2426 14.25 12 14.25C10.7574 14.25 9.75 13.2426 9.75 12Z"
                                    fill="#ffffff" />
                                <path fill-rule="evenodd" clip-rule="evenodd"
                                    d="M2 12C2 13.6394 2.42496 14.1915 3.27489 15.2957C4.97196 17.5004 7.81811 20 12 20C16.1819 20 19.028 17.5004 20.7251 15.2957C21.575 14.1915 22 13.6394 22 12C22 10.3606 21.575 9.80853 20.7251 8.70433C19.028 6.49956 16.1819 4 12 4C7.81811 4 4.97196 6.49956 3.27489 8.70433C2.42496 9.80853 2 10.3606 2 12ZM12 8.25C9.92893 8.25 8.25 9.92893 8.25 12C8.25 14.0711 9.92893 15.75 12 15.75C14.0711 15.75 15.75 14.0711 15.75 12C15.75 9.92893 14.0711 8.25 12 8.25Z"
                                    fill="#ffffff" />
                            </g>

                        </svg>
                    </a>
                    {{-- <a class="btn btn2 profile_crd_icons">Edit Contract <svg xmlns="http://www.w3.org/2000/svg"
                            width="20px" viewBox="0 0 24 24" fill="none">

                            <g id="SVGRepo_bgCarrier" stroke-width="0" />

                            <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round" />

                            <g id="SVGRepo_iconCarrier">

                                <path fill-rule="evenodd" clip-rule="evenodd"
                                    d="m3.99 16.854-1.314 3.504a.75.75 0 0 0 .966.965l3.503-1.314a3 3 0 0 0 1.068-.687L18.36 9.175s-.354-1.061-1.414-2.122c-1.06-1.06-2.122-1.414-2.122-1.414L4.677 15.786a3 3 0 0 0-.687 1.068zm12.249-12.63 1.383-1.383c.248-.248.579-.406.925-.348.487.08 1.232.322 1.934 1.025.703.703.945 1.447 1.025 1.934.058.346-.1.677-.348.925L19.774 7.76s-.353-1.06-1.414-2.12c-1.06-1.062-2.121-1.415-2.121-1.415z"
                                    fill="#787777" />

                            </g>

                        </svg>
                    </a> --}}
                    {{-- <a class="btn btn3 profile_crd_icons">Pause Contract <svg xmlns="http://www.w3.org/2000/svg"
                            width="18px" viewBox="0 0 16 16" fill="none">

                            <g id="SVGRepo_bgCarrier" stroke-width="0" />

                            <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round" />

                            <g id="SVGRepo_iconCarrier">
                                <path d="M7 1H2V15H7V1Z" fill="#D7A202" />
                                <path d="M14 1H9V15H14V1Z" fill="#D7A202" />
                            </g>

                        </svg>
                    </a> --}}
                    @if ($hire->status == 'pending')
                        <a class="btn btn4 profile_crd_icons" onclick="withdrawOffer('{{encrypt_str($hire->id)}}', '{{$encryptedId}}')">Withdraw Contract
                            <svg xmlns="http://www.w3.org/2000/svg"
                                fill="#CF1313" width="20px" viewBox="0 0 32 32" version="1.1">

                                <g id="SVGRepo_bgCarrier" stroke-width="0" />

                                <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round" />

                                <g id="SVGRepo_iconCarrier">
                                    <title>minus-round</title>
                                    <path
                                        d="M0 16q0 3.264 1.28 6.208t3.392 5.12 5.12 3.424 6.208 1.248 6.208-1.248 5.12-3.424 3.392-5.12 1.28-6.208-1.28-6.208-3.392-5.12-5.088-3.392-6.24-1.28q-3.264 0-6.208 1.28t-5.12 3.392-3.392 5.12-1.28 6.208zM4 16q0-3.264 1.6-6.016t4.384-4.352 6.016-1.632 6.016 1.632 4.384 4.352 1.6 6.016-1.6 6.048-4.384 4.352-6.016 1.6-6.016-1.6-4.384-4.352-1.6-6.048zM8 16q0 0.832 0.576 1.44t1.44 0.576h12q0.8 0 1.408-0.576t0.576-1.44-0.576-1.408-1.408-0.576h-12q-0.832 0-1.44 0.576t-0.576 1.408z" />
                                </g>

                            </svg>
                        </a>
                    @endif
                    {{-- @if ($hire->status == 'pending')
                        <a class="btn btn4 profile_crd_icons" onclick="withdrawOffer('{{encrypt_str($hire->id)}}')">End Contract
                            <svg xmlns="http://www.w3.org/2000/svg"
                                fill="#CF1313" width="20px" viewBox="0 0 32 32" version="1.1">

                                <g id="SVGRepo_bgCarrier" stroke-width="0" />

                                <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round" />

                                <g id="SVGRepo_iconCarrier">
                                    <title>minus-round</title>
                                    <path
                                        d="M0 16q0 3.264 1.28 6.208t3.392 5.12 5.12 3.424 6.208 1.248 6.208-1.248 5.12-3.424 3.392-5.12 1.28-6.208-1.28-6.208-3.392-5.12-5.088-3.392-6.24-1.28q-3.264 0-6.208 1.28t-5.12 3.392-3.392 5.12-1.28 6.208zM4 16q0-3.264 1.6-6.016t4.384-4.352 6.016-1.632 6.016 1.632 4.384 4.352 1.6 6.016-1.6 6.048-4.384 4.352-6.016 1.6-6.016-1.6-4.384-4.352-1.6-6.048zM8 16q0 0.832 0.576 1.44t1.44 0.576h12q0.8 0 1.408-0.576t0.576-1.44-0.576-1.408-1.408-0.576h-12q-0.832 0-1.44 0.576t-0.576 1.408z" />
                                </g>

                            </svg>
                        </a>
                    @endif --}}
                </div>
            </div>
        </div>
    </div>
</div>
