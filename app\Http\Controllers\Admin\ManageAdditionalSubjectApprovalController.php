<?php

namespace App\Http\Controllers\Admin;

use App\EmailTemplate;
use App\Helpers\DataTableHelper;
use App\Http\Controllers\Controller;
use App\Models\InstructorBudgetApprovedModel;
use App\Models\InstructorBudgetLine;
use App\Models\v1\BudgetState;
use App\Models\v1\UserAdditionalSubject;
use App\Models\v1\Subject as V1Subject;
use App\OnboardingInstructor;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class ManageAdditionalSubjectApprovalController extends Controller
{
    // *********Manage-Subject-Approval*********
    public function index(Request $request, $id = 'All')
    {
        if ($request->ajax()) {
            $query = UserAdditionalSubject::with(['instructor']);

            switch ($id) {
                case "All":
                    $query->whereIn("status", ['pending', 'rejected', 'approved']);
                break;

                case "Pending":
                    $query->where("status", "pending");
                break;

                case "Approved":
                    $query->where("status", "approved");
                break;

                case "Rejected":
                    $query->where("status", "rejected");
                break;

                default:
                    $query->whereIn("status", ['pending', 'rejected', 'approved']);
                break;
            }

            $params = DataTableHelper::getParams($request);
            $query->orderBy($params['columnName'], $params['columnSortOrder'] ?? 'desc');
            [$count, $result] = DataTableHelper::applyPagination($query, $params['row'], $params['rowperpage']);

            $result->transform(function ($row) {
                $instructor = $row->instructor;
                $row->full_name = $instructor ? $instructor->first_name . ' ' . $instructor->last_name : '-';
                $row->email = $instructor->email ?? '-';
                $row->proficiency = $row->proficiency ?? '-';
                $row->lesson_planning = $row->file_url ? '<a href="'.$row->file_url.'" class="btn btn-sm btn-outline-primary" download target="_blank"><i class="fa fa-download"></i> Download</a>' : 'No Lesson Plan';
                $row->notes = $row->lesson_plan_note ?? '-';
                $row->status = $row->status ?? '-';

                $id = $row->id;
                $approveUrl = url("admin/k12connections/subject-approval-modal/{$id}");
                $rejectUrl = url("admin/k12connections/reject-subject-modal/{$id}");

                $actionHtml = '<div class="d-flex gap-1">';
                if ($row->status == 'pending') {
                    $approveUrl = route('admin.subject.approval.modal', $row->id);
                    $actionHtml .= '
                        <a href="javascript:void(0)" class="btn btn-outline-success btn-sm btn-approve" title="Approve" data-userid="' . $row->id . '" data-url="' . $approveUrl . '" data-insid="' . $row->instructor->id . '" data-selected-item="Approved">
                            <i class="fa fa-check"></i>
                        </a>
                        <a href="javascript:void(0)" class="btn btn-outline-danger btn-sm btn-reject" title="Reject" data-userid="' . $row->id . '" data-url="' . $rejectUrl . '" data-insid="' . $row->instructor->id . '" data-selected-item="Rejected">
                            <i class="fa fa-times"></i>
                        </a>';
                }
                $actionHtml .= '</div>';

                $row->action = $actionHtml;
                return $row;
            });
            return DataTableHelper::generateResponse($params['draw'], $count, $result);
        }
        return view('admin.marketplace.subject-approval.index');
    }
    // *********Manage-Subject-Approval*********

    // *********Approve-Subject-Model-Open*********
    public function approveSubjectModelOpen($id)
    {
        $case_management = 0;
        $bilingual = 0;
        $sped=0;

        $additionalSubject = UserAdditionalSubject::find($id);
        $user = OnboardingInstructor::with(['step1', 'step2', 'step3'])->where('id', $additionalSubject->user_id)->first();
        $subjects = V1Subject::with('subjectBudget')->where('id', $additionalSubject->subject_code)->first();
        $stateData = BudgetState::where('name', $user->step1->state)->first();
        if ($stateData)
        {
            $case_management = $stateData->case_management;
            $bilingual = $stateData->bilingual_inc;
            $sped = $stateData->sped_rec_comp;
        }

        $matchedEducations = empty($user->step2->education) ? [] : collect($user->step2->education)
            ->filter(function ($edu) {
                return stripos($edu->education ?? '', 'special education') !== false;
            })
            ->mapWithKeys(function ($edu) {
                $stateKeys = json_decode($edu->states, true) ?: [];
                $statesWithPrices = BudgetState::whereIn('name', $stateKeys)
                    ->get()
                    ->map(function ($state) {
                        $price = number_format($state->sped_rec_comp, 2); // Format price if needed
                        return [$state->name, $price];
                    })
                    ->values()
                    ->toArray();
                return [$edu->education => $statesWithPrices];
            })
            ->toArray();

        $languages = array_map('trim', explode(',', $user->step3->language_teach_that_i_teach));
        $hasOtherLanguages = collect($languages)->filter(function ($lang) {
            return strtolower($lang) !== 'english';
        })->isNotEmpty();
        $stateData = BudgetState::where('name', $user->step1->state)->first();
        if ($stateData)
        {
            $case_management = $stateData->case_management;
            if($hasOtherLanguages) {
                $bilingual = $stateData->bilingual_inc ?? 0;
            }
            $sped = empty($matchedEducations) ? 0 : $stateData->sped_rec_comp;
        }

        $view = view("admin.marketplace.subject-approval.activeSubjectModel", ['user' => $user, 'subjects' => $subjects, "case_management" => $case_management, "bilingual" => $bilingual, "specialEducation" => $matchedEducations, 'sped' => $sped])->render();
        return response()->json(['status' => true, 'view' => $view]);
    }
    // *********Approve-Subject-Model-Open*********

    // *********Reject-Model-Open*********
    public function rejectSubjectModelOpen($id)
    {
        $view = view("admin.marketplace.subject-approval.rejectSubjectModel", ['id' => $id])->render();
        return response()->json(['status' => true, 'view' => $view]);
    }
    // *********Reject-Model-Open*********

    // *********Update-Subject-Status*********
    public function updateSubjectStatus(Request $request)
    {
        try{
            if ($request->status == 'Approved') {
                DB::beginTransaction();

                $user = OnboardingInstructor::find($request->userid);
                $userEmail = $user->email;
                $request->validate([
                    'userid' => 'required|exists:new_onboarding_instructor,id',
                    'in_person' => 'required|numeric',
                    'bilingual_inc' => 'required|numeric',
                    'case_management' => 'required|numeric',
                    'budget_lines' => 'required|array',
                    'budget_lines.*.code_title' => 'required|string',
                    'budget_lines.*.base_pay' => 'required|numeric',
                    'budget_lines.*.experience_pay' => 'required|numeric',
                    'budget_lines.*.education_pay' => 'required|numeric',
                    'budget_lines.*.non_teaching' => 'nullable|numeric',
                    'budget_lines.*.special_education' => 'nullable|numeric',
                    'budget_lines.*.total' => 'required|numeric'
                ]);

                // Update or create the main budget approval record
                $approved = InstructorBudgetApprovedModel::updateOrCreate(
                    ['user_id' => $request->userid],
                    [
                        'in_person' => $request->in_person,
                        'bilingual_inc' => $request->bilingual_inc,
                        'case_management' => $request->case_management,
                        'status_updated_at' => now(),
                    ]
                );

                // Then handle each budget line uniquely by subject
                foreach ($request->budget_lines as $line) {
                    $parts = explode(':', $line['code_title']);
                    $subject_code = array_shift($parts);
                    $subject_name = implode(':', $parts);

                    if ($line['selected'] == 'true') {
                        InstructorBudgetLine::updateOrCreate(
                            [
                                'approved_id' => $approved->id,
                                'subject_code' => $subject_code,
                            ],
                            [
                                'subject_title' => $subject_name,
                                'base_pay' => $line['base_pay'],
                                'experience_pay' => $line['experience_pay'],
                                'education_pay' => $line['education_pay'],
                                'non_teaching' => $line['non_teaching'] ?? 0,
                                'special_education' => $line['special_education'] ?? 0,
                                'total' => $line['total'],
                            ]
                        );
                    }
                }

                // Update the status of the additional subject
                UserAdditionalSubject::where('user_id', $request->userid)->update(['status' => 'approved']);
                DB::commit();
                return response()->json(['status' => true, 'message' => 'Subject approved successfully']);
            } elseif ($request->status == 'Rejected') {
                UserAdditionalSubject::where('user_id', $request->userid)->update(['status' => 'rejected', 'reason' => $request->reason]);
                return response()->json(['status' => true, 'message' => 'Subject rejected successfully']);
            } else {
                return response()->json(['status' => false, 'message' => 'Unknown status.']);
            }
        } catch (Exception  $e) {
            DB::rollback();
            return response()->json(['status' => false, 'message' => 'Failed to save instructor budget.', 'error' => $e->getMessage()], 500);
        }
    }
    // *********Update-Subject-Status*********
}
