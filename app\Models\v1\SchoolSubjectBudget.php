<?php

namespace App\Models\v1;

use Illuminate\Database\Eloquent\Model;

class SchoolSubjectBudget extends Model
{
    protected $table = 'school_subject_budget_v2';

    protected $fillable = [
        'school_budget_id',
        'subject_id',
        'base_pay_0_3',
        'pay_3_6',
        'pay_6_10',
        'pay_10_plus',
        'masters_inc',
        'doctorate_inc',
        'non_tech_time',
        'curriculum_inc',
    ];

    protected $casts = [
        'base_pay_0_3' => 'float',
        'pay_3_6' => 'float',
        'pay_6_10' => 'float',
        'pay_10_plus' => 'float',
        'masters_inc' => 'float',
        'doctorate_inc' => 'float',
        'non_tech_time' => 'float',
        'curriculum_inc' => 'float',
    ];

    public function schoolBudget()
    {
        return $this->belongsTo(SchoolBudget::class, 'school_budget_id');
    }

    public function subject()
    {
        return $this->belongsTo(Subject::class, 'subject_id');
    }
}
