@extends('admin.layouts.master')

@section('title')
    Edit Requirements | Whizara
@endsection

@section('content')
<style>
    span.select2.select2-container.select2-container--bootstrap4 {
        width: 100% !important;
    }
    .err {
        color: red;
        font-size: 0.875rem;
    }
</style>
<?php $res = get_permission(session('Adminnewlogin')['type']); ?>
{{-- MAIN SECTION START --}}
<main class="content">
    <div class="container-fluid p-0">
        {{-- BREADCRUMB START --}}
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item " aria-current="page"><a href="{{ url('admin/k12connections/requirements') }}">Requirements List</a></li>
                <li class="breadcrumb-item active" aria-current="page">Edit Requirements</li>
                <li class="breadcrumb-item active" aria-current="page"><a href="{{ url('admin/k12connections/add-requirements') }}">Add Requirement</a></li>
            </ol>
        </nav>
        {{-- BREADCRUMB END --}}

        {{-- ADD REQUIREMENTS START --}}
        <form id="edit_requirements_form" enctype='multipart/form-data'>
            <div class="row justify-content-center">
                <div class="col-lg-12 col-md-9">
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="card">
                                <div class="card-header border-bottom"><h5 class="mb-0">Add Requirement</h5></div>
                                {{ csrf_field() }}
                                <div class="card-body">
                                    <input type="hidden" name="id" value="{{$requirement->id}}">
                                    {{-- Overview-Section --}}
                                    <div><h5 class="mb-3">Overview</h5></div>
                                    <div class="row border-bottom pb-2">
                                        <div class="col-md-6 form-group">
                                            <label for="comment">School</label><span class="text-danger">*</span>
                                            <select class="form-control select2" id="school" name="school">
                                                <option value="">Select School</option>
                                                @if (!empty($schools) && $schools->count())
                                                    @foreach ($schools as $val)
                                                        <option value="{{ $val->id }}" @if($requirement->school_id == $val->id){{'selected'}}  @endif>{{ $val->school_name }}</option>
                                                    @endforeach
                                                @endif
                                            </select>
                                            <span id="school_error" class="err"></span>
                                        </div>
                                        <div class="col-md-6 form-group">
                                            <label for="district_school_id">School ID | District ID</label><span class="text-danger">*</span>
                                            <input type="text" class="form-control" id="district_school_id" name="district_school_id" value="{{$requirement->district_school_id}}" placeholder="Enter School ID | District ID" readonly>
                                            <span id="district_school_id_error" class="err"></span>
                                        </div>
                                        <div class="col-md-6 form-group">
                                            <label for="delivery_mode">Delivery Mode</label><span class="text-danger">*</span>
                                            <select class="form-control" id="delivery_mode" name="delivery_mode">
                                                <option value="">Select Delivery Mode</option>
                                                <option value="online" @if($requirement->delivery_mode == 'online'){{'selected'}}  @endif>Online</option>
                                                <option value="in-person" @if($requirement->delivery_mode == 'in-person'){{'selected'}}  @endif>In-person</option>
                                                <option value="hybrid" @if($requirement->delivery_mode == 'hybrid'){{'selected'}}  @endif>Hybrid</option>
                                            </select>
                                            <span id="delivery_mode_error" class="err"></span>
                                        </div>
                                        <div class="col-md-6 form-group">
                                            <label for="courses">Courses</label><span class="text-danger">*</span>
                                            <select name="courses" id="courses" class="form-control select2">
                                                <option value="">Select Courses</option>
                                                @foreach ($subjectArea as $subjects)
                                                    @if (!empty($subjects->subject_area))
                                                        <optgroup label="{{$subjects->subject_area}}">
                                                            @foreach ($subjects->subjects as $subject)
                                                                @if (!empty($subject->title))
                                                                    @php
                                                                        $modifiedCode = strlen($subject->subject_code) == 4 ? '0' . $subject->subject_code : $subject->subject_code;
                                                                    @endphp
                                                                    <option value="{{$subject->id}}" @if($requirement->subject_id == $subject->id){{'selected'}}  @endif>{{$modifiedCode}} : {{$subject->title}}</option>
                                                                @endif
                                                            @endforeach
                                                        </optgroup>
                                                    @endif
                                                @endforeach
                                            </select>
                                            <span id="courses_error" class="err"></span>
                                        </div>
                                        <div class="col-md-6 form-group">
                                            <label for="class_type">Class Type</label><span class="text-danger">*</span>
                                            <select class="form-control" id="class_type" name="class_type">
                                                <option value="">Select Class Type</option>
                                                @foreach ($program_type as $classType)
                                                    <option value="{{$classType->id}}" @if($requirement->class_type == $classType->id){{'selected'}}  @endif>{{$classType->name}}</option>
                                                @endforeach
                                            </select>
                                            <span id="class_type_error" class="err"></span>
                                        </div>
                                        <div class="col-md-6 form-group">
                                            <label for="gradeLevels">Grade Levels</label><span class="text-danger">*</span>
                                            <select class="form-control select2" id="gradeLevels" name="gradeLevels[]" multiple>
                                                <option value="">Select Grade Levels</option>
                                                @foreach ($grades as $grade)
                                                    <option value="{{$grade->id}}" @if(in_array($grade->id, explode(',', $requirement->grade_levels_id))){{'selected'}}  @endif>{{$grade->class_name}}</option>
                                                @endforeach
                                            </select>
                                            <span id="gradeLevels_error" class="err"></span>
                                        </div>
                                        <div class="col-md-3 form-group">
                                            <label for="expected_class_size">Expected Class Size</label><span class="text-danger">*</span>
                                            <input type="number" class="form-control" id="expected_class_size" name="expected_class_size" value="{{$requirement->capacity}}" placeholder="Enter Expected Class Size">
                                            <span id="expected_class_size_error" class="err"></span>
                                        </div>
                                        <div class="col-md-3 form-group">
                                            <label for="cbo">CBO</label><span class="text-danger">*</span>
                                            <select class="form-control select2" id="cbo" name="cbo">
                                                <option value="">Select CBO</option>
                                                @foreach ($cbos as $id => $name)
                                                    <option value="{{ $id }}" @if($requirement->cbo_id == $id){{'selected'}}  @endif>{{ $name }}</option>
                                                @endforeach
                                            </select>
                                            <span id="cbo_error" class="err"></span>
                                        </div>
                                        <div class="col-md-3 form-group">
                                            <label for="status">Status</label><span class="text-danger">*</span>
                                            <select class="form-control" id="status" name="status">
                                                <option value="">Select Status</option>
                                                <option value="draft" @if($requirement->status == 'draft'){{'selected'}}  @endif>Draft</option>
                                                <option value="open" @if($requirement->status == 'open'){{'selected'}}  @endif>Open</option>
                                                <option value="closed" @if($requirement->status == 'closed'){{'selected'}}  @endif>Archive</option>
                                            </select>
                                            <span id="status_error" class="err"></span>
                                            <small id="statusTooltip" class="form-text text-muted"></small>
                                        </div>
                                        <div class="col-md-3 form-group">
                                            <label for="is_visible">Visible To School</label><span class="text-danger">*</span>
                                            <select class="form-control" id="is_visible" name="is_visible">
                                                <option value="">Select Visibility</option>
                                                <option value="0" @if($requirement->is_visible_to_school == '0'){{'selected'}}  @endif>Visible</option>
                                                <option value="1" @if($requirement->is_visible_to_school == '1'){{'selected'}}  @endif>Hidden</option>
                                            </select>
                                            <span id="is_visible_error" class="err"></span>
                                        </div>

                                        <div class="col-md-12 form-group">

                                            @include('components.admin.marketplace.program.program-time-zone', [
                                                'selected_timezone' => $requirement->time_zone
                                            ])
                                        </div>
                                        <div class="col-md-12 form-group d-none">
                                            <label for="address">Address</label><span class="text-danger">*</span>
                                            <textarea class="form-control" id="address" name="address" rows="3" placeholder="Enter Address">{{$requirement->address}}</textarea>
                                            <div id="map"></div>
                                            <input type="hidden" name="city" value="{{$requirement->city}}" id="city">
                                            <input type="hidden" name="state" value="{{$requirement->state}}" id="state">
                                            <input type="hidden" name="country" value="{{$requirement->country}}" id="country">
                                            <input type="hidden" name="lat" value="{{$requirement->lat}}" id="lat">
                                            <input type="hidden" name="lng" value="{{$requirement->lng}}" id="lng">
                                            <span id="address_error" class="err"></span>
                                        </div>

                                        @php
                                            $existingTags = old('tags', json_decode($requirement->requirement_tags ?? '[]', true));
                                        @endphp
                                        <div class="col-md-12 form-group">
                                            <label for="requirement_description">Requirement Description</label>
                                            <textarea class="form-control" id="requirement_description" name="requirement_description" rows="3" placeholder="Enter Requirement Description" minlength="30">{{ old('requirement_description', $requirement->description) }}</textarea>
                                            <small class="text-muted">Minimum 30 characters required</small>
                                            <span id="requirement_description_error" class="err"></span>
                                        </div>
                                        <button type="button" id="generate_tags_btn" class="btn btn-primary ml-3">Generate Tags</button>
                                        <div class="col-md-12 form-group mt-2" id="tag_select_container">
                                            <label for="tag_select">Tags</label>
                                            <select class="form-control" id="tag_select" name="tags[]" multiple="multiple" style="width: 100%;">
                                                <option value="Education">Education</option>
                                                <option value="Learning">Learning</option>
                                                <option value="Online">Online</option>
                                                <option value="Requirement">Requirement</option>
                                                <option value="Language">Language</option>
                                            </select>
                                        </div>
                                    </div>
                                    {{-- Overview-Section --}}

                                    {{-- Schedule Section --}}
                                    <div><h5 class="mt-3 mb-3">Schedule</h5></div>
                                    <div class="row border-bottom pb-2">
                                        <div class="col-md-6 form-group">
                                            <label for="no_instrtructional_days">No Of Instructional Days</label><span class="text-danger">*</span>
                                            <input type="number" class="form-control" id="no_instrtructional_days" name="no_instrtructional_days" value="{{$requirement->no_instrtructional_days}}" placeholder="Enter No Of Instructional Days">
                                            <span id="no_instrtructional_days_error" class="err"></span>
                                        </div>
                                        <div class="col-md-6 form-group">
                                            <label for="class_duration">Class Duration</label>
                                            <select class="form-control" id="class_duration" name="class_duration">
                                                <option value="">Select Class Duration</option>
                                            </select>
                                            <span id="class_duration_error" class="err"></span>
                                        </div>
                                        <div class="col-md-6 form-group">
                                            <label for="no_non_instructional_hr">No Of Non Instructional Hours</label>
                                            <input type="number" class="form-control" id="no_non_instructional_hr" name="no_non_instructional_hr" value="{{$requirement->no_non_instructional_hr}}" placeholder="Enter No Of Non Instructional Hours">
                                            <span id="no_non_instructional_hr_error" class="err"></span>
                                        </div>
                                        <div class="col-md-6 form-group">
                                            <label for="schedule_type">Schedule Type</label><span class="text-danger">*</span>
                                            <select class="form-control" id="schedule_type" name="schedule_type">
                                                <option value="">Select Schedule Type</option>
                                                <option value="regular" @if($requirement->schedule_type == 'regular'){{'selected'}}  @endif>Regular</option>
                                                <option value="alternating" @if($requirement->schedule_type == 'alternating'){{'selected'}}  @endif>Alternating</option>
                                                <option value="other" @if($requirement->schedule_type == 'other'){{'selected'}}  @endif>Other</option>
                                            </select>
                                            <span id="schedule_type_error" class="err"></span>
                                        </div>
                                        <div class="col-md-6 form-group">
                                            <label for="class_start_date">Class Start Date</label><span class="text-danger">*</span>
                                            <input type="date" class="form-control" id="class_start_date" name="class_start_date" value="{{$requirement->start_date}}" placeholder="DD/MM/YYYY">
                                            <span id="class_start_date_error" class="err"></span>
                                        </div>
                                        <div class="col-md-6 form-group">
                                            <label for="class_end_date">Class End Date</label><span class="text-danger">*</span>
                                            <input type="date" class="form-control" id="class_end_date" name="class_end_date" value="{{$requirement->end_date}}" placeholder="DD/MM/YYYY">
                                            <span id="class_end_date_error" class="err"></span>
                                        </div>
                                        <div class="col-md-12 form-group d-none" id="other_sch_class_details_div">
                                            <label for="other_sch_class_details">Class Details</label>
                                            <textarea class="form-control" id="other_sch_class_details" name="other_sch_class_details" rows="3" placeholder="Enter Class Details">{{$requirement->class_details}}</textarea>
                                            <span id="other_sch_class_details_error" class="err"></span>
                                        </div>
                                        {{-- Schedule page included --}}
                                        @include('admin.marketplace.requirements.schedule')
                                        {{-- Schedule page included --}}
                                        <div class="col-md-4 form-group">
                                            <label for="upload_school_calendar">Upload School Calendar</label>
                                            <input type="file" class="form-control" id="upload_school_calendar" name="upload_school_calendar" accept="image/*">
                                            <span id="upload_school_calendar_error" class="err"></span>

                                            @if (!empty($requirement->sch_cal_screenshot))
                                                <div class="mt-2 text-muted">
                                                    <small>{{ basename($requirement->sch_cal_screenshot) }}</small>
                                                </div>
                                            @endif
                                        </div>
                                        <div class="col-md-4 form-group">
                                            <label for="upload_district_calendar">Upload District Calendar</label>
                                            <input type="file" class="form-control" id="upload_district_calendar" name="upload_district_calendar" accept="image/*">
                                            <span id="upload_district_calendar_error" class="err"></span>

                                            @if (!empty($requirement->district_cal_screenshot))
                                                <div class="mt-2 text-muted">
                                                    <small>{{ basename($requirement->district_cal_screenshot) }}</small>
                                                </div>
                                            @endif
                                        </div>
                                        <div class="col-md-4 form-group">
                                            <label for="upload_teacher_schedule_screenshot">Upload Teacher Schedule Screenshot</label>
                                            <input type="file" class="form-control" id="upload_teacher_schedule_screenshot" name="upload_teacher_schedule_screenshot" accept="image/*">
                                            <span id="upload_teacher_schedule_screenshot_error" class="err"></span>

                                            @if (!empty($requirement->teacher_schedule_screenshot))
                                                <div class="mt-2 text-muted">
                                                    <small>{{ basename($requirement->teacher_schedule_screenshot) }}</small>
                                                </div>
                                            @endif
                                        </div>
                                    </div>
                                    {{-- Schedule Section --}}

                                    {{-- Educator Requirement Section --}}
                                    <div><h5 class="mt-3 mb-3">Educator Requirements</h5></div>
                                    <div class="row border-bottom pb-2">
                                        <div class="col-md-6 form-group">
                                            <label for="will_choose_educator">Will Choose Educator</label><span class="text-danger">*</span>
                                            <select class="form-control" id="will_choose_educator" name="will_choose_educator">
                                                <option value="">Select Will Choose Educator</option>
                                                <option value="school" @if($requirement->will_choose_educator == 'school'){{'selected'}}  @endif>School</option>
                                                <option value="whizara" @if($requirement->will_choose_educator == 'whizara'){{'selected'}}  @endif>Whizara</option>
                                            </select>
                                            <span id="will_choose_educator_error" class="err"></span>
                                        </div>
                                        <div class="col-md-6 form-group">
                                            <label for="educator_profile">Educator Profile</label><span class="text-danger">*</span>
                                            <select class="form-control" id="educator_profile" name="educator_profile">
                                                <option value="">Select Educator Profile</option>
                                                <option value="credentialed" @if($requirement->credential_check == 'credentialed'){{'selected'}}  @endif>Credentialed</option>
                                                <option value="non_credentialed" @if($requirement->credential_check == 'non_credentialed'){{'selected'}}  @endif>Non-credentialed</option>
                                            </select>
                                            <span id="educator_profile_error" class="err"></span>
                                        </div>
                                        <div class="col-md-6 form-group">
                                            <label for="special_education_certificate">Special Education Certificate</label><span class="text-danger">*</span>
                                            <select class="form-control" id="special_education_certificate" name="special_education_certificate">
                                                <option value="">Select Special Education Certificate</option>
                                                <option value="yes" @if($requirement->special_education_certificate == 'yes'){{'selected'}}  @endif>Yes</option>
                                                <option value="no" @if($requirement->special_education_certificate == 'no'){{'selected'}}  @endif>No</option>
                                            </select>
                                            <span id="special_education_certificate_error" class="err"></span>
                                        </div>
                                        <div class="col-md-6 form-group">
                                            <label for="language_requirements">Language Requirements</label><span class="text-danger">*</span>
                                            <select class="form-control select2" id="language_requirements" name="language_requirements[]" multiple>
                                                <option value="">Select Language Requirements</option>
                                                 @foreach ($languages as $language)
                                                    <option value="{{ $language }}" @if(in_array($language, explode(',', $requirement->language_requirements))){{'selected'}}  @endif>{{ $language }}</option>
                                                @endforeach
                                            </select>
                                            <span id="language_requirements_error" class="err"></span>
                                        </div>
                                        <div class="col-md-12 form-group">
                                            <label for="other_requirements">Other Requirements</label>
                                            <textarea class="form-control" id="other_requirements" name="other_requirements" rows="3" placeholder="Enter Other Requirements">{{$requirement->other_requirements}}</textarea>
                                            <span id="other_requirements_error" class="err"></span>
                                        </div>
                                        <div class="row pl-3 pr-3">
                                            <div class="col-md-6">
                                                <div class="form-check mb-3">
                                                    <input type="checkbox" class="form-check-input" id="will_follow_provided_curriculum" name="will_follow_provided_curriculum" value="yes" @if($requirement->will_follow_provided_curriculum == 'yes'){{'checked'}}  @endif>
                                                    <label class="form-check-label" for="will_follow_provided_curriculum">
                                                        Educators will use school-provided curriculum and teaching materials on the school's LMS
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-check mb-3">
                                                    <input type="checkbox" class="form-check-input" id="provide_schedule_access" name="provide_schedule_access" value="yes" @if($requirement->provide_schedule_access == 'yes'){{'checked'}}  @endif>
                                                    <label class="form-check-label" for="provide_schedule_access">
                                                        Educators will have access to class schedule and will input grades in school's SIS system
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    {{-- Educator Requirement Section --}}

                                    {{-- Budgets Section --}}
                                    <div><h5 class="mt-3 mb-3">Budgets</h5></div>
                                    <div class="row border-bottom pb-2">
                                        <div class="col-md-4 form-group">
                                            <label for="qualification">Qualifications</label>
                                            <select class="form-control" id="qualification" name="qualification">
                                                <option value="">Select Qualification</option>
                                                <option value="Bachelor’s" @if($requirement->qualifications == 'Bachelor’s'){{'selected'}}  @endif>Bachelor’s</option>
                                                <option value="Master’s" @if($requirement->qualifications == 'Master’s'){{'selected'}}  @endif>Master’s</option>
                                                <option value="Doctorate" @if($requirement->qualifications == 'Doctorate'){{'selected'}}  @endif>Doctorate</option>
                                            </select>
                                            <span id="qualification_error" class="err"></span>
                                        </div>
                                        <div class="col-md-4 form-group">
                                            <label for="experience">Experience</label>
                                            <select class="form-control" id="experience" name="experience">
                                                <option value="">Select Experience</option>
                                                <option value="3" @if($requirement->experience == '3'){{'selected'}}  @endif>0-3 years of experience:</option>
                                                <option value="6" @if($requirement->experience == '6'){{'selected'}}  @endif>3-6 years of experience:</option>
                                                <option value="10" @if($requirement->experience == '10'){{'selected'}}  @endif>6-10 years of experience:</option>
                                                <option value="11" @if($requirement->experience == '11'){{'selected'}}  @endif>10+ years of experience:</option>
                                            </select>
                                            <span id="experience_error" class="err"></span>
                                        </div>
                                        <div class="col-md-4 form-group total_budget_div">
                                            <label for="total_budget">Total Budget</label>
                                            <input type="text" class="form-control" id="total_budget" name="total_budget" value="{{$requirement->total_budget}}" placeholder="Enter Total Budget">
                                            <span id="total_budget_error" class="err"></span>
                                        </div>
                                        <div class="col-md-12 form-group">
                                            <label for="benefits">Benefits</label>
                                            <textarea class="form-control" id="benefits" name="benefits" rows="3" placeholder="Enter Benefits">{{$requirement->benefits}}</textarea>
                                            <span id="benefits_error" class="err"></span>
                                        </div>
                                    </div>
                                    {{-- Budgets Section --}}

                                    {{-- Proctor Section --}}
                                    @php
                                        $proctorName = explode(' ', $requirement->proctor->proctor_name ?? '');
                                        $proctorFname = $proctorName[0] ?? '';
                                        $proctorLname = isset($proctorName[1]) ? implode(' ', array_slice($proctorName, 1)) : '';
                                    @endphp
                                    <div><h5 class="mt-3 mb-3">Proctor</h5></div>
                                    {{-- Toggle Links --}}
                                    <div class="mb-2">
                                        <a href="javascript:void(0);" id="toggleAddProctor">Add New Proctor</a>
                                        <a href="javascript:void(0);" id="toggleSelectProctor" style="display: none;">Select Existing Proctor</a>
                                    </div>
                                    {{-- Select Existing Proctor --}}
                                    <div class="row border-bottom pb-2" id="existingProctorSection">
                                        <div class="col-md-6 form-group">
                                            <label for="existing_proctor">Select Existing Proctor</label>
                                            <select class="form-control" id="existing_proctor" name="existing_proctor">
                                                <option value="">-- Select Proctor --</option>
                                                @foreach($existingProctors as $proctor)
                                                    <option value="{{ $proctor->id }}"{{ isset($requirement->proctor_id) && $requirement->proctor_id == $proctor->id ? 'selected' : '' }}>{{ $proctor->proctor_name }} ({{ $proctor->email }})</option>
                                                @endforeach
                                            </select>
                                            <span id="existing_proctor_error" class="err"></span>
                                        </div>
                                    </div>
                                    {{-- Add New Proctor --}}
                                    <div class="row border-bottom pb-2" id="newProctorSection" style="display: none;">
                                        <div class="col-md-6 form-group">
                                            <label for="proctor_fname">First Name</label>
                                            <input type="text" class="form-control" id="proctor_fname" name="proctor_fname" placeholder="Enter First Name">
                                            <span id="proctor_fname_error" class="err"></span>
                                        </div>
                                        <div class="col-md-6 form-group">
                                            <label for="proctor_lname">Last Name</label>
                                            <input type="text" class="form-control" id="proctor_lname" name="proctor_lname" placeholder="Enter Last Name">
                                            <span id="proctor_lname_error" class="err"></span>
                                        </div>
                                        <div class="col-md-6 form-group">
                                            <label for="proctor_email">Email</label>
                                            <input type="text" class="form-control" id="proctor_email" name="proctor_email" placeholder="Enter Email">
                                            <span id="proctor_email_error" class="err"></span>
                                        </div>
                                        <div class="col-md-6 form-group">
                                            <label for="proctor_phone">Phone</label>
                                            <input type="text" class="form-control" id="proctor_phone" name="proctor_phone" placeholder="Enter Phone">
                                            <span id="proctor_phone_error" class="err"></span>
                                        </div>
                                    </div>
                                    {{-- Hidden field to track selection mode --}}
                                    <input type="hidden" id="proctor_mode" name="proctor_mode" value="existing">
                                    {{-- Proctor Section --}}

                                    {{-- Meeting Link Section --}}
                                    <div><h5 class="mt-3 mb-3">Meeting Link</h5></div>
                                    <div class="row align-items-end border-bottom pb-2">
                                        <div class="col-md-6 form-group">
                                            <label for="link">Link</label>
                                            <input type="url" class="form-control" id="link" name="link" placeholder="Link" value="{{ $requirement->meetingLinks[0]->link ?? '' }}">
                                            <span id="link_error" class="err"></span>
                                        </div>
                                        <div class="col-md-6 form-group d-flex align-items-end">
                                            <button type="button" disabled="disabled" class="btn btn-primary">Generate Link</button>
                                        </div>
                                    </div>
                                    {{-- Meeting Link Section --}}

                                    {{-- Educator Onboarding Section --}}
                                    <div><h5 class="mt-3 mb-3">Educator Onboarding</h5></div>
                                    <div class="row border-bottom pb-2">
                                        <div class="col-md-12 form-group">
                                            <label for="onboarding_instruction">Onboarding Instruction</label>
                                            <textarea class="form-control" id="onboarding_instruction" name="onboarding_instruction" rows="3" placeholder="Enter Onboarding Instruction"></textarea>
                                            <span id="onboarding_instruction_error" class="err"></span>
                                        </div>
                                        <div class="col-md-12 form-group">
                                            <label>Attachments</label>
                                            <div id="attachment_wrapper">
                                                <div class="row attachment-row mb-2">
                                                    <div class="col-md-10">
                                                        <input type="file" class="form-control" name="onboarding_attachments[]" accept="image/*">
                                                    </div>
                                                    <div class="col-md-2 d-flex">
                                                        <button type="button" class="btn btn-primary add-attachment mr-2"><strong>+</strong></button>
                                                    </div>
                                                </div>
                                            </div>
                                            <span id="onboarding_attachments_error" class="err"></span>
                                        </div>
                                    </div>
                                    {{-- Educator Onboarding Section --}}

                                    {{-- Buttons --}}
                                    <div class="col-md-12 d-flex justify-content-md-end justify-content-between mt-3">
                                        <a href="{{ redirect()->getUrlGenerator()->previous() }}" class="btn btn-secondary mr-2">{{ __('messages.cancel') }}</a>
                                        <button type="submit" id="submit_requirements" class="btn btn-primary">{{ __('messages.submit') }}</button>
                                    </div>
                                    {{-- Buttons --}}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
        {{-- ADD REQUIREMENTS END --}}
    </div>
</main>
{{-- MAIN SECTION END --}}
@endsection
@section('scripts')
<script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyCjbhs5R7IoIq8x7SE5AfQ6bx1gylGrcLI&loading=async&callback=initAutocompletemap&libraries=places&v=weekly" async defer></script>

{{-- **********************Overview Section JS********************** --}}
<script>
    // ***************Populate grade levels based on school data***************
    const gradeMap = {
        G_PK_OFFERED: 'TK',
        G_KG_OFFERED: 'K',
        G_1_OFFERED: '1',
        G_2_OFFERED: '2',
        G_3_OFFERED: '3',
        G_4_OFFERED: '4',
        G_5_OFFERED: '5',
        G_6_OFFERED: '6',
        G_7_OFFERED: '7',
        G_8_OFFERED: '8',
        G_9_OFFERED: '9',
        G_10_OFFERED: '10',
        G_11_OFFERED: '11',
        G_12_OFFERED: '12'
    };

    // Maps grade label (e.g., "K") to its value in your system
    const gradeValueMap = {};
    $('#gradeLevels option').each(function () {
        const text = $(this).text().trim();
        const value = $(this).val();
        gradeValueMap[text] = value;
    });

    $('#school').on('change', function () {
        const selectedOptionText = $(this).find('option:selected').text().trim();
        if (!selectedOptionText || selectedOptionText === "Select School") {
            $('#district_school_id').val('');
            $('#gradeLevels').val(null).trigger('change');
            return;
        }

        fetch(`/admin/k12connections/fetch-school-data?name=${encodeURIComponent(selectedOptionText)}`)
            .then(res => res.json())
            .then(data => {
                const match = data.find(item => item.SCH_NAME === selectedOptionText);
                if (!match) {
                    $('#district_school_id').val('');
                    $('#gradeLevels').val(null).trigger('change');
                    return;
                }

                // Set school ID
                $('#district_school_id').val(match.NCESSCH || '');

                // Set grade levels
                let selectedGrades = [];
                for (const [apiKey, gradeLabel] of Object.entries(gradeMap)) {
                    if (match[apiKey] === 'Yes' && gradeValueMap[gradeLabel]) {
                        selectedGrades.push(gradeValueMap[gradeLabel]);
                    }
                }

                $('#gradeLevels').val(selectedGrades).trigger('change');
            })
            .catch(err => {
                console.error('Error fetching school data:', err);
                $('#district_school_id').val('');
                $('#gradeLevels').val(null).trigger('change');
            });
    });
    if ($.fn.select2) {
        $('#gradeLevels').select2();
    }

    // ***************Show address field based on the delivery mode selection***************
    $('#delivery_mode').change(function() {
        if ($(this).val() == 'in-person') {
            $('#address').parent().removeClass('d-none');
        } else {
            $('#address').parent().addClass('d-none');
        }
    });

    // ***************Initialize the address autocomplete***************
    function initAutocompletemap() {
        var autocomplete = new google.maps.places.Autocomplete(
            document.getElementById('address'),
            {
                types: ['geocode'],
                componentRestrictions: { country: "us" } // Optional
            }
        );
        autocomplete.addListener('place_changed', function () {
            var place = autocomplete.getPlace();
            if (!place.address_components) return;
            var city = '', state = '', zipcode = '', country = '';
            $.each(place.address_components, function (index, component) {
                var types = component.types;
                if (types.includes('locality')) {
                    city = component.long_name;
                }
                if (types.includes('administrative_area_level_1')) {
                    state = component.short_name;
                }
                if (types.includes('postal_code')) {
                    zipcode = component.long_name;
                }
                if (types.includes('country')) {
                    country = component.long_name;
                }
            });
            var lat = place.geometry.location.lat();
            var lng = place.geometry.location.lng();
            $('#city').val(city);
            $('#state').val(state);
            $('#zipcode').val(zipcode);
            $('#country').val(country);
            $('#lat').val(lat);
            $('#lng').val(lng);
        });
    }

    $(document).ready(function () {
        function updateStatusTooltip(value) {
            let message = '';
            if (value === 'draft') {
                message = 'Visible to Whizara only';
            } else if (value === 'open') {
                message = 'Visible to Educator Portal';
            } else if (value === 'closed') {
                message = 'Archived — not visible';
            }
            $('#statusTooltip').text(message);
        }

        // Initial set (if editing)
        updateStatusTooltip($('#status').val());

        // On change
        $('#status').on('change', function () {
            updateStatusTooltip($(this).val());
        });
    });
</script>

{{-- **********************Generate Tags********************** --}}
<script>
    $(document).ready(function () {
        const existingTags = @json($existingTags ?? []);
        const description = $('#requirement_description').val().trim();
        $('#tag_select').select2({
            placeholder: "Select tags"
        });
        if (existingTags.length > 0) {
            $('#tag_select').val(existingTags).trigger('change');
            $('#generate_tags_btn').prop('disabled', false);
            $('#tag_select').prop('disabled', false);
        } else {
            $('#generate_tags_btn').prop('disabled', description.length < 50);
            $('#tag_select').prop('disabled', true);
        }
        $('#requirement_description').on('input', function () {
            const val = $(this).val().trim();
            if (val.length >= 30) {
                $('#generate_tags_btn').prop('disabled', false);
                $('#tag_select').prop('disabled', false);
            } else {
                $('#generate_tags_btn').prop('disabled', true);
                $('#tag_select').val(null).trigger('change');
                $('#tag_select').prop('disabled', true);
            }
        });
        $('#generate_tags_btn').on('click', function () {
            const preSelected = ['Education', 'Learning', 'Online'];
            $('#tag_select').val(preSelected).trigger('change');
        });
    });
</script>

{{-- **********************Schedule Section JS********************** --}}
<script>
    // ***************Update time based on class duration***************
    function parseTimeToDate(timeStr) {
        let [time, modifier] = timeStr.split(' ');
        if (!time || !modifier) return null;

        let [hours, minutes] = time.split(':').map(Number);
        if (modifier.toLowerCase() === 'pm' && hours < 12) hours += 12;
        if (modifier.toLowerCase() === 'am' && hours === 12) hours = 0;

        let date = new Date();
        date.setHours(hours);
        date.setMinutes(minutes);
        date.setSeconds(0);
        return date;
    }
    // ***************Format date time in correct format***************
    function formatDateToTime(date) {
        let hours = date.getHours();
        let minutes = date.getMinutes();
        let ampm = hours >= 12 ? 'PM' : 'AM';
        hours = hours % 12;
        hours = hours ? hours : 12; // 0 becomes 12
        minutes = minutes < 10 ? '0' + minutes : minutes;
        return `${hours}:${minutes} ${ampm}`;
    }
    // ***************Append the hours with half and hour interval for class duration***************
    var $durationSelect = $('#class_duration');
    let selectedDuration = @json($requirement->class_duration);
    for (var i = 0.5; i <= 12; i += 0.5) {
        var label = i + (i === 1 ? ' hour' : ' hours');
        $durationSelect.append(
            $('<option>', {
                value: i * 60,
                text: label,
                selected: i == selectedDuration / 60
            })
        );
    }
    // ***************Populate end time based on start time and class duration***************
    $(document).on('change', '.timepicker', function () {
        const $this = $(this);
        const $row = $this.closest('.row');
        const $timepickers = $row.find('.timepicker');
        const classDurationInMinutes = parseFloat($('#class_duration').val());

        if (isNaN(classDurationInMinutes)) return;
        const startTime = $timepickers.eq(0).val();
        const endTime = $timepickers.eq(1).val();

        if ($this.is($timepickers.eq(0))) {
            const fromTime = startTime;
            if (!fromTime) return;

            const fromDate = parseTimeToDate(fromTime);
            if (!fromDate) return;

            const toDate = new Date(fromDate.getTime() + classDurationInMinutes * 60 * 1000);
            const toTimeFormatted = formatDateToTime(toDate);

            const $endInput = $timepickers.eq(1);
            if ($endInput.val() === '') {
                $endInput.val(toTimeFormatted);
            }
        }

        // If this is the end time input and start time is present
        if ($this.is($timepickers.eq(1)) && startTime && endTime) {
            const start = parseTimeToDate(startTime);
            const end = parseTimeToDate(endTime);
            if (!start || !end) return;
            const diffInMinutes = (end - start) / (1000 * 60);
            if (diffInMinutes < classDurationInMinutes) {
                alertify.error("End time is shorter than the selected class duration.");
                $this.val('');
            } else if (diffInMinutes > classDurationInMinutes) {
                alertify.error("End time is longer than the selected class duration.");
                $this.val('');
            }
        }
    });

    // ***************Calculate the end date based on class days and start date***************
    $('.no-class-datepicker').datepicker({
        dateFormat: 'mm/dd/yy'
    });
    function parseDate(str) {
        var parts = str.split('/');
        if (parts.length !== 3) return null;
        var month = parseInt(parts[0]) - 1;
        var day = parseInt(parts[1]);
        var year = parseInt(parts[2]);
        return new Date(year, month, day);
    }
    function formatDate(date) {
        var mm = String(date.getMonth() + 1).padStart(2, '0');
        var dd = String(date.getDate()).padStart(2, '0');
        var yyyy = date.getFullYear();
        return mm + '/' + dd + '/' + yyyy;
    }
    function calculateEndDate() {
        var days = parseInt($('#no_instrtructional_days').val());
        var startDateStr = $('#class_start_date').val();
        var startDate = parseDate(startDateStr);

        if (!isNaN(days) && startDate) {
            var endDate = new Date(startDate);
            endDate.setDate(endDate.getDate() + days - 1); // -1 to include the start day

            var formattedEndDate = formatDate(endDate);
            $('#class_end_date').val(formattedEndDate);

            // Restrict datepicker for end date to prevent invalid selections
            $('#class_end_date').datepicker('option', {
                minDate: endDate,
                beforeShowDay: function(date) {
                    // Only allow selecting the calculated end date
                    return [formatDate(date) === formattedEndDate];
                }
            });
        }
    }
    // Trigger on changes to instructional days and start date
    $('#no_instrtructional_days, #class_start_date').on('input change', function () {
        calculateEndDate();
    });
    // Extra safeguard if user manually edits end date
    $('#class_end_date').on('change', function () {
        var userEndDate = parseDate($(this).val());
        var startDate = parseDate($('#class_start_date').val());
        var days = parseInt($('#no_instrtructional_days').val());

        if (!startDate || isNaN(days)) return;

        var minValidEndDate = new Date(startDate);
        minValidEndDate.setDate(minValidEndDate.getDate() + days - 1);

        if (userEndDate < minValidEndDate) {
            alertify.error("End date must be at least " + days + " days from the start date.");
            $(this).val('');
        }
    });

    // ***************Show schedule fields based on the schedule type selection***************
    function toggleScheduleFields() {
        if ($("#schedule_type").val() === 'other') {
            $('#other_sch_class_details').parent().removeClass('d-none');
            $('#normal-schedule').addClass('d-none');
        } else {
            $('#other_sch_class_details').parent().addClass('d-none');
            $('#normal-schedule').removeClass('d-none');
        }
    }
    // Run on change
    $("#schedule_type").change(function () {
        toggleScheduleFields();
    });
    // Run on page load
    $(document).ready(function () {
        toggleScheduleFields();
    });
</script>

{{-- **********************Educator Requirement Section JS********************** --}}
<script>
    function handleDeliveryModeChange() {
        const deliveryMode = $('#delivery_mode').val();
        if (deliveryMode === 'in-person') {
            $('#will_choose_educator').val('whizara').prop('disabled', true);
            $('#educator_profile').val('non_credentialed').prop('disabled', true);
            $('#special_education_certificate').val('no').prop('disabled', true);
        } else {
            $('#will_choose_educator').prop('disabled', false);
            $('#educator_profile').prop('disabled', false);
            $('#special_education_certificate').prop('disabled', false);
        }
    }
    $('#delivery_mode').change(handleDeliveryModeChange);
    $(document).ready(function () {
        handleDeliveryModeChange();
    });

    $(document).ready(function () {
        function toggleFields() {
            let profile = $('#educator_profile').val();
            if (profile === 'non_credentialed' || profile === '') {
                $('#qualification, #experience').prop('disabled', true).val('');
                $('.total_budget_div').addClass('d-none');
            } else {
                $('#qualification, #experience').prop('disabled', false);
                $('.total_budget_div').removeClass('d-none');
            }
        }
        toggleFields();
        $('#educator_profile').on('change', function () {
            toggleFields();
        });
    });
</script>

{{-- **********************Budget Calculation JS********************** --}}
<script>
    // ***************Calculate the budget***************
        function initializeBudgetInitialize() {
        var subject_id = $("#courses").val();
        var no_instrtructional_days = $("#no_instrtructional_days").val();
        var class_duration = $("#class_duration").val(); // in minutes
        var total_minutes = no_instrtructional_days * class_duration;
        var total_hours = total_minutes / 60;

        var provide_curriculum = $("#will_follow_provided_curriculum").is(":checked");
        var qualification = $("#qualification").val();
        var experience = $("#experience").val();
        var educator_profile = $("#educator_profile").val();

        if (subject_id) {
            $.ajax({
                url: "/admin/k12connections/calculate-budget/" + subject_id,
                type: "GET",
                dataType: "json",
                data: {
                    total_hours: total_hours,
                    provide_curriculum: provide_curriculum,
                    qualification: qualification,
                    experience: experience,
                },
                success: function (response) {
                    if (response.status) {
                        var budgets = response.budgets;
                        $('#experience option[value="3"]').text(`0-3 years of experience: $${budgets[3].toFixed(2)}`).data("budget", budgets[3]);
                        $('#experience option[value="6"]').text(`3-6 years of experience: $${budgets[6].toFixed(2)}`).data("budget", budgets[6]);
                        $('#experience option[value="10"]').text(`6-10 years of experience: $${budgets[10].toFixed(2)}`).data("budget", budgets[10]);
                        $('#experience option[value="11"]').text(`10+ years of experience: $${budgets[11].toFixed(2)}`).data("budget", budgets[11]);

                        $(".credential_div").show();
                        if (educator_profile === "non_credentialed") {
                            $("#experience").prop("disabled", true).val('');
                        } else {
                            $("#experience").prop("disabled", false);
                        }

                        // ✅ Set total budget if experience is already selected
                        var selectedOption = $('#experience').find('option:selected');
                        var budget = selectedOption.data('budget');
                        if (budget !== undefined) {
                            $('#total_budget').val(budget.toFixed(2));
                            $('#total_budget_div').removeClass('d-none');
                        } else {
                            $('#total_budget').val('');
                            $('#total_budget_div').addClass('d-none');
                        }
                    }
                },
                error: function (xhr) {
                    console.log("Error fetching subject budget");
                },
            });
        }
    }

    // ***************Trigger on Page Load and on Relevant Changes***************
    $(document).ready(function () {
        initializeBudgetInitialize();
        $('#courses, #no_instrtructional_days, #class_duration, #qualification, #experience').on('change input', function () {
            initializeBudgetInitialize();
        });
        $('#will_follow_provided_curriculum').on('change', function () {
            initializeBudgetInitialize();
        });
    });

    // ***************Set total budget on experience change***************
    $('#experience').on('change', function () {
        var selectedOption = $(this).find('option:selected');
        var budget = selectedOption.data('budget');

        if (budget !== undefined) {
            $('#total_budget').val(budget.toFixed(2));
            $('#total_budget_div').removeClass('d-none'); // Show the field if hidden
        } else {
            $('#total_budget').val('');
            $('#total_budget_div').addClass('d-none'); // Optionally hide it again
        }
    });
</script>

{{-- **********************Script to add more day rows to assign multiple classes********************** --}}
<script>
    function initializeTimepickers() {
        $('.timepicker').timepicker({
            'timeFormat': 'h:i A'
        });
    }
    initializeTimepickers();

    // Handle add (+) button
    $(document).on('click', '.add-time-slot', function () {
        const dayBlock = $(this).closest('.day-block');
        const originalSlot = $(this).closest('.time-slot');
        const clone = originalSlot.clone();

        // Determine next index based on existing slots
        const inputs = dayBlock.find('.time-slot input:first-child');
        const totalSlots = inputs.length;
        const dayName = inputs.first().val().toLowerCase();

        // Update input names with new index and clear values
        clone.find('input').each(function () {
            const nameAttr = $(this).attr('name');
            const fieldName = nameAttr.split('[')[2]; // "start_time]" or "end_time]"
            if (fieldName) {
                $(this).attr('name', `${dayName}[${totalSlots}][${fieldName}`);
            }

            if (!$(this).is('[readonly]')) {
                $(this).val(''); // Clear only editable inputs
            }
        });

        // Append the clone
        dayBlock.find('.extra-time-slots').append(clone);

        // Re-initialize timepicker
        clone.find('.timepicker').timepicker({
            'timeFormat': 'h:i A'
        });
    });

    // Handle remove (-) button
    $(document).on('click', '.remove-time-slot', function () {
        const totalSlots = $(this).closest('.day-block').find('.time-slot').length;
        // Only remove if more than one slot is present
        if (totalSlots > 1) {
            $(this).closest('.time-slot').remove();
        }
    });
</script>

{{-- **********************Script to add more attachment inputs********************** --}}
<script>
$(document).ready(function () {
    $(document).on('click', '.add-attachment', function () {
        const newRow = `
            <div class="row attachment-row mb-2">
                <div class="col-md-10">
                    <input type="file" class="form-control" name="onboarding_attachments[]" accept="image/*">
                </div>
                <div class="col-md-2 d-flex">
                    <button type="button" class="btn btn-danger remove-attachment"><strong>-</strong></button>
                </div>
            </div>
        `;
        $('#attachment_wrapper').append(newRow);
    });
    $(document).on('click', '.remove-attachment', function () {
        $(this).closest('.attachment-row').remove();
    });
});
</script>

{{-- **********************Ajax Form Submission********************** --}}
<script>
    $('#edit_requirements_form').on('submit', function (e) {
        e.preventDefault();
        var formData = new FormData(this);
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });
        $.ajax({
            url: "/admin/k12connections/update-requirements/{{ $requirement->id }}",
            type: 'POST',
            data: formData,
            dataType: "json",
            processData: false,
            contentType: false,
            beforeSend: function () {
                $('#submit_requirements').prop("disabled", true);
                $('.err').html('');
            },
            success: function (response) {
                if (response.success == true) {
                    alertify.success('Submitted Successfully');
                    window.location.href = '/admin/k12connections/requirements';
                } else {
                    alertify.error(response.message || "Something went wrong");
                }
                $('#submit_requirements').prop("disabled", false);
            },
            error: function (xhr) {
                $('#submit_requirements').prop("disabled", false);
                if (xhr.status === 422) {
                    let errors = xhr.responseJSON.errors;
                    $.each(errors, function (key, value) {
                        $('#' + key + '_error').html(value[0]);
                    });
                } else {
                    alertify.error("Unexpected error occurred.");
                }
            },
        });
    });
</script>
<script>
    $(document).ready(function () {
        $('#toggleAddProctor').on('click', function () {
            $('#existingProctorSection').hide();
            $('#newProctorSection').show();
            $('#toggleAddProctor').hide();
            $('#toggleSelectProctor').show();
            $('#proctor_mode').val('new');
        });
        $('#toggleSelectProctor').on('click', function () {
            $('#existingProctorSection').show();
            $('#newProctorSection').hide();
            $('#toggleAddProctor').show();
            $('#toggleSelectProctor').hide();
            $('#proctor_mode').val('existing');
        });
    });
</script>
@endsection