<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateUserAdditionalSubjectsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('user_additional_subjects_v1', function (Blueprint $table) {
            $table->id()->comment('PK: Unique subject ID');
            $table->foreignId('user_id')
                ->constrained('new_onboarding_instructor')
                ->onDelete('cascade')
                ->comment('FK to new_onboarding_instructor.id');
            $table->string('subject_code', 50)->comment('Subject code');
            $table->string('subject_name', 255)->comment('Subject name');
            $table->tinyInteger('proficiency')->unsigned()->comment('Proficiency 1-5');
            $table->enum('status', ['pending', 'approved', 'rejected', 'accepted', 'declined'])
                ->default('pending')
                ->comment('Approval status');
            $table->timestamps();

            $table->index('user_id');
            $table->index('status');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('user_additional_subjects_v1');
    }
}
