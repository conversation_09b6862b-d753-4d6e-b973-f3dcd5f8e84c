<?php

namespace App\Models\v1;

use Illuminate\Database\Eloquent\Model;

class Otp extends Model
{
    protected $table = 'otps';
    protected $fillable = [
        'user_id',
        'user_type',
        'otp_code',
        'expires_at',
    ];

    protected $casts = [
        'expires_at' => 'datetime',
    ];

    protected static function booted()
    {
        parent::boot();
        static::creating(function ($otp) {
            // Auto-generate OTP code if not provided
            if (empty($otp->otp_code)) {
                $otp->otp_code = str_pad(random_int(0, 999999), 6, '0', STR_PAD_LEFT);
            }

            // Auto-set expiry to 10 minutes if not provided
            if (empty($otp->expires_at)) {
                $otp->expires_at = now()->addMinutes(10);
            }
        });
    }

    public function isExpired(): bool
    {
        return $this->expires_at && $this->expires_at->isPast();
    }
}
