<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateUserNotificationPreferencesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('user_notification_preferences_v1', function (Blueprint $table) {
            $table->id()->comment('PK: Unique notification preference ID');
            $table->foreignId('user_id')
                ->constrained('new_onboarding_instructor')
                ->onDelete('cascade')
                ->comment('FK to new_onboarding_instructor.id');
            $table->string('notification_type')->comment('Type of notification');
            $table->boolean('in_app_notifications')->default(true)->comment('Enable in-app notifications');
            $table->boolean('email_notifications')->default(true)->comment('Enable email notifications');
            $table->boolean('realtime_email_notifications')->default(false)->comment('Enable realtime email alerts');
            $table->boolean('daily_summary_emails')->default(true)->comment('Enable daily summary emails');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('user_notification_preferences_v1');
    }
}
