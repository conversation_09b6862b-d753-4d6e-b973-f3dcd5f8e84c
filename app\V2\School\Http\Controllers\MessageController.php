<?php

namespace App\V2\School\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\Chat;
use Illuminate\Http\Request;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Support\Facades\Auth;
use App\V2\Core\Helpers\ApiResponse;
use App\V2\Core\Services\MessageService;

use Exception;

class MessageController extends Controller
{
    
    protected $messageService;

    public function __construct(MessageService $messageService){
        $this->messageService = $messageService;
    }
    /**
     * Display a list of chats for the logged-in user.
     *
     * GET /api/messages
     */
    public function index()
    {
        try {
            $loggedInId   = Auth::id();
            $loggedInRole = 'school';
            $chatsList = $this->messageService->FetchThreads($loggedInId, $loggedInRole);
            return ApiResponse::success($chatsList, "Message List Fetched Successfully");
        } catch (Exception $e) {
            return ApiResponse::error("Failed to fetch messages: " . $e->getMessage(), 500);
        }
    }

    /**
     * Show all messages for a specific chat thread.
     *
     * GET /api/messages/{id}
     */
    public function show($id)
    {
        try {
            $loggedInId   = Auth::id();
            $loggedInRole = 'school';
            $conversation = $this->messageService->MessagesByThread($id, $loggedInId, $loggedInRole);
            return ApiResponse::success($conversation, "Conversation fetched successfully");
        } catch (ModelNotFoundException $e) {
            return ApiResponse::error("Message not found", 404);
        } catch (Exception $e) {
            return ApiResponse::error("Failed to fetch conversation: " . $e->getMessage(), 500);
        }
    }

    /**
     * Delete a specific message.
     *
     * DELETE /api/messages/{id}
     */
    public function destroy($id)
    {
        try {
            $message = Chat::findOrFail($id);

            $message->delete();

            return ApiResponse::success(null, "Message deleted successfully");

        } catch (ModelNotFoundException $e) {
            return ApiResponse::error("Message not found", 404);
        } catch (Exception $e) {
            return ApiResponse::error("Failed to delete message: " . $e->getMessage(), 500);
        }
    }
}
