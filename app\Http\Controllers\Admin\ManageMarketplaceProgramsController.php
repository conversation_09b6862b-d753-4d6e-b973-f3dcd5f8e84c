<?php

namespace App\Http\Controllers\Admin;

use App\cbo;
use App\Classes;
use App\District;
use App\document_form;
use App\Helpers\CustomHelper;
use App\Helpers\DataTableHelper;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\k12Connection\k12ConnectionProgramRequest;
use App\Imports\k12ProgramClassesImport;
use App\invite_programs;
use App\MailModel;

use App\View\Components\Marketplace\Admin\EducatorInvite;
use App\Services\EducatorInvitationService;

use App\Models\k12ConnectionCategorizedData;
use App\Models\k12ConnectionClasses;
use App\Models\k12ConnectionContactDetails;
use App\Models\k12ConnectionMeetingLinks;
use App\Models\k12ConnectionNoClassDates;
use App\Models\k12ConnectionProgramNotes;
use App\Models\k12ConnectionPrograms;
use App\Models\k12ConnectionProgramsSchedule;
use App\Models\PlatformSchoolCalendersModel;
use App\Models\PlatformSchoolInvites;
use App\Models\PlatformSchoolProctor;
use App\Models\PlatformSchoolRequirements;
use App\Models\SchoolReviewApplicants;
use App\Models\v1\SubjectArea;
use App\Models\v1\SubjectBudget;
use App\OnboardingInstructor;
use App\SchoolRequirementContract;
use App\SchoolRequirementContractVersion;
use App\Schools;
use App\SettingTermsModel;
use App\ShortlistInstructorModel;
use App\ShortlistMarketplaceInstructorModel;
use App\StateModel;
use App\Subject;
use App\SubsubjectModel;
use App\User;
use App\V2\Core\Helpers\ApiResponse;
use App\ZoomModel;
use DB;
use Mail;
use Auth;
use Barryvdh\DomPDF\Facade\Pdf;
use Carbon\Carbon;
use Excel;
use Exception;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\DB as FacadesDB;
use Illuminate\Support\Facades\Facade;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;

class ManageMarketplaceProgramsController extends Controller
{
    public $daysOfWeek = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];

    protected $inviteService;

    public function __construct(EducatorInvitationService $inviteService)
    {
        $this->inviteService = $inviteService;
    }

    public function insertProgramSchedule($programId, $slotDay, $startTime, $endTime, $timezone = "America/Los_Angeles")
    {
        k12ConnectionProgramsSchedule::create([
            "program_id" => $programId,
            "class_day" => $slotDay,
            "start_time" => formatTimeAdminTimezone($startTime, $timezone),
            "end_time" => formatTimeAdminTimezone($endTime, $timezone),
        ]);
    }

    private function generateStatusButton($row, $userPermissions)
    {
        $updatePermission = in_array('update', $userPermissions);

        $hasActiveClasses = $row->userNotes()
            ->where(function ($qry) {
                $qry->whereNotNull('main_instructor_id')
                    ->orWhereNotNull('sub_instructor_id');
            })
            ->whereNotNull('status')
            ->where('class_date', '>=', now()->toDateString())
            ->get();


        switch ($row->status) {
            case 'draft':
                return $updatePermission
                    ? '<a href="javascript:void(0);" onclick="status_update(' . $row->id . ',  ' . $hasActiveClasses . ')" data-data="1" class="btn btn-warning btn-rounded changestatuscls-' . $row->id . '">Deactive</a>'
                    : 'Deactive';
            case 'publish':
                return $updatePermission
                    ? '<a href="javascript:void(0);" onclick="status_update(' . $row->id . ',' . $hasActiveClasses . ')" data-data="0" class="btn btn-success btn-rounded changestatuscls-' . $row->id . '">Active</a>'
                    : 'Active';
            default:
                return '';
        }
    }

    private function generateActionButtons($encryptedStrId,$res, $row)
    {
        $orgId = $row->id;
        $viewRoute =  url('admin/k12connections/view-program/step1/' . $encryptedStrId);
        $actionUrl = "javascript:void(0);";
        $editButton = $deleteButton = $logisticsButton = '';

        $viewButton = "<a href='{$viewRoute}'><button type='button' title='View' class='btn btn-rounded btn-block btn-xs btn-outline-secondary'><i class='fa fa-eye' aria-hidden='true'></i></button></a>&nbsp;";
        // if (isset($res['manageprogram'])) :
        //     if (array_key_exists('manageprogram', $res)) :
        //         if (in_array('update', json_decode($res['manageprogram'], true))) :
        //             $editRoute = url('edit-program/' . $encryptedStrId);

        //             $editButton = "<a href='{$editRoute}' class='btn btn-rounded btn-block btn-xs btn-outline-secondary mt-0'><i class='fa fa-pencil' aria-hidden='true'></i></a>  &nbsp;";
        //         endif;

        //         if (in_array('delete', json_decode($res['manageprogram'], true))) :
        //             $hasActiveClasses = $row->userNotes()
        //                 ->where(function ($qry) {
        //                     $qry->whereNotNull('user_id')
        //                         ->orWhereNotNull('sub_user_id');
        //                 })
        //                 ->whereNull('status')
        //                 ->where('class_date', '>=', now()->toDateString())
        //                 ->exists();
        //             $deleteRoute = route('admin.program.delete', ['program' => $orgId]);

        //             $deleteButton = "<a href='{$actionUrl}' onclick=deleteRow('$deleteRoute','$hasActiveClasses')  class='btn btn-rounded btn-block btn-xs btn-outline-danger mt-0'><i class='fa fa-trash' aria-hidden='true'></i></a>";

        //         endif;

        //         if (in_array('logistics/prerequisites', json_decode($res['manageprogram'], true))) :

        //             $logisticsRoute = route('admin.program.logistics', ['program_id' => $encryptedStrId]);

        //             $logisticsButton = "<a href='{$logisticsRoute}'   class='btn btn-rounded btn-block btn-xs btn-outline-info mt-0'>Logistics/Prerequisites</a>&nbsp;";

        //         endif;

        //     endif;
        // endif;



        return "<div class='w-100 d-flex justify-content-around align-items-center'>{$viewButton}</div>";
    }

    public function programsList(Request $request)
    {
        $adminSession = session()->get('Adminnewlogin');
        $adminType = $adminSession['type'];

        if ($request->ajax()) {
            $params = DataTableHelper::getParams($request);

            $currentDate = now()->toDateString();
            $id = $request->id;
            $qry = k12ConnectionPrograms::with(['userNotes','subject','school']);

            $whereInIds = getAdminUserk12ConnectionProgramIds();

            if ($adminType == '4') {
                $qry->whereIn('id', $whereInIds);
            } else {
                if (!empty($whereInIds)) {
                    $qry->whereIn('id', $whereInIds);
                }
            }


            switch ($id) {
                case "Today":
                    $qry->where("program_status", "!=", "inactive");
                    $qry->whereHas('userNotes', fn($query) => $query->where('class_date', $currentDate));
                    break;

                case "Active":
                    $qry->where("program_status", "!=", "inactive")
                        ->whereHas('mainAssignedUser')
                        ->where("start_date", "<=", $currentDate)
                        ->where("end_date", ">=", $currentDate);
                    break;

                case "Upcoming":
                    $qry->where("program_status", "!=", "inactive")
                        ->where("start_date", ">",  $currentDate)
                        ->where("end_date", ">",  $currentDate);
                    break;

                case "ALL":
                    // if ($request->filled('missing_instructor')) {
                    //     $request->missing_instructor == '1'
                    //         ? $qry->doesntHave('mainAssignedUser')
                    //         : $qry->has('subAssignedUser');
                    // }
                    break;

                case "Publish":
                    $qry->where("program_status", "!=", "inactive")
                        ->where("program_status", $id)
                        ->whereDoesntHave('mainAssignedUser');
                    break;

                case "Completed":
                    $qry->where("program_status", "!=", "inactive")
                        ->whereHas('mainAssignedUser')
                        ->where("start_date", "<",  $currentDate)
                        ->where("end_date", "<",  $currentDate);
                    break;

                case "Archived":
                    $qry->where("program_status", "!=", "inactive")
                        ->whereDoesntHave('mainAssignedUser')
                        ->where("start_date", "<",  $currentDate)
                        ->where("end_date", "<",  $currentDate);
                    break;

                default:
                    $qry->where("program_status", $id);
                    break;
            }

            if (!empty($params['searchValue'])) {
                $searchValue = strtolower($params['searchValue']);
                $qry->where(function ($qry) use ($searchValue, $params) {
                    $qry->where(function ($query) use ($searchValue) {
                        $userIds = User::whereRaw('LOWER(full_name) LIKE ?', ['%' . $searchValue . '%'])->pluck('id')->toArray();
                        $query->whereIn('school_id', $userIds);
                    })->orWhereHas('user', function ($subQuery) use ($searchValue) {
                        $subQuery->whereRaw('LOWER(first_name) LIKE ?', ['%' . $searchValue . '%'])
                                ->orWhereRaw('LOWER(last_name) LIKE ?', ['%' . $searchValue . '%']);
                    })->orWhere(function ($subQuery) use ($params) {
                        DataTableHelper::applySearchFilter($subQuery, $params['searchValue'], $params['columns']);
                    });
                });
            }

            if ($params['columnName']) {
                switch($params['columnName']){
                    case 'id':
                        $params['columnName'] = 'id';
                        break;

                    case 'name':
                        $params['columnName'] = 'name';
                        break;

                    case 'school_name':
                        $qry->with('school');
                        $qry->addSelect([
                            'schoolName' => User::selectRaw('users.full_name')
                            ->whereColumn('k12_connection_programs.school_id', 'users.id')
                            ->limit(1)
                        ]);

                        $params['columnName'] = 'schoolName';
                        break;

                    case 'course':
                        $qry->addSelect([
                            'subSubjectName' => SubsubjectModel::selectRaw('tbl_sub_subjects.name')
                            ->whereColumn('k12_connection_programs.subject_id', 'tbl_sub_subjects.id')
                            ->limit(1)
                        ]);
                        $params['columnName'] = 'subSubjectName';
                        break;

                    // case 'instructore':
                    //     $qry->addSelect([
                    //         'instId' => k12ConnectionClasses::selectRaw('k12_connection_classes.user_id')
                    //             ->whereColumn('k12_connection_classes.program_id', 'k12_connection_programs.id')
                    //             ->where(function ($query) {
                    //                 $currentDate = now()->toDateString();
                    //                 $query->where(function ($subQuery) use ($currentDate) {
                    //                     $subQuery->whereNull('is_makeup_class')
                    //                         ->where(function ($q) use ($currentDate) {
                    //                             $q->whereDate('class_date', '=', $currentDate)
                    //                               ->orWhere('class_date', '>', $currentDate);
                    //                         });
                    //                 })->orWhere('class_date', '<', $currentDate);
                    //             })
                    //             ->orderByRaw("CASE WHEN class_date >= '{$currentDate}' THEN 0 ELSE 1 END")
                    //             ->orderBy('class_date', 'asc')
                    //             ->limit(1) // Get the next upcoming program's user_id
                    //     ]);

                    //     $qry->addSelect([
                    //         'instName' => OnboardingInstructor::selectRaw("CONCAT(users.first_name, ' ', users.last_name)")
                    //         ->whereColumn('instId', 'new_onboarding_instructor.id')
                    //         ->limit(1)
                    //     ]);

                    //     $params['columnName'] = 'instName';
                    //     break;

                    // case 'standby':
                    //     $qry->addSelect([
                    //         'subInstId' => invite_programs::selectRaw('tbl_invite_programs.user_id')
                    //             ->whereColumn('tbl_invite_programs.program_id', 'k12_connection_programs.id')
                    //             ->where([
                    //                 'tbl_invite_programs.status' => 1,
                    //                 'tbl_invite_programs.is_standby' => 1
                    //             ])
                    //         ->limit(1)
                    //     ]);

                    //     $qry->addSelect([
                    //         'subInstName' => OnboardingInstructor::selectRaw("CONCAT(new_onboarding_instructor.first_name, ' ', new_onboarding_instructor.last_name)")
                    //         ->whereColumn('subInstId', 'new_onboarding_instructor.id')
                    //         ->limit(1)
                    //     ]);

                    //     $params['columnName'] = 'subInstName';
                    //     break;

                    case 'delivery_type':
                        $params['columnName'] = 'delivery_type';
                        break;

                    case 'start_date':
                        $params['columnName'] = 'start_date';
                        break;

                    case 'end_date':
                        $params['columnName'] = 'end_date';
                        break;

                    case 'status':
                        $params['columnName'] = 'status';
                        break;

                    case 'program_type':
                        $qry->addSelect([
                            'programType' => k12ConnectionCategorizedData::where('type', 'program_type')->selectRaw('k12_connection_categorized_data.description')
                            ->whereColumn('k12_connection_programs.program_type_id', 'k12_connection_categorized_data.id')
                            ->limit(1)
                        ]);
                        $params['columnName'] = 'programType';

                        break;

                    case 'program_type':
                        $params['columnName'] = 'created_at';
                        break;
                }

                if($params['columnName'] != 'departing') {
                    $qry->orderBy($params['columnName'] ?? 'id', $params['columnSortOrder'] ?? 'desc');
                }
            }

            [$count, $result] = DataTableHelper::applyPagination($qry, $params['row'], $params['rowperpage']);
            if ($params['columnName'] == 'departing'){
                foreach ($result as $row) {
                    $uniqueUserIds = CustomHelper::allk12ConnectionProgramUsers($row->id);
                    $departingNotices = MailModel::whereIn('user_id', array_filter($uniqueUserIds))->get(['user_id']);
                    $departingCount = $departingNotices->count();
                    $row->departing_instructor_count = $departingCount;
                }
                $result = $result->sortByDesc('departing_instructor_count')->values();
            }

            $data = [];
            $i = 1;
            $res = get_permission(session('Adminnewlogin')['type']);

            foreach ($result as $row) {

                $encryptedStrId = encrypt_str($row->id);
                $viewButton = "";
                if (($id == 'Publish' || $id == 'Draft') && $row->end_date >= $currentDate  && (!$row->mainAssignedUser || !$row->mainUser)) :

                    $viewButton .= "<input type='checkbox' form='inviteprogram' value='{$row->id}' name='program_id[]'
                class='program_id'> ";
                    $viewButton .= @$row->mainAssignedUser->id;
                endif;

                $viewRoute =  url('admin/k12connections/view-program/step1/' . $encryptedStrId);

                $viewButton .= " <a href='{$viewRoute}'>{$row->id}</a>";

                if ($row->program_status != 'inactive') {
                    $action = $this->generateActionButtons($encryptedStrId,$res, $row);
                } else {
                    $action = '';
                }


                if (strlen($row->name) > 20) {
                    $programName = substr($row->name, 0, 20) . '...';
                } else {
                    $programName = $row->name;
                }
                if ($row->school_id) {
                    $schoolName = schoolusername($row->school_id);
                } else {
                    $schoolName = '';
                }

                $uniqueUserIds = CustomHelper::allk12ConnectionProgramUsers($row->id);

                /**
                 * get departing instructors
                 */
                // $notices = MailModel::with('onboardingInstructor:id,first_name,last_name')
                //     ->whereIn('user_id', array_filter($uniqueUserIds))
                //     // ->whereDate('created_at', '<=', Carbon::now()->subDays(14))
                //     ->get(['user_id']);


                // $departing = [];

                // if ($notices->isNotEmpty()) {
                //     foreach ($notices as $notice) {
                //         if (!$notice->user) {
                //             continue;
                //         }
                //         $viewUrl = url('viewinstructordetails/step1/' . encrypt_str($notice->user_id));
                //         $viewName = $notice->user->first_name . ' '  . $notice->user->last_name;
                //         $departing[] = "<a href='{$viewUrl}'>{$viewName}</a> ";
                //     }
                // }
                // $departing = !empty($departing) ? implode(', ', $departing) : "-";
                if(isset($row->userNotes[0]) > 0){
                    if ($row->userNotes[0]->class_date) {
                        $classDate = explode(" ", $row->userNotes[0]->class_date)[0];
                        $startTime = $row->userNotes[0]->start_time;
                        $dateTimeString = $classDate . ' ' . $startTime;
                    } else {
                        $formattedDateTime = '';
                    }
                }
                if(isset($row->nextCommingProgram->user_id)){
                    $inst = k12username($row->nextCommingProgram->user_id);
                }
                else{
                    $inst = '';
                }

                if ($row->subject_id) {
                    $subsubject = subsubjectname($row->subject_id);
                } else {
                    $subsubject = '';
                }
                if ($row->id) {
                    $standby = getstandbyk12username($row->id);
                } else {
                    $standby = '';
                }
                $data[] = [
                    "id" => $viewButton,
                    "name" => "<a href='{$viewRoute}'>{$programName}</a>",
                    "school_name" => $schoolName,
                    "course" => $subsubject ?? '',
                    "instructore" => $inst,
                    "delivery_type" => $row->delivery_type,
                    "standby" => $standby,
                    "start_date" => date('m-d-Y', strtotime($row->start_date)),
                    "end_date" => date('m-d-Y', strtotime($row->end_date)),
                    "status" => $this->generateStatusButton($row, json_decode($res['manageprogram'], true)),
                    "program_type" => categorizedProgramName($row->program_type_id),
                    "departing" => "-",
                    "created_at" => getAdminTimestamp($row->created_at),
                    "action" => $action,
                    // "name" => $viewButton. ' '.$row->id,
                    // "class_date" => $formattedDateTime ?? '',
                ];

                $i++;
            }

            $additional_data = [
                "searchValue" => $params['searchValue'],
                "columnName" => $params['columns'],
            ];

            return DataTableHelper::generateResponse($params['draw'], $count, $data, $additional_data);
        }


        $data["state"] = StateModel::where(["country_id" => "239"])->get();
        $data["subject"] = Subject::get();
        $data["status"] = DB::table("tbl_profile_status")->where(['status_type' => 'Instructor'])->get();
        $data["users"] =  User::active()->special()->get(['users.id', 'users.first_name', 'users.last_name', 'users.email']);

        return view("admin.marketplace.programs.list", compact('data'));
    }

    public function addProgram()
    {
        $cbos = cbo::pluck('name', 'id');
        $district = District::where("status", "1")->get();
        $states = StateModel::where(["country_id" => "239"])->pluck('name', 'id');
        $grade = Classes::where("status", "1")->get();
        $subjects = Subject::pluck('subject_name', 'id');
        $schools = User::where("type", 6)->get();
        $timezone = k12ConnectionCategorizedData::where('type', 'timezone')->get();
        $program_type = k12ConnectionCategorizedData::where('type', 'program_type')->get();
        $states = StateModel::where(["country_id" => "239"])->get();

        return view("admin.marketplace.programs.add", compact("district", "grade", "cbos", "states", "subjects", "schools", 'timezone', 'program_type'));
    }

    public function requirementsInvite(Request $request)
    {
        $id = decrypt_str($request->id);
        $invite = new EducatorInvite($request->invitation_type, $id);
        return $invite->render();
    }

    public function requirementsInviteSearch(Request $request)
    {
        $id = $request->requirement_id;
        $requirements = PlatformSchoolRequirements::with(['subject'])->find($id);
        $searchTerm = $request->all();

        // Check if this is a DataTable request
        if ($request->has('draw')) {
            return $this->handleDataTableRequest($request, $requirements, $searchTerm);
        }

        // Legacy response for non-DataTable requests
        $educators = $this->inviteService->getEligibleEducators($requirements, $searchTerm);
        return response()->json($educators);
    }

    private function handleDataTableRequest(Request $request, $requirements, $searchTerm)
    {
        $educators = $this->inviteService->getEligibleEducators($requirements, $searchTerm);
        // Apply DataTable search if provided
        $searchValue = $request->input('search.value');
        if (!empty($searchValue)) {
            $educators = $educators->filter(function ($educator) use ($searchValue) {
                $searchableText = strtolower(
                    $educator->first_name . ' ' . $educator->last_name . ' ' .
                    $educator->email . ' ' .
                    ($educator->locations->city ?? '') . ' ' .
                    ($educator->locations->state ?? '')
                );
                return strpos($searchableText, strtolower($searchValue)) !== false;
            });
        }

        // Calculate pagination
        $start = $request->input('start', 0);
        $length = $request->input('length', 10);
        $totalRecords = $educators->count();

        // Apply pagination
        $paginatedEducators = $educators->slice($start, $length);

        // Format data for DataTable
        $data = $paginatedEducators->map(function ($educator) use ($requirements) {
            $distance = $this->inviteService->passesLocationCheck($educator, $requirements);
            $availability = $this->formatAvailability($educator);
            $gradeLevel = $educator->step3->i_prefer_to_teach ?? '';
            $estimated_cost = $this->inviteService->calculateEducatorRate($requirements, $educator);

            return [
                'id' => $educator->id,
                'name' => $educator->first_name . ' ' . $educator->last_name,
                'email' => $educator->email,
                'location' => ($educator->locations->city ?? '') . ', ' . ($educator->locations->state ?? ''),
                'distance' => $distance . ' miles',
                'grade' => $gradeLevel,
                'availability' => $availability,
                'programs' => '',
                'format' => ucfirst($educator->teach ?? ''),
                'cost' => '$' . number_format($estimated_cost, 2),
            ];
        })->values();

        return response()->json([
            'draw' => intval($request->input('draw')),
            'recordsTotal' => $totalRecords,
            'recordsFiltered' => $totalRecords,
            'data' => $data
        ]);
    }

    private function formatAvailability($educator)
    {
        if (!$educator->availability || $educator->availability->isEmpty()) {
            return 'Not specified';
        }
        $slots = [];
        foreach ($educator->availability as $slot) {
            if (!isset($slot['day_of_week'], $slot['start_time'], $slot['end_time'])) {
                continue;
            }
            $day = ucfirst(strtolower($slot['day_of_week']));
            $startTime = \Carbon\Carbon::parse($slot['start_time'])->format('h:i A');
            $endTime   = \Carbon\Carbon::parse($slot['end_time'])->format('h:i A');

            $slots[] = "{$day} {$startTime} - {$endTime}";
        }
        return !empty($slots) ? implode('<br>', $slots) : 'Not specified';
    }

    public function sendInvite(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'educator_ids'   => 'required|array',
                'educator_ids.*' => 'exists:new_onboarding_instructor,id',
                'requirement_id' => 'required|exists:platform_school_requirements,id',
                'deadline_date'  => 'required|date',
                'deadline_time'  => 'required',
                'timezone'       => 'nullable',
            ]);

            if ($validator->fails()) {
                return response()->json(['errors' => $validator->errors()], 422);
            }

            $data = $validator->validated();
            $invitedBy = session()->get('Adminnewlogin')['id'];
            $requirement = PlatformSchoolRequirements::findOrFail($data['requirement_id']);

            // Convert deadline to MySQL-friendly format
            $deadlineDate = Carbon::createFromFormat('m/d/Y', $data['deadline_date'])->format('Y-m-d');
            $deadlineTime = Carbon::createFromFormat('h:i A', $data['deadline_time'])->format('H:i:s');

            $count = 0;
            foreach ($data['educator_ids'] as $educatorId) {
                // Look for a non-expired invite first
                $existingInvite = PlatformSchoolInvites::where('user_id', $educatorId)
                    ->where('requirement_id', $data['requirement_id'])
                    ->where('status', '!=', 'expired') // only active ones
                    ->first();

                if ($existingInvite) {
                    // Update active invite
                    $existingInvite->update([
                        'school_id'     => $requirement->school_id,
                        'deadline_date' => $deadlineDate,
                        'deadline_time' => $deadlineTime,
                        'invited_by'    => $invitedBy,
                        'updated_at'    => now(),
                    ]);
                } else {
                    // No active invite → create a fresh one
                    PlatformSchoolInvites::create([
                        'user_id'        => $educatorId,
                        'requirement_id' => $data['requirement_id'],
                        'school_id'      => $requirement->school_id,
                        'deadline_date'  => $deadlineDate,
                        'deadline_time'  => $deadlineTime,
                        'invited_by'     => $invitedBy,
                        'status'         => 'pending', // default
                        'created_at'     => now(),
                        'updated_at'     => now(),
                    ]);
                }

                $count++;
            }


            return response()->json([
                'success' => true,
                'message' => $count . ' invite(s) processed successfully.'
            ]);

        } catch (\Exception $e) {
            Log::error('Error sending invite: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'error'   => $e->getMessage(),
                'message' => 'Something went wrong. Please try again later.'
            ], 500);
        }
    }

    public function updateInviteStatus(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'invite_id' => 'required|exists:platform_school_invites,id',
            'status'    => 'required|in:accepted,rejected,withdraw'
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $data = $validator->validated();
        $invite = PlatformSchoolInvites::find($data['invite_id']);
        $invite->status = $data['status'];
        $invite->save();

        return response()->json([
            'success' => true,
            'message' => $data['status'] . ' successfully.'
        ]);
    }

    public function showInviteHistory(Request $request)
    {
        $appliedRequests = SchoolReviewApplicants::all();
        return view('admin.marketplace.manage-requests.appliedrequests', compact('appliedRequests'));
    }

    public function showProgramInvites(Request $request)
    {
        $programInvites = PlatformSchoolInvites::with(['user', 'educator'])->get();
        return view('admin.marketplace.manage-requests.programinvites', compact('programInvites'));
    }

    public function updateAppliedRequestStatus(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'invite_id' => 'required|exists:platform_school_review_applicants,id',
            'status'    => 'required|in:pending,accepted,rejected,withdraw'
        ]);

        if ($validator->fails()) {
            return response()->json(['success' => false, 'errors'  => $validator->errors()], 422);
        }

        $data = $validator->validated();

        $invite = SchoolReviewApplicants::find($data['invite_id']);
        $invite->status = $data['status'];
        $invite->save();

        return response()->json(['success' => true, 'message' => ucfirst($data['status']) . ' successfully.']);
    }

    public function saveProgram(k12ConnectionProgramRequest $request)
    {
        DB::beginTransaction();
        try {

            $data = $request->validated();
            $data = $request->except([
                '_token',
                'notes',
            ]);
            $data["name"] = $request->program_name;
            $data["cbo_id"] = $request->cbo_id;

            $data["end_date"] = date('Y-m-d', strtotime($request->datesingle1));
            $data["status"] = $request->program_status;
            $data["district_id"] = $request->district;
            $data["school_id"] = $request->school_name ?? null;
            $data["program_type_id"] = $request->program_type;
            $data["delivery_type"] = $request->delivery_type;
            $data["capacity"] = $request->capacity;
            $data["has_certifications"] = !empty($request->certifications) ? $request->certifications: 0;
            $data["other_certification_states"] = !empty($request->other_certification_states) ? $request->other_certification_states : null;
            $data["background_checks"] = !empty($request->background_checks) ? $request->background_checks : 0;
            $data["medicalrequirements"] = !empty($request->medicalrequirements) ? $request->medicalrequirements : 0;
            $data["address"] = $request->address;
            $data["city"] = $request->city;
            $data["timezone_id"] = $request->timezone;
            $data["zip_code"] = $request->zipcode;
            $data["country"] = $request->country;

            $data["lat"] = $request->lat;
            $data["lng"] = $request->lng;
            $data['created_by'] = session('Adminnewlogin')['id'];
            if ($request->program_status === 'Publish') {
                if ($request->lat) {
                } else {

                    return response()->json([
                        "success" => false,
                        "message" => 'Please select a valid address from the picker.',
                    ]);
                }
            }

            $data["created_by"] = session()->get('Adminnewlogin')['id'];

            $data["link"] = $request->link;
            $data["joinlink"] = $request->joinlink;
            $is_imported = !empty($request->is_imported) ? $request->is_imported : 0;
            $data["is_imported"] = $is_imported;
            $data["created_at"] = date("Y-m-d H:i:s");
            $data["updated_at"] = date("Y-m-d H:i:s");

            $data["subject_area_id"] = $request->subject_id;
            $data["subject_id"] = $request->sub_subject_id;
            $data["state"] = $request->state;
            if (!empty($request->program_status) && $request->program_status == 'publish') {
                $data["program_status"] = 'active';
            } else {
                $data["program_status"] = 'upcoming';
            }


            // if ($request->is_past) {
            //     $data["is_past"] = $request->is_past;
            //     $data["past_date"] = date('Y-m-d', strtotime($request->past_date));
            //     $data["start_date"] = date('Y-m-d', strtotime($request->past_date));
            // } else {
                $data["start_date"] = date('Y-m-d', strtotime($request->datesingle));
            // }
            $data["grade_id"] = $request->grade;

            $program = k12ConnectionPrograms::create($data);
            $save = $program->id;

            $notes = [];
            if ($request->notes) {
                $notes['program_id'] = $save;
                $notes['note'] = $request->notes;
                $notes['created_by'] = session('Adminnewlogin')['id'];
                k12ConnectionProgramNotes::create($notes);
            }

            if ($is_imported == 0 && $request->filled('no_class_start_date')) {
                $startDates = $request->no_class_start_date;
                $endDates = $request->no_class_end_date;
                $data = [];
                foreach ($startDates as $startIndex => $startDate) {
                    if ($startDate) {
                        $data[] = [
                            'program_id' => $save,
                            'start_date' => Carbon::createFromFormat('m/d/Y', $startDate)->format('Y-m-d'),
                            'end_date' => $endDates[$startIndex] ? Carbon::createFromFormat('m/d/Y', $endDates[$startIndex])->format('Y-m-d') : null,
                        ];
                    }
                }

                if (!empty($data)) {
                    k12ConnectionNoClassDates::insert($data);
                }
            }


            for ($i = 0; $i < count($_POST["job_title"]); $i++) {
                $data1["job_title"] = $_POST["job_title"][$i];
                $data1["email"] = $_POST["cemail"][$i];
                $data1["first_name"] = $_POST["first_name"][$i];
                $data1["last_name"] = $_POST["last_name"][$i];
                $data1["phone"] = $_POST["phone"][$i];
                $data1["program_id"] = $save;
                k12ConnectionContactDetails::insertGetId($data1);
            }
            if ($is_imported == 0) {

                $daysOfWeek = $this->daysOfWeek;

                foreach ($daysOfWeek as $index => $day) {
                    if (isset($request->$day[1]) && isset($request->$day[2])) {
                        $this->insertProgramSchedule($save, ($index + 1), $request->$day[1], $request->$day[2], $request->timezone);
                    }
                }

                $program->createClasses();
            } elseif (request()->file('class_schedule')) {
                Excel::import(new k12ProgramClassesImport($program), request()->file('class_schedule'));
            }


            DB::commit();
            if ($program->program_status == 'Publish') {
                $program->notification_sent = false;
            }


            $route = route('admin.marketplace-getrezoommodel', ['program_id' => $save]);
            return response()->json([
                "success" => true,
                "message" => "Program  successfully created",
                "redirect" => $route,
            ]);
        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                "success" => false,
                "message" =>  $e->getMessage(),
            ]);
        }
    }

    public function getrezoommodel($program_id, Request $request)
    {
        $program = k12ConnectionPrograms::where("id", $program_id)->with('creator')->first();

        $slots = k12ConnectionProgramsSchedule::where("program_id", $program_id)
            ->get();
        $zoom = ZoomModel::select('id', 'account_name')->where("status", 1)
            ->get();
        $view = view("admin.marketplace.zoom.editzoommodel", compact('program_id', 'zoom', 'program', 'slots'))->render();
        return response()->json(['status' => true, 'view' => $view]);
    }

    public function viewprogram($id, $id2, Request $request)
    {
        $id = Crypt::decryptString($id2);

        $program = k12ConnectionPrograms::where("id", $id)->with(['creator', 'programNoteAmounts', 'subject', 'subSubject'])->first();
        // dd($program);
        $user = @$program->mainUser ?? '';
        $user_id = @$user->id ?? '';
        $meetings = $program->programNoteAmounts()->has('note')->with('note')->get();
        // $reimbursements = $program->reimbursements()->where(function($query) {
        //                     $query->where('status', '1')
        //                         ->orWhere('status', '3');
        //                 })
        //                 ->get();

        $program_contact_info = k12ConnectionContactDetails::where("program_id", $id)
            ->get();
        $slots = k12ConnectionProgramsSchedule::where("program_id", $id)
            ->get();

        // $medicalform = BackgroundMedicalModel::where([
        //     "type" => "background_check",
        //     "program_id" => $id,
        // ])->get();

        // $background_medical = BackgroundMedicalModel::where([
        //     "type" => "medical_requirements",
        //     "program_id" => $id,
        // ])->get();
        // $user_references = user_references::where([
        //     "program_id" => $id,
        // ])->get();
        $school = User::where(["users.type" => 6, "users.status" => "1"])->get();
        $state = StateModel::where(["country_id" => "239"])->get();
        $form = document_form::where([
            "status" => 1,
        ])->get();

        $users = OnboardingInstructor::query()
            ->where('new_onboarding_instructor.type', '5')
            ->where('profile_status', '2')->get(['new_onboarding_instructor.id', 'first_name', 'last_name', 'email']);

        $status = null;
        $program_type = k12ConnectionCategorizedData::where('type', 'program_type')->get();
        $states = StateModel::where(["country_id" => "239"])->get();
        // $invites = array();
        // if (request()->segment(2) == 'step9') {

        //     $query = invite_programs::has('program')->with('program', 'user', 'program.school', 'program.schedules','notes.programNote');

        //     $query->where('program_id',  $id);

        //     if ($request->has('expired')) {
        //         $query->where('deadline', '<', now()->toDateTimeString());
        //         $query->where('is_approved', null);
        //         $query->where('tbl_invite_programs.status', null);
        //         $query->whereNotNull('deadline');
        //     } elseif ($request->has('status')) {
        //         $status = $request->filled('status') ? $request->status : null;
        //         $query->where('tbl_invite_programs.status', $status);
        //     }
        //     $query->where('is_auto_invite', 0);
        //     // $query->groupBy('user_id');

        //     $query->whereNotNull('user_id');
        //     $invites = $query->orderBy('tbl_invite_programs.id', 'DESC')->get();
        // }

        // $programBackReq = ProgramRequirementsModel::where([
        //     "req_type" => "background_check",
        //     "program_id" => $id,
        // ])->get();


        // $programMedReq = ProgramRequirementsModel::where([
        //     "req_type" => "medical_requirements",
        //     "program_id" => $id,
        // ])->get();


        return view("admin.marketplace.programs.program-details", [

            "user" => $user,
            "user_references" => '',
            "users" => $users,
            "medicalform" => '',
            "background_medical" => '',
            "application_id" => $user_id,
            "program" => $program,
            "program_contact_info" => $program_contact_info,
            "slots" => $slots,
            "meetings" => $meetings,
            "reimbursements" => '',
            "school" => $school,
            "state" => $state,
            "form" => $form,
            "status" => $status,
            "invites" => '',
            "programBackReq" => '',
            "programMedReq" => '',
            "program_type" => $program_type,
            "states" => $states,

        ]);
    }

    public function requirementsList(Request $request)
    {
        if ($request->ajax()) {
            $schoolId = $request->school;
            $query = PlatformSchoolRequirements::with(['school', 'addedBy', 'invites', 'reviewApplicants.user.step2.education', 'reviewApplicants.user.step3.subjects', 'classType']);
            $query->whereHas('school', function ($q) use ($schoolId) {
                $q->where('id', $schoolId);
            });
            $requirements = $query->OrderBy('id', 'DESC')->get();

            $processedRequirements = $requirements->map(function ($requirement) {
                return $this->processRequirementData($requirement);
            });
            $view = view("admin.marketplace.requirements.requirements-list", compact('requirements', 'processedRequirements'))->render();
            return response()->json(['success' => true, 'view' => $view]);
        }

        $requirements = PlatformSchoolRequirements::with(['invites', 'reviewApplicants.user.step2.education', 'reviewApplicants.user.step3.subjects', 'classType'])
        ->leftJoin('users as u', 'platform_school_requirements.added_by', '=', 'u.id')
        ->addSelect([
            'platform_school_requirements.*',
            FacadesDB::raw("CONCAT(u.first_name, ' ', u.last_name) as created_by")
        ])
        ->orderByRaw("
            CASE
                WHEN platform_school_requirements.status IN ('filled', 'closed', 'archived') THEN 1
                ELSE 0
            END ASC
        ")->orderBy('platform_school_requirements.id', 'DESC')->get();

        $processedRequirements = $requirements->map(function ($requirement) {
            return $this->processRequirementData($requirement);
        });
        return view('admin.marketplace.requirements.requirements-list', compact('requirements', 'processedRequirements'));
    }

    /**
     * Process requirement data for enhanced display
     */
    private function processRequirementData($requirement)
    {
        // Get invites count
        $totalInvites = $requirement->invites->count();
        $acceptedInvites = $requirement->invites->where('status', 'accepted')->count();

        // Calculate potential applicants (matched educators)
        $potentialApplicants = $this->calculateMatchedEducators($requirement);

        // Check ESOL requirement
        $esolRequired = false;
        if ($requirement->language_requirements) {
            $languages = explode(',', $requirement->language_requirements);
            $esolRequired = in_array('ESOL', $languages) || in_array('ESL', $languages);
        }

        return [
            'id' => $requirement->id,
            'delivery_mode' => $requirement->delivery_mode,
            'grade_levels' => rtrim(gradeLevel($requirement->grade_levels_id), ','),
            'subject_area' => v1SubjectAreaName($requirement->subject_area_id),
            'subject' => v1SubjectName($requirement->subject_id),
            'class_type' => $requirement->classType ? $requirement->classType->name : classType($requirement->class_type),
            'total_invites' => $totalInvites,
            'accepted_invites' => $acceptedInvites,
            'potential_applicants' => $potentialApplicants,
            'special_education_required' => $requirement->special_education_certificate == 'yes',
            'esol_required' => $esolRequired,
            'curriculum_provided' => $requirement->will_follow_provided_curriculum == 'yes',
            'status' => $requirement->status,
            'encrypted_id' => encrypt_str($requirement->id),
            'school_name' => schoolusername($requirement->school_id),
            'capacity' => $requirement->capacity,
            'start_date' => $requirement->start_date,
            'end_date' => $requirement->end_date,
            'created_by' => $requirement->created_by ?? 'Superadmin',
            'updated_at' => $requirement->updated_at
        ];
    }

    /**
     * Calculate matched educators for a requirement
     */
    private function calculateMatchedEducators($requirement)
    {
        $potentialApplicants = 0;
        $applicants = $requirement->reviewApplicants;

        foreach ($applicants as $applicant) {
            if ($applicant->user) {
                $educator = $applicant->user;

                // Check grade level match
                $educatorGradeLevels = explode(',', $educator->step3->i_prefer_to_teach ?? '');
                $requirementGradeLevels = explode(',', $requirement->grade_levels_id ?? '');
                $gradeMatch = count(array_intersect($educatorGradeLevels, $requirementGradeLevels)) > 0;

                // Check subject match
                $educatorSubjects = $educator->step3->subjects->pluck('sub_subject')->toArray();
                $requirementSubjects = explode(',', $requirement->subject_id ?? '');
                $subjectMatch = count(array_intersect($educatorSubjects, $requirementSubjects)) > 0;

                // Check delivery mode compatibility (assuming all educators can handle both modes)
                $deliveryMatch = true;

                // Check certification/state match if required
                $certificationMatch = true;
                if (!empty($requirement->certifications_valid)) {
                    $requirementStates = explode(',', $requirement->certifications_valid);
                    $educatorStates = [];

                    // Get educator's certification states from step2.education relationship
                    if ($educator->step2 && $educator->step2->education) {
                        foreach ($educator->step2->education as $certification) {
                            if (!empty($certification->states)) {
                                $states = json_decode($certification->states, true);
                                if (is_array($states)) {
                                    $educatorStates = array_merge($educatorStates, $states);
                                }
                            }
                        }
                    }

                    // Also check teaching_certification_states from step2 directly
                    if ($educator->step2 && !empty($educator->step2->teaching_certification_states)) {
                        $additionalStates = explode(',', $educator->step2->teaching_certification_states);
                        $educatorStates = array_merge($educatorStates, $additionalStates);
                    }

                    $educatorStates = array_unique($educatorStates);
                    $certificationMatch = count(array_intersect($educatorStates, $requirementStates)) > 0;
                }

                if ($gradeMatch && $subjectMatch && $deliveryMatch && $certificationMatch) {
                    $potentialApplicants++;
                }
            }
        }

        return $potentialApplicants;
    }

    // ***************View-Requirements***************
    public function viewRequirements($id)
    {
        $id = decrypt_str($id);
        $data = PlatformSchoolRequirements::with([
            'school',
            'subject',
            'proctor',
            'meetingLinks',
            'reviewApplicants.user.step1',
            'reviewApplicants.user.step2.education',
            'reviewApplicants.user.step3.subjects',
            'reviewApplicants.user.step5',
            'reviewApplicants.user.shortList',
            'reviewApplicants.requirement',
            'contracts.versions.createdBy',
            'contracts.versions.updatedBy',
            'contracts.createdBy',
            'contracts.updatedBy'
        ])->find($id);
        // Get School Contract content
        $schoolContractContent = SettingTermsModel::get_single_record([
            'type' => 'School Contract',
            'locale' => 'en'
        ]);

        // Build proposal tab rows
        $allRows = [];
        $shortlistedRows = [];
        $archivedRows = [];

        if ($data && $data->reviewApplicants) {
            $requirementId = $data->id;
            foreach ($data->reviewApplicants as $applicant) {
                $user = $applicant->user;
                if (!$user) { continue; }

                // Pick the first state
                $state = $user->state ?? '-';

                // Get all subject area and names
                $subjectAreaNames = [];
                $subjectNames = [];
                if ($user->step3 && $user->step3->subjects) {
                    foreach ($user->step3->subjects as $subj) {
                        if (!empty($subj->subject)) { $subjectAreaNames[] = v1SubjectAreaName($subj->subject); }
                        if (!empty($subj->sub_subject)) { $subjectNames[] = v1SubjectName($subj->sub_subject); }
                    }
                }
                $subjectArea = implode(', ', array_values(array_unique(array_filter($subjectAreaNames))));
                $subjectName = implode(', ', array_values(array_unique(array_filter($subjectNames))));
                $subjectArea = $subjectArea !== '' ? $subjectArea : '-';
                $subjectName = $subjectName !== '' ? $subjectName : '-';

                // Get grade levels
                $gradeLevels = '-';
                if ($user->step3 && !empty($user->step3->i_prefer_to_teach)) {
                    $gradeLevels = rtrim(gradeLevel($user->step3->i_prefer_to_teach), ',');
                }

                // Get certified states
                $certStatesArr = [];
                $hasCertification = false;
                if ($user->step2 && $user->step2->education) {
                    $hasCertification = $user->step2->education->count() > 0;
                    foreach ($user->step2->education as $edu) {
                        if (!empty($edu->states)) {
                            $decoded = is_array($edu->states) ? $edu->states : json_decode($edu->states, true);
                            if (is_array($decoded)) { $certStatesArr = array_merge($certStatesArr, $decoded); }
                        }
                    }
                }
                $certifiedStates = implode(', ', array_values(array_unique(array_filter($certStatesArr))));
                $certifiedStates = $certifiedStates !== '' ? $certifiedStates : '-';

                // Get shortlist status
                $shortlistStatus = null;
                if ($user->shortList) {
                    foreach ($user->shortList as $sl) {
                        if ($sl->requirement_id == $requirementId) { $shortlistStatus = is_null($sl->status) ? null : (int) $sl->status; break; }
                    }
                }

                // Get profile description
                $profileDescription = ($user->step5 && !empty($user->step5->description)) ? $user->step5->description : '-';

                // Get updated by
                if ($user->shortList) {
                    foreach ($user->shortList as $sl) {
                        if ($sl->requirement_id == $requirementId) { $updatedBy = $sl->updated_by ?? '-'; break; }
                    }
                }

                // Build row
                $row = [
                    'user_id' => (int) ($user->id ?? 0),
                    'requirement_id' => (int) $requirementId,
                    'name' => trim(($user->first_name ?? '') . ' ' . ($user->last_name ?? '')),
                    'state' => $state,
                    'subject_area' => $subjectArea,
                    'subject_name' => $subjectName,
                    'grade_levels' => $gradeLevels,
                    'certified_states' => $certifiedStates,
                    'has_certification' => (bool) $hasCertification,
                    'shortlist_status' => $shortlistStatus,
                    'profile_description' => $profileDescription,
                    'updated_by' => $updatedBy,
                ];

                // Add to all rows and respective shortlist status rows
                $allRows[] = $row;
                if ($shortlistStatus === 1) { $shortlistedRows[] = $row; }
                if ($shortlistStatus === 0) { $archivedRows[] = $row; }
            }
        }

        // Prepare contract data with versions
        $contractVersions = [];
        $contractData = null;

        if ($data && $data->contracts) {
            foreach ($data->contracts as $contract) {
                // Store contract data (should be only one contract per requirement)
                if (!$contractData) {
                    $contractData = [
                        'id' => $contract->id,
                        'legal_first_name' => $contract->legal_first_name,
                        'legal_last_name' => $contract->legal_last_name,
                        'phone' => $contract->phone,
                        'email' => $contract->email,
                        'job_title' => $contract->job_title,
                        'address' => $contract->address,
                        'client_name' => $contract->client_name,
                        'status' => $contract->status,
                    ];
                }

                // Collect all versions for this contract
                foreach ($contract->versions as $version) {
                    $contractVersions[] = [
                        'id' => $version->id,
                        'contract_id' => $contract->id,
                        'legal_first_name' => $contract->legal_first_name,
                        'legal_last_name' => $contract->legal_last_name,
                        'phone' => $contract->phone,
                        'email' => $contract->email,
                        'version_number' => $version->version_number,
                        'file_url' => $version->file_url,
                        'notes' => $version->notes,
                        'created_at' => $version->created_at,
                        'updated_at' => $version->updated_at,
                        'created_by' => $version->createdBy ? ($version->createdBy->first_name . ' ' . $version->createdBy->last_name) : 'N/A',
                        'updated_by' => $version->updatedBy ? ($version->updatedBy->first_name . ' ' . $version->updatedBy->last_name) : 'N/A',
                    ];
                }
            }
        }

        return view('admin.marketplace.requirements.requirements-details', compact('data', 'allRows', 'shortlistedRows', 'archivedRows', 'contractVersions', 'contractData', 'schoolContractContent'));
    }
    // ***************View-Requirements***************

    // ***************Update Contract Status***************
    public function updateStatus(Request $request, $id)
    {
        try {
            // Get admin user from session
            $adminSession = session('Adminnewlogin');
            if (!$adminSession || !isset($adminSession['id'])) {
                return ApiResponse::error('Unauthorized access', 401);
            }

            $contract = SchoolRequirementContract::find($id);
            if (!$contract) {
                return ApiResponse::error('Contract not found', 404);
            }

            // Validate request
            $validator = Validator::make($request->all(), [
                'status' => 'required|in:draft,pending_approval,in_review,approved,on_hold,cancelled,completed',
            ], [
                'status.required' => 'Status is required.',
                'status.in' => 'Invalid status value.',
            ]);

            if ($validator->fails()) {
                return ApiResponse::error('Validation failed', 422, $validator->errors());
            }

            // Update contract status
            $contract->update([
                'status' => $request->status,
                'updated_by_id' => $adminSession['id'],
                'updated_by_type' => 'App\User', // Admin user type
            ]);

            // Load relationships for response
            $contract->load(['requirement.school:id,school_name', 'requirement.subject.subjectArea', 'requirement.classType', 'createdBy', 'updatedBy', 'versions']);

            return ApiResponse::success([
                'contract' => $contract,
                'message' => 'Contract status updated successfully'
            ], "Contract status updated to " . ucfirst(str_replace('_', ' ', $request->status)));

        } catch (Exception $e) {
            Log::error('Error updating contract status: ' . $e->getMessage(), [
                'admin_id' => $adminSession['id'] ?? 'unknown',
                'contract_id' => $id,
                'trace' => $e->getTraceAsString()
            ]);
            return ApiResponse::error('Failed to update contract status. Please try again later.', 500);
        }
    }
    // ***************Update Contract Status***************

    // ***************Update Contract Information***************
    public function uploadVersion(Request $request, $id)
    {
        FacadesDB::beginTransaction();
        try {
            // Get admin user from session
            $adminSession = session('Adminnewlogin');
            if (!$adminSession || !isset($adminSession['id'])) {
                return ApiResponse::error('Unauthorized access', 401);
            }

            $contract = SchoolRequirementContract::find($id);
            if (!$contract) {
                return ApiResponse::error('Contract not found', 404);
            }

            // Validate request
            $validator = Validator::make($request->all(), [
                'document' => 'required|file|mimes:pdf,doc,docx,txt|max:20480', // 20MB max
                'version_number' => 'nullable|string|max:50',
                'notes' => 'nullable|string|max:2000',
            ], [
                'document.required' => 'Document file is required.',
                'document.mimes' => 'Document must be a PDF, DOC, DOCX, or TXT file.',
                'document.max' => 'Document size must not exceed 20MB.',
            ]);

            if ($validator->fails()) {
                return ApiResponse::error('Validation failed', 422, $validator->errors());
            }

            // Handle file upload first
            $fileUrl = null;
            if ($request->hasFile('document')) {
                $file = $request->file('document');

                // Validate file
                if (!$file->isValid()) {
                    throw new Exception('Invalid file upload');
                }

                try {
                    $originalName = pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME);
                    $extension = $file->getClientOriginalExtension();
                    $sanitizedName = preg_replace('/[^a-zA-Z0-9_-]/', '_', $originalName);
                    $name = time() . '-' . $sanitizedName . '.' . $extension;
                    $filename = 'uploads/school/contracts/' . $name;

                    // Upload to S3
                    $uploadResult = uploads3image($filename, $file);

                    if ($uploadResult) {
                        $fileUrl = $filename;
                        Log::info('Document uploaded successfully', [
                            'filename' => $filename,
                            'original_name' => $file->getClientOriginalName(),
                            'size' => $file->getSize(),
                            'contract_id' => $contract->id
                        ]);
                    } else {
                        throw new Exception('Failed to upload file to S3');
                    }
                } catch (Exception $uploadException) {
                    Log::error('File upload failed: ' . $uploadException->getMessage(), [
                        'contract_id' => $contract->id,
                        'admin_id' => $adminSession['id'],
                        'filename' => $filename ?? 'unknown'
                    ]);
                    throw new Exception('File upload failed. Please try again.');
                }
            }

            // Get next version number if not provided
            $versionNumber = $request->input('version_number');
            if (!$versionNumber) {
                $lastVersion = $contract->versions()->orderBy('created_at', 'desc')->first();
                if ($lastVersion) {
                    // Extract version number and increment
                    $versionParts = explode('.', str_replace('v', '', $lastVersion->version_number));
                    $majorVersion = intval($versionParts[0] ?? 1);
                    $minorVersion = intval($versionParts[1] ?? 0);
                    $versionNumber = 'v' . $majorVersion . '.' . ($minorVersion + 1);
                } else {
                    $versionNumber = 'v1.0';
                }
            }

            // Create new version
            $versionData = [
                'school_requirement_contract_id' => $contract->id,
                'file_url' => $fileUrl,
                'version_number' => $versionNumber,
                'notes' => $request->input('notes', 'New contract version'),
                'created_by_id' => $adminSession['id'],
                'created_by_type' => 'App\User', // Admin user type
                'updated_by_id' => $adminSession['id'],
                'updated_by_type' => 'App\User', // Admin user type
            ];

            // Create version record
            $version = SchoolRequirementContractVersion::create($versionData);

            FacadesDB::commit();

            // Load relationships for response
            $contract->load(['requirement.school:id,school_name', 'requirement.subject.subjectArea', 'requirement.classType', 'createdBy', 'updatedBy', 'versions']);

            return ApiResponse::success([
                'contract' => $contract,
                'version' => $version,
                'message' => 'Contract version created successfully',
                'has_document' => !is_null($fileUrl)
            ], "Contract version created successfully");
        } catch (Exception $e) {
            FacadesDB::rollBack();
            Log::error('Error uploading contract version: ' . $e->getMessage(), [
                'admin_id' => $adminSession['id'] ?? 'unknown',
                'contract_id' => $id,
                'trace' => $e->getTraceAsString()
            ]);
            return ApiResponse::error('Failed to upload contract version. Please try again later.', 500);
        }
    }
    // ***************Update Contract Information***************

    // ***************Like/Dislike Applicant***************
    public function shortlistApplicant(Request $request) {
        try {
            // Validate request
            $request->validate([
                'requirement_id' => 'required|integer|exists:platform_school_requirements,id',
                'user_id' => 'required|integer|exists:new_onboarding_instructor,id',
                'status' => 'nullable|integer|in:0,1'
            ]);
            $admin = session()->get('Adminnewlogin');
            if ($admin) {
                $updatedBy = 'Whizara Admin';
            }
            $shortlistApplicant = ShortlistInstructorModel::where('requirement_id', $request->requirement_id)->where('user_id', $request->user_id)->first();
            if (!$shortlistApplicant) {
                return response()->json(['status' => false, 'message' => 'Shortlist not found']);
            }
            $shortlistApplicant->update(['status' => $request->status, 'updated_by' => $updatedBy]);
            return response()->json(['status' => true, 'message' => 'Shortlist status updated successfully']);
        } catch (ValidationException $e) {
            Log::error('Validation failed: ' . $e->getMessage());
            return response()->json(['status' => false, 'message' => 'Validation failed', 'errors' => $e->errors()], 422);
        } catch (Exception $e) {
            Log::error('Error shortlisting applicant: ' . $e->getMessage());
            return response()->json(['status' => false, 'message' => 'Something went wrong', 'error' => $e->getMessage()], 500);
        }
    }
    // ***************Like/Dislike Applicant***************

    // ***************Add-Requirements***************
    public function addRequirements()
    {
        $schools = Schools::all();
        $subjects = Subject::all();
        $subjectArea = SubjectArea::with('subjects')->get();
        $program_type = k12ConnectionCategorizedData::where('type', 'program_type')->get();
        $grades = Classes::all();
        $timezone = k12ConnectionCategorizedData::where('type', 'timezone')->get();
        $allLanguages = FacadesDB::table('school_management_setting')->where('type', 'language')->first();
        $languages = json_decode($allLanguages->value);
        $cbos = cbo::pluck('name', 'id');
        return view('admin.marketplace.requirements.add-requirements', compact('schools', 'subjects', 'subjectArea', 'program_type', 'grades', 'languages', 'cbos', 'timezone'));
    }
    // ***************Add-Requirements***************

    // ***************Calculate-Budget***************
    public function calculatePlatformBudget($id)
    {
        try {
            $subjectBudget = SubjectBudget::where('subject_id', $id)->first();
            if (!$subjectBudget) {
                return response()->json(['status' => false, 'message' => 'Subject budget not found']);
            }
            $totalHours = request()->get('total_hours');
            $provideCurriculum = request()->get('provide_curriculum');
            $qualification = request()->get('qualification');
            $experience = request()->get('experience');

            // Step 1: Curriculum Increment
            $curriculumInc = $provideCurriculum ? ($subjectBudget->curriculum_inc ?? 0) : 0;

            // Step 2: Qualification Increment
            $qualificationInc = 0;
            if ($qualification == "Master’s") {
                $qualificationInc = $subjectBudget->masters_inc ?? 0;
            } elseif ($qualification == "Doctorate") {
                $qualificationInc = $subjectBudget->doctorate_inc ?? 0;
            }

            // Step 3: Calculate for each experience range
            $basePay = $subjectBudget->base_pay_0_3 ?? 0;

            // Calculate for each experience range
            $budgets = [
                '3'  => ($curriculumInc + $qualificationInc + $basePay) * $totalHours,
                '6'  => ($curriculumInc + $qualificationInc + $basePay + ($subjectBudget->pay_3_6 ?? 0)) * $totalHours,
                '10' => ($curriculumInc + $qualificationInc + $basePay + ($subjectBudget->pay_6_10 ?? 0)) * $totalHours,
                '11' => ($curriculumInc + $qualificationInc + $basePay + ($subjectBudget->pay_10_plus ?? 0)) * $totalHours,
            ];

            return response()->json(['status' => true, 'subjectBudget' => $subjectBudget, 'budgets' => $budgets]);
        } catch (Exception $e) {
            Log::info($e->getMessage());
            return response()->json(['status' => false, 'message' => $e->getMessage()]);
        }
    }
    // ***************Calculate-Budget***************

    // ***************Fetch School Data***************
    public function fetchSchoolData(Request $request)
    {
        $schoolName = $request->query('name');
        $response = Http::get('https://app.whizara.com/node/nces/schools', ['SCH_NAME' => $schoolName]);
        return $response->json();
    }
    // ***************Fetch School Data***************

    // ***************Save-Requirements***************
    public function saveRequirements(Request $request)
    {
        try {
            $user = session()->get('Adminnewlogin');
            // Validate Request
            $validated = $request->validate([
                'school'                        => 'required',
                'district_school_id'            => 'required',
                'delivery_mode'                 => 'required',
                'courses'                       => 'required',
                'class_type'                    => 'required',
                'gradeLevels'                   => 'required|array',
                'expected_class_size'           => 'required',
                'address'                       => 'required_if:delivery_mode,in-person',
                'no_instrtructional_days'       => 'required',
                'schedule_type'                 => 'required',
                'other_sch_class_details'       => 'required_if:schedule_type,other',
                'class_start_date'              => 'required|date',
                'class_end_date'                => 'required|date',
                'will_choose_educator'          => 'required_if:delivery_mode,online',
                'educator_profile'              => 'required_if:delivery_mode,online',
                'special_education_certificate' => 'required_if:delivery_mode,online',
                'language_requirements'         => 'required|array',
            ]);

            $noClassDates = [];
            if ($request->filled('no_class_start_date')) {
                $startDates = $request->no_class_start_date;
                $endDates = $request->no_class_end_date;
                foreach ($startDates as $startIndex => $startDate) {
                    if ($startDate) {
                        $noClassDates[] = [
                            'start_date' => date('Y-m-d', strtotime($startDate)),
                            'end_date' => $endDates[$startIndex] ? date('Y-m-d', strtotime($endDates[$startIndex])) : null,
                        ];
                    }
                }
            }

            // Base Data
            $data = [
                'status'                            => $request->status,
                'is_visible_to_school'              => $request->is_visible,
                'requirement_title'                 => 'class',
                'school_id'                         => $request->school,
                'district_school_id'                => $request->district_school_id ?? null,
                'delivery_mode'                     => $request->delivery_mode,
                'subject_area_id'                   => v1SubjectId($request->courses),
                'subject_id'                        => $request->courses,
                'class_type'                        => $request->class_type,
                'grade_levels_id'                   => implode(',', $request->gradeLevels),
                'capacity'                          => $request->expected_class_size,
                'cbo_id'                            => $request->cbo,
                'address'                           => $request->address,
                'city'                              => $request->city,
                'state'                             => $request->state,
                'country'                           => $request->country,
                'time_zone'                          => $request->timezone,
                'lat'                               => $request->lat,
                'lng'                               => $request->lng,
                'description'                       => $request->requirement_description,
                'requirement_tags'                  => json_encode($request->tags),
                'no_instrtructional_days'           => $request->no_instrtructional_days,
                'class_duration'                    => $request->class_duration,
                'no_non_instructional_hr'           => $request->no_non_instructional_hr,
                'schedule_type'                     => $request->schedule_type,
                'no_class_dates'                    => json_encode($noClassDates),
                'other_sch_class_details'           => $request->other_sch_class_details ?? null,
                'start_date'                        => date('Y-m-d', strtotime($request->class_start_date)),
                'end_date'                          => date('Y-m-d', strtotime($request->class_end_date)),
                'experience'                        => $request->experience,
                'qualifications'                    => $request->qualification,
                'total_budget'                      => $request->total_budget,
                'benefits'                          => $request->benefits ?? null,
                'will_choose_educator'              => $request->will_choose_educator ?? 'whizara',
                'credential_check'                  => $request->educator_profile ?? 'non_credentialed',
                'special_education_certificate'     => $request->special_education_certificate ?? 'no',
                'language_requirements'             => !empty($request->language_requirements) ? implode(',', $request->language_requirements) : null,
                'other_requirements'                => $request->other_requirements ?? null,
                'will_follow_provided_curriculum'   => $request->will_follow_provided_curriculum ? 'yes' : 'no',
                'provide_schedule_access'           => $request->provide_schedule_access ? 'yes' : 'no',
                'added_by'                          => $user['id'],
                'created_at'                        => now(),
                'updated_at'                        => now(),
            ];

            $days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
            $scheduleDays = [];
            foreach ($days as $day) {
                if (!empty($request->$day) && is_array($request->$day)) {
                    foreach ($request->$day as $entry) {
                        if (!empty($entry['day']) && !empty($entry['start_time']) && !empty($entry['end_time'])) {
                            $scheduleDays[] = [
                                'day'        => $entry['day'],
                                'start_time' => $entry['start_time'],
                                'end_time'   => $entry['end_time'],
                            ];
                        }
                    }
                }
            }
            if ($request->schedule_type == 'regular') {
                $data['regular_days'] = json_encode($scheduleDays);
            } else {
                $data['schedule_1_days'] = json_encode($scheduleDays);
            }


            // File Uploads (Reusable Helper)
            $uploadFile = function ($file, $folder = 'uploads/') {
                $name = time() . '-' . pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME) . '.' . $file->getClientOriginalExtension();
                $path = $folder . $name;
                uploads3image($path, $file);
                return $path;
            };

            if ($request->hasFile('upload_school_calendar')) {
                $filePath = $uploadFile($request->file('upload_school_calendar'));
                $data['sch_cal_screenshot'] = $filePath;
                PlatformSchoolCalendersModel::create([
                    'calender_url'     => $filePath,
                    'school_id'        => auth()->id(),
                    // 'district_id'      => auth()->user()->district,
                    'type'             => 'school',
                    'lastUpdatedFrom'  => 'Requirement-' . $request->id,
                ]);
            }

            if ($request->hasFile('upload_district_calendar')) {
                $filePath = $uploadFile($request->file('upload_district_calendar'));
                $data['district_cal_screenshot'] = $filePath;
                PlatformSchoolCalendersModel::create([
                    'calender_url'     => $filePath,
                    'school_id'        => auth()->id(),
                    // 'district_id'      => auth()->user()->district,
                    'type'             => 'district',
                    'lastUpdatedFrom'  => 'Requirement-' . $request->id,
                ]);
            }

            if ($request->hasFile('upload_teacher_schedule_screenshot')) {
                $filePath = $uploadFile($request->file('upload_teacher_schedule_screenshot'));
                $data['teacher_schedule_screenshot'] = $filePath;
            }
            // Final Save
            $requirement = PlatformSchoolRequirements::create($data);
            return response()->json(['success' => true, 'message' => 'Requirements saved successfully', 'data' => $requirement]);
        } catch (ValidationException $e) {
            return response()->json(['success' => false, 'message' => 'Validation error.', 'errors'  => $e->errors()], 422);
        } catch (Exception $e) {
            Log::error('Error saving requirements: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Something went wrong. Please try again later.'], 500);
        }
    }
    // ***************Save-Requirements***************

    // ***************Edit-Requirements***************
    public function editRequirements($id)
    {
        $id = decrypt_str($id);
        $requirement = PlatformSchoolRequirements::with(['school', 'subject', 'proctor', 'meetingLinks'])->find($id);
        $existingProctors = PlatformSchoolProctor::all();
        $schools = Schools::all();
        $subjects = Subject::all();
        $subjectArea = SubjectArea::with('subjects')->get();
        $program_type = k12ConnectionCategorizedData::where('type', 'program_type')->get();
        $timezone = k12ConnectionCategorizedData::where('type', 'timezone')->get();
        $grades = Classes::all();
        $allLanguages = FacadesDB::table('school_management_setting')->where('type', 'language')->first();
        $languages = json_decode($allLanguages->value);
        $cbos = cbo::pluck('name', 'id');
        return view('admin.marketplace.requirements.edit-requirements', compact('requirement', 'existingProctors', 'schools', 'subjects', 'subjectArea', 'program_type', 'grades', 'languages', 'cbos', 'timezone'));
    }
    // ***************Edit-Requirements***************

    // ***************Update-Requirements***************
    public function updateRequirements(Request $request, $id)
    {
        try {
            $user = session()->get('Adminnewlogin');
            $validated = $request->validate([
                'school'                        => 'required',
                'district_school_id'            => 'required',
                'delivery_mode'                 => 'required',
                'courses'                       => 'required',
                'class_type'                    => 'required',
                'gradeLevels'                   => 'required|array',
                'expected_class_size'           => 'required',
                'address'                       => 'required_if:delivery_mode,in-person',
                'no_instrtructional_days'       => 'required',
                'schedule_type'                 => 'required',
                'other_sch_class_details'       => 'required_if:schedule_type,other',
                'class_start_date'              => 'required|date',
                'class_end_date'                => 'required|date',
                'will_choose_educator'          => 'required_if:delivery_mode,online',
                'educator_profile'              => 'required_if:delivery_mode,online',
                'special_education_certificate' => 'required_if:delivery_mode,online',
                'language_requirements'         => 'required|array',
            ]);

            $requirement = PlatformSchoolRequirements::findOrFail($id);

            $noClassDates = [];
            if ($request->filled('no_class_start_date')) {
                $startDates = $request->no_class_start_date;
                $endDates = $request->no_class_end_date;
                foreach ($startDates as $startIndex => $startDate) {
                    if ($startDate) {
                        $noClassDates[] = [
                            'start_date' => date('Y-m-d', strtotime($startDate)),
                            'end_date' => $endDates[$startIndex] ? date('Y-m-d', strtotime($endDates[$startIndex])) : null,
                        ];
                    }
                }
            }

            $data = [
                'status'                            => $request->status,
                'is_visible_to_school'              => $request->is_visible,
                'requirement_title'                 => 'class',
                'school_id'                         => $request->school,
                'district_school_id'                => $request->district_school_id ?? null,
                'delivery_mode'                     => $request->delivery_mode,
                'subject_area_id'                   => v1SubjectId($request->courses),
                'subject_id'                        => $request->courses,
                'class_type'                        => $request->class_type,
                'grade_levels_id'                   => implode(',', $request->gradeLevels),
                'capacity'                          => $request->expected_class_size,
                'cbo_id'                            => $request->cbo,
                'address'                           => $request->address,
                'city'                              => $request->city,
                'state'                             => $request->state,
                'country'                           => $request->country,
                'time_zone'                          => $request->timezone,
                'lat'                               => $request->lat,
                'lng'                               => $request->lng,
                'description'                       => $request->requirement_description,
                'requirement_tags'                  => json_encode($request->tags),
                'no_instrtructional_days'           => $request->no_instrtructional_days,
                'class_duration'                    => $request->class_duration,
                'no_non_instructional_hr'           => $request->no_non_instructional_hr,
                'schedule_type'                     => $request->schedule_type,
                'no_class_dates'                    => json_encode($noClassDates),
                'other_sch_class_details'           => $request->other_sch_class_details ?? null,
                'start_date'                        => date('Y-m-d', strtotime($request->class_start_date)),
                'end_date'                          => date('Y-m-d', strtotime($request->class_end_date)),
                'experience'                        => $request->experience,
                'qualifications'                    => $request->qualification,
                'total_budget'                      => $request->total_budget,
                'benefits'                          => $request->benefits ?? null,
                'will_choose_educator'              => $request->will_choose_educator,
                'credential_check'                  => $request->educator_profile,
                'special_education_certificate'     => $request->special_education_certificate,
                'language_requirements'             => !empty($request->language_requirements) ? implode(',', $request->language_requirements) : null,
                'other_requirements'                => $request->other_requirements ?? null,
                'will_follow_provided_curriculum'   => $request->will_follow_provided_curriculum ? 'yes' : 'no',
                'provide_schedule_access'           => $request->provide_schedule_access ? 'yes' : 'no',
                'added_by'                          => $user['id'],
                'updated_at'                        => now(),
            ];

            // Schedule Data
            $days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
            $scheduleDays = [];
            foreach ($days as $day) {
                if (!empty($request->$day) && is_array($request->$day)) {
                    foreach ($request->$day as $entry) {
                        if (!empty($entry['day']) && !empty($entry['start_time']) && !empty($entry['end_time'])) {
                            $scheduleDays[] = [
                                'day'        => $entry['day'],
                                'start_time' => $entry['start_time'],
                                'end_time'   => $entry['end_time'],
                            ];
                        }
                    }
                }
            }
            if ($request->schedule_type == 'regular') {
                $data['regular_days'] = json_encode($scheduleDays);
            } else {
                $data['regular_days'] = null;
                $data['schedule_1_days'] = json_encode($scheduleDays);
            }

            $uploadFile = function ($file, $folder = 'uploads/') {
                $name = time() . '-' . pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME) . '.' . $file->getClientOriginalExtension();
                $path = $folder . $name;
                uploads3image($path, $file);
                return $path;
            };
            if ($request->hasFile('upload_school_calendar')) {
                $filePath = $uploadFile($request->file('upload_school_calendar'));
                $data['sch_cal_screenshot'] = $filePath;
                PlatformSchoolCalendersModel::create([
                    'calender_url'     => $filePath,
                    'school_id'        => auth()->id(),
                    // 'district_id'      => auth()->user()->district,
                    'type'             => 'school',
                    'lastUpdatedFrom'  => 'Requirement-' . $id,
                ]);
            }
            if ($request->hasFile('upload_district_calendar')) {
                $filePath = $uploadFile($request->file('upload_district_calendar'));
                $data['district_cal_screenshot'] = $filePath;
                PlatformSchoolCalendersModel::create([
                    'calender_url'     => $filePath,
                    'school_id'        => auth()->id(),
                    // 'district_id'      => auth()->user()->district,
                    'type'             => 'district',
                    'lastUpdatedFrom'  => 'Requirement-' . $id,
                ]);
            }
            if ($request->hasFile('upload_teacher_schedule_screenshot')) {
                $filePath = $uploadFile($request->file('upload_teacher_schedule_screenshot'));
                $data['teacher_schedule_screenshot'] = $filePath;
            }

            // Handle Proctor Info
            if ($request->proctor_mode === 'new' && $request->filled(['proctor_fname', 'proctor_lname', 'proctor_email', 'proctor_phone'])) {
                $proctorFullName = trim($request->proctor_fname . ' ' . $request->proctor_lname);
                $proctorData = PlatformSchoolProctor::updateOrCreate(
                    [
                        'school_id' => $requirement->school_id,
                        'email' => $request->proctor_email,
                        'proctor_name' => $proctorFullName,
                        'phone'        => $request->proctor_phone,
                    ]
                );
                $data['proctor_id'] = $proctorData->id ?? null;
            } elseif ($request->proctor_mode === 'existing') {
                $data['proctor_id'] = $request->existing_proctor;
            }

            // Handle Meeting Link
            if (!empty($request->link)) {
                k12ConnectionMeetingLinks::updateOrCreate(
                    ['requirement_id' => $requirement->id],
                    [
                        'requirement_id' => $requirement->id,
                        'link' => $request->link,
                    ]
                );
            }

            $requirement->update($data);
            return response()->json(['success' => true, 'message' => 'Requirement updated successfully', 'data' => $requirement]);
        } catch (ValidationException $e) {
            return response()->json(['success' => false, 'message' => 'Validation error.', 'errors' => $e->errors()], 422);
        } catch (Exception $e) {
            Log::error('Error updating requirement: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Something went wrong. Please try again later.'], 500);
        }
    }
    // ***************Update-Requirements***************

    // ***************Delete-Requirements***************
    public function deleteRequirements(Request $request)
    {
        $id = decrypt_str($request->id);
        if (isset($id)) {
            $record = PlatformSchoolRequirements::where("id", $id)->first();
            if ($record) {
                $res = PlatformSchoolRequirements::where("id", "=", $id)->delete();
                if ($res) {
                    return response()->json(["success" => true, "message" => "Successfully Deleted"]);
                } else {
                    return response()->json(["success" => false, "message" => "Something went worng"]);
                }
            } else {
                return response()->json(["success" => false, "message" => "Record not found"]);
            }
        }
    }
    // ***************Delete-Requirements***************

    // ***************Duplicate-Requirements***************
    public function duplicateRequirements(Request $request)
    {
        $id = decrypt_str($request->id);
        $data = PlatformSchoolRequirements::find($id);

        if ($data) {
            $newData = $data->replicate();
            $newData->parent_id = $data->id;
            $newData->status = 'draft';
            $newData->is_valid = '0';
            $newData->save();

            return response()->json(['success' => true, 'reload' => true, 'message' => 'Requirement duplicated successfully']);
        }
        return response()->json(['error' => 'Data not found']);
    }
    // ***************Duplicate-Requirements***************

    public function update_user_background(Request $request)
    {
        $user = OnboardingInstructor::where('id', $request->userId)->first();
        if (!empty($user)) {
            if ($request->isChecked == 1) {
                $user->update(['is_background_check'=> $request->isChecked]);
            } else {
                $user->update(['is_background_check'=> null]);
            }
        }
        return response()->json(['success' => true]);
    }
}

