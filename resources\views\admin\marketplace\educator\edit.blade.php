@extends('admin.layouts.master')

@section('title') Update Educator | Whizara @endsection
@section('content')

<?php
    $res=get_permission(session('Adminnewlogin')['type']);
?>

<!-- MAIN SECTION START -->
<main class="content">
    <div class="container-fluid p-0">
        <!-- BREADCRUMB START -->
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                @if(isset($res['dashboard']))
                    @if(array_key_exists('dashboard',$res))
                        @if(in_array('add',json_decode($res['dashboard'] ,true)))
                            <li class="breadcrumb-item"><a href="{{url('admin-dashboard')}}" class="text-primary">{{ __('messages.dashboard') }}</a></li>
                        @endif
                    @endif
                @endif
                <li class="breadcrumb-item active float-right" aria-current="page"><a href="{{url('admin/k12connections/manage-educator')}}">Manage Educator</a></li>
                <li class="breadcrumb-item active" aria-current="page">Update Educator</li>
            </ol>
        </nav>
        <!-- BREADCRUMB END -->

        <div class="row">
            <div class="col-lg-12 col-md-9">
                <div class="row">
                    <div class="col-lg-12">
                        <div class="card">
                            <form method="post" id="editeducator" enctype='multipart/form-data'>
                                <div class="card-header border-bottom"><h5 class="mb-0">Update Educator</h5></div>
                                {{ csrf_field() }}
                                <div class="card-body">
                                    <div class="row">
                                        <input type="hidden" name="id" value="1">
                                        {{-- Fields --}}
                                    </div>
                                    <div class="col-md-12 d-flex justify-content-md-end justify-content-between mt-3">
                                        <a href="{{redirect()->getUrlGenerator()->previous()}}" class="btn btn-secondary mr-2">{{ __('messages.cancel') }}</a>
                                        <button type="button" id="edit_instructor" class="btn btn-primary">{{ __('messages.update') }}</button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>
@endsection