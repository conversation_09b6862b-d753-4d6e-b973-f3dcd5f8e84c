<?php

use Illuminate\Support\Facades\Route;
use App\V2\Core\Http\Controllers\CoreController;
use App\V2\Core\Http\Controllers\SubjectAndBudgetController;

#region Open Routes
Route::group([
    'prefix' => 'core',
    'as' => 'core.',
], function () {
    Route::get('subjects', [SubjectAndBudgetController::class, 'getAllSubjects'])->name('getAllSubjects');
    Route::get('dropdown/{type}', [CoreController::class, 'getCategoryBasedInfo'])->name('getCategoryBasedInfo');
    // Featured Educator
    Route::get('featured-educator', [CoreController::class, 'getFeaturedEducator'])->name('featured-educator');
    Route::get('s3-url', [CoreController::class, 'getS3Url'])->name('s3-url');

    Route::get('all-resources', [CoreController::class, 'getAllResources'])->name('all-resources');
    Route::get('resources/{id}', [CoreController::class, 'getResourceById'])->name('resources.show');
    Route::put('resources/{id}/shortlist', [CoreController::class, 'updateShortlistStatusForResources'])->name('resources.shortlist');
});