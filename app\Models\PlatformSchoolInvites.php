<?php

namespace App\Models;
use App\OnboardingInstructor;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletes;

class PlatformSchoolInvites extends Model
{
    use SoftDeletes;
    protected $table = 'platform_school_invites';
    protected $fillable = ['school_id', 'requirement_id', 'user_id', 'deadline_date', 'deadline_time', 'invited_by', 'type', 'status'];

    public function user()
    {
        return $this->belongsTo(OnboardingInstructor::class, 'user_id');
    }
    public function educator()
    {
        return $this->belongsTo(OnboardingInstructor::class, 'user_id');
    }

     protected static function booted()
    {
        static::addGlobalScope('autoExpire', function (Builder $builder) {
            // Before fetching, update all expired ones
            DB::table('platform_school_invites')
                ->where('status', '!=', 'expired')
                ->whereRaw("STR_TO_DATE(CONCAT(deadline_date, ' ', deadline_time), '%Y-%m-%d %H:%i:%s') < NOW()")
                ->update(['status' => 'expired', 'updated_at' => now()]);
        });
    }
}
