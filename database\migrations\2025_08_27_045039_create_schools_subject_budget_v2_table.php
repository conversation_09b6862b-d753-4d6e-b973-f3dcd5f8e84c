<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSchoolsSubjectBudgetV2Table extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('school_subject_budget_v2', function (Blueprint $table) {
            $table->id();

            $table->unsignedBigInteger('school_budget_id')->comment('Reference to global budget config');
            $table->unsignedBigInteger('subject_id')->comment('Reference to subjects table');

            // Base pay by years of experience
            $table->decimal('base_pay_0_3', 10, 2)->default(0)->comment('Base pay for 0-3 years experience');
            $table->decimal('pay_3_6', 10, 2)->default(0)->comment('Base pay for 3-6 years experience');
            $table->decimal('pay_6_10', 10, 2)->default(0)->comment('Base pay for 6-10 years experience');
            $table->decimal('pay_10_plus', 10, 2)->default(0)->comment('Base pay for 10+ years experience');

            // Education increments
            $table->decimal('masters_inc', 10, 2)->default(0)->comment('Increment for Masters degree');
            $table->decimal('doctorate_inc', 10, 2)->default(0)->comment('Increment for Doctorate degree');

            // Other increments
            $table->decimal('non_tech_time', 10, 2)->default(0)->comment('Non-teaching time allowance');
            $table->decimal('curriculum_inc', 10, 2)->default(0)->comment('Curriculum development increment');

            $table->timestamps();

            // Foreign keys
            $table->foreign('school_budget_id')->references('id')->on('school_budget_v2')->onDelete('cascade');
            $table->foreign('subject_id')->references('id')->on('subjects_v1')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('schools_subject_budget_v2');
    }
}
