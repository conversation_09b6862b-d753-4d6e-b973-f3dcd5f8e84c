class DynamicFormValidator {
  constructor(formSelector, config = {}, apiData = {}) {
    this.$form = $(formSelector);
    this.config = config;
    this.apiData = apiData;
    this.rules = {};
    this.messages = {};

    this.applyConfigValues();
    this.assignApiDataValues();
    this.initPlugins();
    this.buildValidationRules();
    this.initValidator();
    this.setupDynamicHandlers();
  }

  /**
   * Recursively extract validatable fields from nested config
   */
  getValidatableFields(fields) {
    let validFields = [];

    fields.forEach(field => {
      if (['separator', 'description'].includes(field.type)) {
        return; // skip non-input types
      }
      if (field.type === 'view' && Array.isArray(field.arrayFields)) {
        validFields = validFields.concat(this.getValidatableFields(field.arrayFields));
      } else if (field.name) {
        validFields.push(field);
      }
    });

    return validFields;
  }

  /**
   * Add `data-required` and `data-rules` to all inputs
   */
  applyConfigValues() {
    const section = this.config;
    const fields = this.getValidatableFields(section.fields);

    fields.forEach(field => {
      const $fields = this.$form.find(`[name="${field.name}[]"], [name="${field.name}"]`);
      if (!$fields.length) return;

      $fields.each(function () {
        $(this).data('required', field.required);
        $(this).data('rules', (field.rules || []).join('|'));
      });
    });
  }

  /**
   * Assign initial API values to inputs
   */
  assignApiDataValues() {
    Object.keys(this.apiData).forEach(fieldName => {
      const value = this.apiData[fieldName];
      const $fields = this.$form.find(`[name="${fieldName}[]"], [name="${fieldName}"]`);
      if (!$fields.length) return;

      if ($fields.is(':checkbox')) {
        const values = Array.isArray(value) ? value : [value];
        values.forEach(val => {
          $fields.filter(`[value="${val}"]`).prop('checked', true);
        });
      } else if ($fields.is(':radio')) {
        $fields.filter(`[value="${value}"]`).prop('checked', true);
      } else {
        $fields.each(function () {
          if ($(this).hasClass('select2')) {
            $(this).val(value).trigger('change');
          } else if ($(this).hasClass('datepicker')) {
            $(this).datepicker('update', value);
          } else if ($(this).hasClass('timepicker')) {
            $(this).timepicker('setTime', value);
          } else {
            $(this).val(value);
          }
        });
      }
    });
  }

  /**
   * Initialize plugins (select2, datepicker, timepicker, etc.)
   */
  initPlugins() {
    this.$form.find('select.select2').each(function () {
      $(this).select2({
        theme: 'bootstrap4',
        width: 'style',
        placeholder: $(this).data('placeholder') || 'Select an option',
        allowClear: Boolean($(this).data('allow-clear')),
      });
    });

    this.$form.find('input.datepicker').each(function () {
      $(this).datepicker({ format: 'yyyy-mm-dd', autoclose: true, todayHighlight: true });
    });

    this.$form.find('input.timepicker').each(function () {
      $(this).timepicker({ timeFormat: 'HH:mm', interval: 30, dropdown: true, scrollbar: true });
    });
  }

  /**
   * Build validation rules & messages for all inputs
   */
  buildValidationRules() {
    const self = this;

    const section = this.config;
    const fields = this.getValidatableFields(section.fields);

    fields.forEach(field => {
      const baseName = field.name;
      const $fields = self.$form.find(`[name="${baseName}[]"], [name="${baseName}"]`);
      if (!$fields.length) return;

      if (!self.rules[baseName]) {
        self.rules[baseName] = {};
      }

      if (field.required) {
        self.rules[baseName]['required'] = true;
      }

      (field.rules || []).forEach(rule => {
        if (rule.startsWith('min:')) {
          self.rules[baseName]['minlength'] = parseInt(rule.split(':')[1]);
        }
        if (rule.startsWith('max:')) {
          self.rules[baseName]['maxlength'] = parseInt(rule.split(':')[1]);
        }
        if (rule === 'email') {
          self.rules[baseName]['email'] = true;
        }
        if (rule === 'numeric') {
          self.rules[baseName]['number'] = true;
        }
      });

      self.messages[baseName] = {
        required: `${baseName} is required.`,
        email: `Please enter a valid email.`,
      };
    });
  }

  /**
   * Initialize jquery.validate with built rules
   */
  initValidator() {
    const self = this;

    this.validator = this.$form.validate({
      rules: self.rules,
      messages: self.messages,
      errorElement: 'div',
      errorClass: 'invalid-feedback',
      highlight: function (element) {
        $(element).addClass('is-invalid');
      },
      unhighlight: function (element) {
        $(element).removeClass('is-invalid');
      },
      errorPlacement: function (error, element) {
        if (element.is(':checkbox') || element.is(':radio')) {
          error.appendTo(element.closest('.form-group, .form-check'));
        } else {
          error.insertAfter(element);
        }
      },
      submitHandler: function (form) {
        console.log('Form is valid. Submitting...');
        form.submit();
      }
    });
  }

  /**
   * Example: dynamic conditional rules or dependent logic
   */
  setupDynamicHandlers() {
    const self = this;
    this.$form.find('select[name="country[]"]').on('change', function () {
      const countryVal = $(this).val();
      const $apptField = self.$form.find('[name="appointment_time[]"]');
      if (countryVal === 'US') {
        $apptField.rules('add', { required: true });
      } else {
        $apptField.rules('remove', 'required');
      }
    });
  }

  /**
   * Add validation rules for new dynamically added field
   */
  addFieldValidation($field) {
    const name = $field.attr('name').replace('[]', '');
    const required = $field.data('required') === true || $field.data('required') === 'true';
    const rulesString = $field.data('rules') || '';

    const newRules = {};
    if (required) {
      newRules['required'] = true;
    }

    if (rulesString) {
      const rulesArray = rulesString.split('|');
      rulesArray.forEach(rule => {
        if (rule.startsWith('min:')) {
          newRules['minlength'] = parseInt(rule.split(':')[1]);
        }
        if (rule.startsWith('max:')) {
          newRules['maxlength'] = parseInt(rule.split(':')[1]);
        }
        if (rule === 'email') {
          newRules['email'] = true;
        }
        if (rule === 'numeric') {
          newRules['number'] = true;
        }
      });
    }

    $field.rules('add', newRules);
  }

  /**
   * Re-initialize plugins for any new fields
   */
  initPluginsForNewField($field) {
    if ($field.hasClass('select2')) {
      $field.select2({
        theme: 'bootstrap4',
        width: 'style',
        placeholder: $field.data('placeholder') || 'Select an option',
        allowClear: Boolean($field.data('allow-clear')),
      });
    }
    if ($field.hasClass('datepicker')) {
      $field.datepicker({ format: 'yyyy-mm-dd', autoclose: true, todayHighlight: true });
    }
    if ($field.hasClass('timepicker')) {
      $field.timepicker({ timeFormat: 'HH:mm', interval: 30, dropdown: true, scrollbar: true });
    }
  }

  /**
   * Attach validation to every input in a dynamic row
   */
  addRepeatingRowValidation($row) {
    const self = this;
    $row.find('input, select').each(function () {
      self.initPluginsForNewField($(this));
      self.addFieldValidation($(this));
    });
  }
}

window.DynamicFormValidator = DynamicFormValidator;
