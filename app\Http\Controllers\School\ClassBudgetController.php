<?php

namespace App\Http\Controllers\School;

use App\Http\Controllers\Controller;
use App\Models\School\SchoolClassBudget;
use App\Models\v1\BudgetState;
use App\Models\v1\SubjectArea as SubjectV1;
use App\Models\v1\SubjectBudget;
use Exception;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\ValidationException;

class ClassBudgetController extends Controller
{
    private function generateName($validated)
    {
        $subject = SubjectV1::find($validated['subject_id']);
        $parts = [
            $validated['requirement_type'] ?? '',
            $subject->name ?? '',
            $validated['delivery_mode'] ?? '',
            $validated['language_of_instruction'] ?? '',
        ];

        return implode(' - ', array_filter($parts));
    }

    public function search_budget(Request $request)
    {
        if ($request->sort == "") {
            $searchTerm = $request->search;

            $budgets = SchoolClassBudget::where('language_of_instruction', 'like', '%' . $searchTerm . '%')
                ->where('school_id', auth()->user()->id)
                ->get();
            $subjectArea = SubjectV1::with('subjects')->get();
            $languages = DB::table('school_management_setting')->where('type', 'language')->get();

            // $view =view('school-marketplace.calculate-budget.index', compact('budgets', 'subjectArea', 'languages'))->render();

            //   return response()->json([
            //     "view"=>$view
            //  ]);
            $html = '';

            foreach ($budgets as $budget) {
                $html .= view('components.school-marketplace.calculate-budget.budget-card-component', [
                    'budget' => $budget
                ])->render();
            }




            return response()->json([
                'html' => $html
            ]);
        } else {
            $searchTerm = $request->search;
            $query = SchoolClassBudget::where('language_of_instruction', 'like', '%' . $searchTerm . '%')
                ->where('school_id', auth()->user()->id);
            if ($query->exists()) {

                if ($request->sort == "high_to_low") {

                    $budgets = $query->orderBy('calculated_budget', 'desc')->get();
                    $html = '';

                    foreach ($budgets as $budget) {
                        $html .= view('components.school-marketplace.calculate-budget.budget-card-component', [
                            'budget' => $budget
                        ])->render();
                    }

                    return response()->json([
                        'html' => $html
                    ]);
                } else if ($request->sort == "low_to_high") {

                    $budgets = $query->orderBy('calculated_budget', 'asc')->get();
                    $html = '';

                    foreach ($budgets as $budget) {
                        $html .= view('components.school-marketplace.calculate-budget.budget-card-component', [
                            'budget' => $budget
                        ])->render();
                    }

                    return response()->json([
                        'html' => $html
                    ]);
                } else if ($request->sort == "subject_a_to_z") {
                } else if ($request->sort == "subject_z_to_a") {
                } else if ($request->sort == "newest") {

                    $budgets = $query->orderBy('updated_at', 'desc')->get();
                    $html = '';

                    foreach ($budgets as $budget) {
                        $html .= view('components.school-marketplace.calculate-budget.budget-card-component', [
                            'budget' => $budget
                        ])->render();
                    }

                    return response()->json([
                        'html' => $html
                    ]);
                } else if ($request->sort == "oldest") {
                    $budgets = $query->orderBy('updated_at', 'asc')->get();
                    $html = '';

                    foreach ($budgets as $budget) {
                        $html .= view('components.school-marketplace.calculate-budget.budget-card-component', [
                            'budget' => $budget
                        ])->render();
                    }

                    return response()->json([
                        'html' => $html
                    ]);
                }
            } else {

                if ($request->sort == "high_to_low") {
                    $query = SchoolClassBudget::where('school_id', auth()->user()->id)->orderBy("calculated_budget", "desc")->get();


                    $budgets = $query->orderBy('calculated_budget', 'desc')->get();
                    $html = '';

                    foreach ($budgets as $budget) {
                        $html .= view('components.school-marketplace.calculate-budget.budget-card-component', [
                            'budget' => $budget
                        ])->render();
                    }

                    return response()->json([
                        'html' => $html
                    ]);
                } else if ($request->sort == "low_to_high") {
                    $query = SchoolClassBudget::where('school_id', auth()->user()->id)->orderBy("calculated_budget", "desc")->get();


                    $budgets = $query->orderBy('calculated_budget', 'asc')->get();
                    $html = '';

                    foreach ($budgets as $budget) {
                        $html .= view('components.school-marketplace.calculate-budget.budget-card-component', [
                            'budget' => $budget
                        ])->render();
                    }

                    return response()->json([
                        'html' => $html
                    ]);
                } else if ($request->sort == "subject_a_to_z") {
                } else if ($request->sort == "subject_z_to_a") {
                } else if ($request->sort == "newest") {
                    $query = SchoolClassBudget::where('school_id', auth()->user()->id)->orderBy("updated_at", "desc")->get();


                    $budgets = $query->orderBy('calculated_budget', 'desc')->get();
                    $html = '';

                    foreach ($budgets as $budget) {
                        $html .= view('components.school-marketplace.calculate-budget.budget-card-component', [
                            'budget' => $budget
                        ])->render();
                    }

                    return response()->json([
                        'html' => $html
                    ]);
                } else if ($request->sort == "oldest") {
                    $query = SchoolClassBudget::where('school_id', auth()->user()->id)->orderBy("updated_at", "desc")->get();


                    $budgets = $query->orderBy('calculated_budget', 'asc')->get();
                    $html = '';

                    foreach ($budgets as $budget) {
                        $html .= view('components.school-marketplace.calculate-budget.budget-card-component', [
                            'budget' => $budget
                        ])->render();
                    }

                    return response()->json([
                        'html' => $html
                    ]);
                }
            }

            $subjectArea = SubjectV1::with('subjects')->get();
            $languages = DB::table('school_management_setting')->where('type', 'language')->get();
        }
    }

    public function edit_budget(Request $request)
    {
        $budget_id = $request->budget_id;

        $budget = SchoolClassBudget::where("id", $budget_id)->first();


        $sped = empty($stateData) ? 0 : $stateData->sped_rec_comp;
        $languages = DB::table('school_management_setting')->where('type', 'language')->get();
        $subjectArea = SubjectV1::with('subjects')->get();
        $view = view('school-marketplace.calculate-budget.add', compact('subjectArea', 'languages', 'sped', 'budget'))->render();
        return response()->json([
            "view" => $view,

        ]);
    }


    public function calculateBudget(Request $request)
    {

        $subject_id = $request->subject_id;

        try {
            
            
            $userState = auth()->user()->state;

            // Get the matching state ID from budget_states
            $stateId = BudgetState::where('name', $userState)->value('id');


            $subjectBudget = SubjectBudget::where('subject_id', $subject_id)->where("state_id", $stateId)->first();


            if (!$subjectBudget) {
                return response()->json(['status' => false, 'message' => 'Subject budget not found']);
            }
            $totalHours = (request()->class_duration_hours * request()->instructional_days) / 60;

            $provideCurriculum = request()->school_curriculum_provided;
            $years_of_experience = $request->years_of_experience;



            // Step 1: Curriculum Increment
            $curriculumInc = $provideCurriculum ? ($subjectBudget->curriculum_inc ?? 0) : 0;

            // Step 2: Qualification Increment


            // Step 3: Calculate for each experience range
            $basePay = $subjectBudget->base_pay_0_3 ?? 0;


            // dd($request->years_of_experience);


            // Map experience to budget key
            switch ($years_of_experience) {
                case '0-3':
                    $key = '3';
                    break;
                case '3-6':
                    $key = '6';
                    break;
                case '6-10':
                    $key = '10';
                    break;
                case '10+':
                    $key = '11';
                    break;
                default:
                    $key = '3'; // fallback to base budget if experience is unknown
                    break;
            }

            //calcaulate the budget
            $schoolstate = DB::table('users')->where('id', auth()->user()->id)->value("state");
            $stateData = BudgetState::where('name', $schoolstate)->first();

            $sped = empty($stateData) ? 0 : $stateData->sped_rec_comp;
            if ($request->special_education_certification == true) {
                $basePay = $sped;
            }
            if ($request->language_of_instruction !== "English") {

                $bilingual = empty($stateData) ? 0 : $stateData->bilingual_inc ?? 0;
            } else {
                $bilingual = 0;
            }


            // Calculate budgets
            $budgets = [
                '3'  => ($curriculumInc + $basePay + $bilingual) * $totalHours,
                '6'  => ($curriculumInc + $basePay + $bilingual + ($subjectBudget->pay_3_6 ?? 0)) * $totalHours,
                '10' => ($curriculumInc + $basePay + $bilingual + ($subjectBudget->pay_6_10 ?? 0)) * $totalHours,
                '11' => ($curriculumInc + $basePay + $bilingual + ($subjectBudget->pay_10_plus ?? 0)) * $totalHours,
            ];


            // Get the final budget
            $finalBudget = $budgets[$key] ?? 0;

            return $finalBudget;
        } catch (Exception $e) {
            // log::info($e->getMessage());
            return response()->json(['status' => false, 'message' => $e->getMessage()]);
        }
    }


    public function index(Request $request)
    {
        $budgets = SchoolClassBudget::where('school_id', Auth::user()->id)->get();

        if ($request->wantsJson()) {
            return response()->json($budgets);
        }
        $subjectArea = SubjectV1::get();
        $languages = DB::table('school_management_setting')->where('type', 'language')->get();

        return view('school-marketplace.calculate-budget.index', compact('budgets', 'subjectArea', 'languages'));
    }
    public function create(Request $request)
    {


        $subjectArea = SubjectV1::with('subjects')->get();

        if ($request->wantsJson()) {
            return response()->json($subjectArea);
        }
        // $languages = DB::table('school_management_setting')->where('type', 'language')->first();

        $schoolstate = DB::table('users')->where('id', auth()->user()->id)->value("state");
        $stateData = BudgetState::where('name', $schoolstate)->first();
        $bilingual = empty($stateData) ? 0 : $stateData->bilingual_inc ?? 0;

        $sped = empty($stateData) ? 0 : $stateData->sped_rec_comp;
        $languages = DB::table('school_management_setting')->where('type', 'language')->get();

        return view('school-marketplace.calculate-budget.add', compact('subjectArea', 'languages', 'sped'));
    }

    // public function store(Request $request)
    // {


    //     $validated = $request->validate([
    //         'name' => 'nullable|string',
    //         'requirement_type' => ['required', Rule::in(['Class', 'Case Management', 'Speech therapy', 'Other'])],
    //         'subject_id' => 'required|exists:subjects_v1,id',
    //         'educator_profile' => ['required', Rule::in(['credentialed', 'non_credentialed'])],
    //         'special_education_certification' => 'required',
    //         'delivery_mode' => ['required', Rule::in(['Online', 'Hybrid'])],
    //         'years_of_experience' => ['required', Rule::in(['0-3', '3-6', '6-10', '10+'])],
    //         'instructional_days' => 'required|integer|min:1',
    //         'class_duration_hours' => 'required|integer|min:1',
    //         'non_instructional_hours' => 'required|integer|min:0',
    //         'language_of_instruction' => 'required|string',
    //         'expected_class_size' => 'required|integer|min:1|max:30',
    //         'school_curriculum_provided' => 'required',
    //         'calculated_budget' => 'required|numeric|min:0',
    //         // 'tags' => 'sometimes|array',
    //         // 'tags.*' => 'string'
    //     ]);



    //     $validated['school_id'] = Auth::user()->id;
    //     $validated['name'] = $validated['name'] ?? $this->generateName($validated) ?? 'Class Budget';

    //     $budget = SchoolClassBudget::create($validated);

    //     return response()->json(["status"=>"success"]);
    // }


    public function store(Request $request)
    {
        $total = $this->calculateBudget($request);



        try {
            $validated = $request->validate([
                'name' => 'nullable|string',
                'requirement_type' => ['required', Rule::in(['Class', 'Case Management', 'Speech therapy', 'Other'])],
                'subject_id' => 'required|exists:subjects_v1,id',
                'educator_profile' => ['required', Rule::in(['Credentialed', 'Non-credentialed'])],
                'special_education_certification' => 'required',
                'delivery_mode' => ['required', Rule::in(['Online', 'Hybrid'])],
                'years_of_experience' => ['required', Rule::in(['0-3', '3-6', '6-10', '10+'])],
                'instructional_days' => 'required|integer|min:1',
                'class_duration_hours' => 'required|integer|min:1',
                'non_instructional_hours' => 'required|integer|min:0',
                'language_of_instruction' => 'required|string',
                'expected_class_size' => 'required|integer|min:1|max:30',
                'school_curriculum_provided' => '',
                // 'calculated_budget' => 'required|numeric|min:0',
            ]);

            $validated['calculated_budget'] = $total;

            $validated['school_id'] = Auth::user()->id;
            $validated['name'] = $validated['name'] ?? $this->generateName($validated) ?? 'Class Budget';

            $budget = SchoolClassBudget::create($validated);

            return response()->json([
                'status' => 'success',
                'message' => 'Budget saved successfully.',
                'data' => $budget
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to save budget.',
                'error_message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString(), // ⚠️ Optional, can be long
            ], 500);
        }
    }


    public function show($id)
    {
        $budget = SchoolClassBudget::where('school_id', Auth::user()->id)->findOrFail($id);
        return response()->json($budget);
    }

    public function edit($id, Request $request)
    {
        $budget = SchoolClassBudget::where('school_id', Auth::user()->school_id)->findOrFail($id);
        $subjects = SubjectV1::all();

        if ($request->wantsJson()) {
            return response()->json([
                'budget' => $budget,
                'subjects' => $subjects,
            ]);
        }

        return view('school_class_budgets.edit', compact('budget', 'subjects'));
    }

    public function update(Request $request, $id)
    {


        $budget = SchoolClassBudget::where('school_id', Auth::user()->id)->findOrFail($id);

        $total = $this->calculateBudget($request);





        $validated = $request->validate([
            'name' => 'nullable|string',
            'requirement_type' => ['required', Rule::in(['Class', 'Case Management', 'Speech therapy', 'Other'])],
            'subject_id' => 'required|exists:subjects_v1,id',
            'educator_profile' => ['required', Rule::in(['Credentialed', 'Non-credentialed'])],
            'special_education_certification' => 'required',
            'delivery_mode' => ['required', Rule::in(['Online', 'Hybrid'])],
            'years_of_experience' => ['required', Rule::in(['0-3', '3-6', '6-10', '10+'])],
            'instructional_days' => 'required|integer|min:1',
            'class_duration_hours' => 'required|integer|min:1',
            'non_instructional_hours' => 'required|integer|min:0',
            'language_of_instruction' => 'required|string',
            'expected_class_size' => 'required|integer|min:1|max:30',
            'school_curriculum_provided' => 'required',
        ]);


        // Convert JSON string to object
        $validated['calculated_budget'] = $total;
        try {

            if (!isset($validated['name'])) {
                $validated['name'] = $this->generateName(array_merge($budget->toArray(), $validated)) ?? $budget->name;
            }

            $budget->update($validated);

            // return response()->json($budget);
            return response()->json([
                'status' => 'success',
                'message' => 'Budget saved successfully.',
                'data' => $budget
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to save budget.',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function destroy($id)
    {
        $budget = SchoolClassBudget::where('school_id', Auth::user()->id)->findOrFail($id);
        $budget->delete();

        return response()->json(['message' => 'Budget deleted successfully']);
    }


    public function duplicate_budget(Request $request)
    {

        $budget_id = $request->id;

        // Fetch the original budget record
        $originalBudget = SchoolClassBudget::find($budget_id);

        if (!$originalBudget) {
            return response()->json(['error' => 'Original budget not found'], 404);
        }

        // Duplicate the budget
        $newBudget = $originalBudget->replicate(); // clone all attributes except id
        $newBudget->name = $originalBudget->name . ' Copy '; // change name
        $newBudget->save();

        return response()->json(['success' => true, 'new_budget' => $newBudget]);
    }
    public function get_all_state(Request $rrequest)
    {

        $schoolstate = DB::table('users')->where('id', auth()->user()->id)->value("state");

        $stateData = BudgetState::where('name', $schoolstate)->first();




        return response()->json(["status" => "success", "state_data" => $stateData]);
    }
    // public function sort_budget(Request $request)
    // {

    //     if ($request->sort_type == "high_to_low") {

    //     } else if ($request->sort_type == "low_to_how") {

    //     } else if ($request->sort_type == "subject_a_to_z") {

    //     } else if ($request->sort_type == "subject_z_to_a") {

    //     } else if ($request->sort_type == "newest") {

    //     } else if ($request->sort_type == "oldest") {

    //     }
    // }
}
