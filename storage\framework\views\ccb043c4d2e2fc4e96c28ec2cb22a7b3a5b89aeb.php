

<?php $__env->startSection('title'); ?> Edit Setting Terms | Whizara <?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>

<!-- MAIN SECTION START -->
<main class="content">
    <div class="container-fluid p-0">
        <!-- BREADCRUMB START -->
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="<?php echo e(url('admin-dashboard')); ?>" class="text-primary"><?php echo e(__('messages.dashboard')); ?></a></li>
                <li class="breadcrumb-item active" aria-current="page">Content Management</li>
                <li class="breadcrumb-item active" aria-current="page">Edit Content</li>
                <li class="breadcrumb-item active float-right" aria-current="page">
                    <a href="<?php echo e(url('terms-setting-list')); ?>"> Content List</a>
                </li>
            </ol>
        </nav>
        <!-- BREADCRUMB END -->

        <!-- EDIT PROFILE SECTION START -->

            <div class="row justify-content-center">

                <div class="col-lg-10 col-md-9">
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="card">
                                <div class="card-header border-bottom">
                                    <h5 class="mb-0">Content Management</h5>
                                </div>
                                <form method="post" id="edit_setting_terms_form" enctype="multipart/form-data" action="<?php echo e(route('edittermssetting')); ?>">
                                <?php echo e(csrf_field()); ?>

                                <div class="card-body">

                                    <div class="row">
                                        <input type="hidden" name="id" value="<?php echo e($terms_id); ?>">

                                        <div class="col-md-12 form-group">
                                            <label class="form-label">Type</label>
                                            <input type="text" class="form-control" name="type" id="title"  placeholder="Type" value="<?php echo e($setting[0]->type); ?>" readonly="">
                                            <span id="title_error" class="err"></span>
                                        </div>

                                        <div class="col-md-12 form-group">
                                            <label class="form-label">Upload file</label>

                                            <input type="file" id="file_data"  class="form-control" value="" name="file_data" accept=".doc,.docx,.ppt, .pptx,.txt,.pdf">
                                            <span id="file_data_error" class="err"></span>
                                        </div>
                                        <?php if($setting[0]->document): ?>
                                        <div class="col-md-12 form-group">
                                            <label class="form-label">Uploaded file : <a target="_blank" href="<?php echo e(generateSignedUrl($setting[0]->document)); ?>"><?php echo e($setting[0]->document); ?> </a></label>

                                    </div>
                                       <?php endif; ?>

                                        <div class="col-md-12 form-group">
                                            <label class="form-label">Description</label>
                                            <textarea class="form-control editor1" name="description" id="description"><?php echo e($setting[0]->description); ?></textarea>
                                            <span id="description_error" class="err"></span>
                                        </div>
                                        <input type="hidden" id="description_hidden" name="description_hidden">

                                        <?php if(!$setting[0]->type=='Online Logistics/Prerequisites' || !$setting[0]->type=='In person Logistics/Prerequisites'): ?>
                                        <div class="col-md-12 form-group">
                                            <label class="form-label"><?php echo e(__('messages.status')); ?></label>
                                            <select class="form-control" name="status" id="status">
                                                <option value="">Select status</option>
                                                <option value="1" <?php echo e($setting[0]->status == 1  ? 'selected' : ''); ?>><?php echo e(__('messages.active')); ?></option>
                                                <option value="0" <?php echo e($setting[0]->status == 0  ? 'selected' : ''); ?>><?php echo e(__('messages.deactive')); ?></option>
                                            </select>
                                            <span id="status_error" class="err"></span>
                                        </div>


                                      <?php endif; ?>
                                        <div class="col-md-12 d-flex justify-content-md-end justify-content-between mt-3">
                                            <a href="<?php echo e(redirect()->getUrlGenerator()->previous()); ?>" class="btn btn-secondary mr-2"><?php echo e(__('messages.cancel')); ?></a>
                                            <button type="submit" id="edit_setting_terms_btn" class="btn btn-primary" onclick="updateEditorContent()"><?php echo e(__('messages.update')); ?></button>
                                        </div>
                                    </div>
                                </div>
                                </form>
                            </div>
                        </div>

                    </div>
                </div>
            </div>

        <!-- EDIT PROFILE SECTION END -->
    </div>
</main>
<!-- MAIN SECTION END -->



<?php $__env->stopSection(); ?>
<?php echo $__env->make('admin.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\whizara\whizara\resources\views/admin/settingterms/editsettingterms.blade.php ENDPATH**/ ?>