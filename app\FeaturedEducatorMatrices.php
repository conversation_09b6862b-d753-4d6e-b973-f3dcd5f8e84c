<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class FeaturedEducatorMatrices extends Model
{
    protected $table = 'featured_educator_matrices';

    protected $fillable = [
        'educator_id',
        'start_date',
        'end_date',
        'program_count',
        'class_count',
        'avg_rating',
        'lifetime_avg_rating',
        'weighted_score',
        'updated_by',
    ];

    public function educator()
    {
        return $this->belongsTo(OnboardingInstructor::class, 'educator_id');
    }
}
