{{-- SCHOOL USERS ACTION ACCORDION --}}
<style>
    .tab-content{
        width: 100%;
    }
    .accordion-container {
        width: 100%;
        margin: 20px 0;
    }

    .accordion-item {
        border: 1px solid #ddd;
        border-radius: 6px;
        margin-bottom: 8px;
        overflow: hidden;
        background: #fff;
    }

    .accordion-header {
        background: #f8f9fa;
        padding: 12px 15px;
        cursor: pointer;
        font-weight: 600;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .accordion-header:hover {
        background: #e9ecef;
    }

    .accordion-icon {
        font-size: 16px;
        transition: transform 0.3s ease;
    }

    .accordion-item.active .accordion-icon {
        transform: rotate(90deg);
    }

    .accordion-body {
        display: none;
        padding: 15px;
        border-top: 1px solid #ddd;
    }

    .form-check {
        margin-bottom: 8px;
    }

    .form-check-label {
        cursor: pointer;
    }
</style>

<div class="accordion-container" id="manualUsersAccordion">
    @foreach ($school_contact_info as $user)
        <div class="accordion-item">
            <div class="accordion-header">
                <span>{{ $user->first_name }} ({{ $user->email }})</span>
                <span class="accordion-icon">></span>
            </div>
            <div class="accordion-body">
                @foreach ($coreActions as $coreAction)
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" value="{{ $coreAction->id }}" name="core_action[{{ $user->id }}][]" id="coreAction_{{ $user->id }}_{{ $coreAction->id }}" @if($user->actions->contains($coreAction->id)) checked @endif>
                        <label class="form-check-label" for="coreAction_{{ $user->id }}_{{ $coreAction->id }}">{{ $coreAction->description }}</label>
                    </div>
                @endforeach
            </div>
        </div>
    @endforeach
</div>

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
    $(document).ready(function () {
        $("#manualUsersAccordion .accordion-header").on("click", function () {
            const item = $(this).closest(".accordion-item");

            // close others
            $("#manualUsersAccordion .accordion-item").not(item).removeClass("active").find(".accordion-body").slideUp();

            // toggle clicked
            item.toggleClass("active");
            item.find(".accordion-body").slideToggle();
        });
    });
</script>
{{-- SCHOOL USERS ACTION ACCORDION --}}
