<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateEmailLayoutsAndTemplatesTables extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Layouts
        Schema::create('email_layouts', function (Blueprint $table) {
            $table->bigIncrements('id')->comment('Primary key');
            $table->string('title')->comment('Layout display title');
            $table->string('slug')->unique()->comment('Unique slug identifier for fetching layouts');
            $table->text('header_html')->nullable()->comment('HTML content for email header');
            $table->text('footer_html')->nullable()->comment('HTML content for email footer');
            $table->json('tags')->nullable()->comment('JSON array of tags for categorization (e.g. core, marketing)');
            $table->unsignedBigInteger('created_by')->nullable()->comment('FK to users.id who created this record');
            $table->unsignedBigInteger('updated_by')->nullable()->comment('FK to users.id who last updated this record');
            $table->timestamps();

            // Indexes
            $table->index('title', 'idx_layouts_title');
            $table->index('slug', 'idx_layouts_slug'); // redundant with unique, helps query plans
            $table->index('created_by', 'idx_layouts_created_by');
            $table->index('updated_by', 'idx_layouts_updated_by');

            // Foreign keys
            $table->foreign('created_by')->references('id')->on('users')->onDelete('set null');
            $table->foreign('updated_by')->references('id')->on('users')->onDelete('set null');
        });

        // Templates
        Schema::create('email_templates', function (Blueprint $table) {
            $table->bigIncrements('id')->comment('Primary key');
            $table->string('title')->comment('Template display title');
            $table->string('slug')->unique()->comment('Unique slug identifier for fetching templates');
            $table->unsignedBigInteger('layout_id')->nullable()->comment('FK to email_layouts.id');
            $table->text('subject')->nullable()->comment('Subject line for the email');
            $table->longText('body_html')->nullable()->comment('Main HTML content of the email template');
            $table->json('tags')->nullable()->comment('JSON array of tags for categorization (e.g. user, auth, feature)');
            $table->unsignedBigInteger('created_by')->nullable()->comment('FK to users.id who created this record');
            $table->unsignedBigInteger('updated_by')->nullable()->comment('FK to users.id who last updated this record');
            $table->timestamps();

            // Indexes
            $table->index('title', 'idx_templates_title');
            $table->index('slug', 'idx_templates_slug');
            $table->index('layout_id', 'idx_templates_layout_id');
            $table->index('created_by', 'idx_templates_created_by');
            $table->index('updated_by', 'idx_templates_updated_by');

            // Foreign keys
            $table->foreign('layout_id')->references('id')->on('email_layouts')->onDelete('set null');
            $table->foreign('created_by')->references('id')->on('users')->onDelete('set null');
            $table->foreign('updated_by')->references('id')->on('users')->onDelete('set null');
        });

    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('email_templates');
        Schema::dropIfExists('email_layouts');
    }
}
