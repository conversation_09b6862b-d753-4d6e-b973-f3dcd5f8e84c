<style>
    .select2-container.select2-container--open {
        z-index: 9999 !important;
    }

    .select2-search.select2-search--dropdown {
        position: relative;
        font-size: 16px;
    }

    .select2-search.select2-search--dropdown input {
        padding-left: 24px;
    }

    .select2-search.select2-search--dropdown::after {
        content: "\f002";
        font-family: FontAwesome;
        /* use FA 4 font */
        position: absolute;
        left: 8px;
        top: 50%;
        transform: translateY(-50%);
        pointer-events: none;
        color: #555;
        /* icon color */
        font-size: 16px;
        /* matching size */
    }

    .select2-container--bootstrap4 .select2-selection--single .select2-selection__placeholder {
        color: black !important;
        margin: 0;
        font-family: inherit;
        font-size: inherit;
        line-height: inherit;
        font-weight: 400;
        font-size: 16px !important;

    }

    .select2-container--bootstrap4 .select2-selection--single {
        border: 1px solid black;

    }

    input:focus {
        outline: none !important;
        box-shadow: none !important;
        border-color: black !important;
    }

    /* .dollar_absolute {
        right: 197px !important;
        font-weight: bold;
        color: #004CBD;

    } */
    .dollar-prefix {
        position: relative;
        padding-left: 20px;
    }

    .dollar-prefix::before {
        content: "$";
        position: absolute;
        left: 71px;
        top: 50%;
        transform: translateY(-50%);
        color: #004CBD;
        font-weight: bold;
        pointer-events: none;
    }

    .custom-select-wrapper .select2-selection__arrow {
        display: none !important;
    }

    .error_border {
        border: 1px solid red !important;
    }

    .select2 .select2-selection__rendered {
        color: black !important;
        font-size: 16px !important;
    }

    /* .select2-container--bootstrap4.select2-container--focus .select2-selection {
        border-color: black !important;
    } */
    .budget_sub_subject+.select2-container--bootstrap4 .selection::before {

        content: "";
        display: inline-block;
        position: absolute;
        width: 27px;
        height: 27px;
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 12 12' fill='none'%3E%3Cpath d='M4.43812 8.87624C3.19772 8.87624 2.14805 8.44654 1.2891 7.58714C0.430157 6.72774 0.000455553 5.67807 0 4.43812C-0.000454831 3.19818 0.429246 2.14851 1.2891 1.2891C2.14896 0.429701 3.19863 0 4.43812 0C5.67761 0 6.72751 0.429701 7.58782 1.2891C8.44814 2.14851 8.87761 3.19818 8.87624 4.43812C8.87624 4.93883 8.79659 5.41109 8.63727 5.85491C8.47795 6.29872 8.26174 6.69132 7.98862 7.03272L11.8122 10.8563C11.9374 10.9815 12 11.1408 12 11.3343C12 11.5277 11.9374 11.6871 11.8122 11.8122C11.6871 11.9374 11.5277 12 11.3343 12C11.1408 12 10.9815 11.9374 10.8563 11.8122L7.03272 7.98862C6.69132 8.26173 6.29872 8.47795 5.85491 8.63727C5.4111 8.79659 4.93883 8.87624 4.43812 8.87624ZM4.43812 7.51067C5.29161 7.51067 6.01718 7.21206 6.61485 6.61485C7.21252 6.01764 7.51112 5.29206 7.51067 4.43812C7.51021 3.58418 7.21161 2.85883 6.61485 2.26208C6.01809 1.66532 5.29252 1.36649 4.43812 1.36558C3.58373 1.36467 2.85838 1.6635 2.26208 2.26208C1.66578 2.86065 1.36694 3.586 1.36558 4.43812C1.36421 5.29024 1.66304 6.01582 2.26208 6.61485C2.86111 7.21388 3.58646 7.51249 4.43812 7.51067Z' fill='black'/%3E%3C/svg%3E");
        background-size: 17px 30px;
        background-repeat: no-repeat;
        margin-right: 6px;
        vertical-align: middle;
        top: 10px;
        left: 12px;

    }


    .budget_sub_subject+.select2-container--bootstrap4 .select2-selection__rendered {
        padding-left: 26px !important;
    }

    .new_budget_width {
        width: 45% !important;
    }

    .custom-select-wrapper {
        width: 209px !important;
    }
</style>


<div class="modal fade {{ !empty($budget) ? 'edit_model' : 'budgetcalculatormodal' }}" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle">
    <div class="modal-dialog modal-dialog-centered" style="max-width:1106px!important;" role="document">
        <div class="modal-content p-3 budget_modal_content @if(!empty($budget)) edit_model_content @endif">
            <div class="modal-header" style="border-bottom:none">
                <div class="d-flex align-items-center">
                    <p class="ms-3  new_card_text heading_text bold mb-0">Calculate budget for your requirement</p>
                </div>

                <button type="button" class="close_budgetcontent_modal" data-dismiss="modal" aria-label="Close" style="background: none; border: none; font-size: 1.5rem; line-height: 1; color: #000;">
                    <span aria-hidden="true" style="font-size:2rem;color:#787777">&times;</span>
                </button>
            </div>
            <div class="">
                <p style="font-size:14px;width:85%;margin-left:3%">Enter required details to calculate the budget. You can change the values to adjust as per desired budget.
                    You can save budget as well and use it later for posting a class.</p>
            </div>
            <div class="mx-5">
                <hr class="mt-5" style="height:1px; width:100%; border:none; background-color:grey;">
            </div>

            <div class="modal-body">
                <form class="formdata_budget" id=@if(!empty($budget)) "savebudget_edit_form" @else "savebudget_form" @endif>
                    <div class="row d-flex justify-content-between">
                        <h5 style="color:#004CBD">Requirement Overview</h5>
                        <div class="col-md-6">

                            <div class="row d-flex align-items-center">
                                <div class="row d-flex justify-content-between my-3">
                                    <div class="col-lg-4 d-flex align-items-center">
                                        <label for="deliveryMode" class="form-label d-inline-block pt-3" style="white-space: nowrap;">
                                            Requirement Overview<span class="text-danger">*</span>
                                        </label>

                                    </div>

                                    <div class="col-lg-4 position-relative custom-select-wrapper " style="width:250px;margin-right:48px">

                                        <select
                                            class="budget_requirement_type error_calculate_budget delivery_mode select22 budget_input"
                                            name="requirement_type"
                                            style="border:1px solid black!important;border-radius:32px!important; padding:3% 6%;font-weight:400"
                                            onfocus="this.style.outline='none'; this.style.boxShadow='none';">
                                            <option value="" disabled hidden class="placeholder-option" selected>Select</option>
                                            <option value="Class" {{ (!empty($budget) && $budget->requirement_type == 'Class') ? 'selected' : '' }}>Class</option>
                                            <option value="Case Management" {{ (!empty($budget) && $budget->requirement_type == 'Case Management') ? 'selected' : '' }}>Case Management</option>
                                            <option value="Speech therapy" {{ (!empty($budget) && $budget->requirement_type == 'Speech therapy') ? 'selected' : '' }}>Speech therapy</option>
                                            <option value="Other" {{ (!empty($budget) && $budget->requirement_type == 'Other') ? 'selected' : '' }}>Other</option>
                                        </select>

                                        <div class="custom-select-arrow">
                                            <svg width="8" height="4" viewBox="0 0 8 4" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path fill-rule="evenodd" clip-rule="evenodd" d="M4 2.98204L7.33341 0L8 0.596323L4.33329 3.87652C4.24489 3.95559 4.125 4 4 4C3.875 4 3.75511 3.95559 3.66671 3.87652L0 0.596323L0.666588 0L4 2.98204Z" fill="black"></path>
                                            </svg>
                                        </div>
                                    </div>


                                </div>
                                <div class="row d-flex justify-content-between my-2">
                                    <div class="col-lg-4 d-flex align-items-center">
                                        <label for="deliveryMode" class="form-label platform_school_label d-inline-block pt-3">Subject<span class="text-danger">*</span></label>
                                    </div>

                                    <div class="col-lg-4 position-relative custom-select-wrapper" style="width:250px;margin-right:48px">

                                        <select @if (!empty($budget) && $budget->requirement_type != "Class") disabled @endif class="budget_sub_subject select22 new_budget error_calculate_budget budget_input" {{!empty($budget) ? 'data-selected=' . $budget->subject_id : '' }} id="subject" name="subject_id" style="outline:none;">
                                            <option value="" {{ empty($budget) ? 'selected' : '' }} hidden style="color:black">Search Subject</option>
                                            @foreach ($subjectArea as $subjects)
                                            @if (!empty($subjects->subject_area) && $subjects->subjects->isNotEmpty())
                                            <optgroup label="{{ $subjects->subject_area }}" data-id="{{$subjects->id}}">
                                                {{-- @foreach ($subjects->subjects as $subject)
                                                @if (!empty($subject->title))
                                                <option
                                                    value="{{ (int) $subject->id }}"
                                                    data-amount_base="{{ number_format($subject->subjectBudget->base_pay_0_3, 2, '.', '') }}"
                                                    data-amount_inc_3_6="{{ number_format($subject->subjectBudget->pay_3_6, 2, '.', '') }}"
                                                    data-amount_inc_6_10="{{ number_format($subject->subjectBudget->pay_6_10, 2, '.', '') }}"
                                                    data-amount_inc_10="{{ number_format($subject->subjectBudget->pay_10_plus, 2, '.', '') }}"
                                                    data-amount_curriculum="{{ number_format($subject->subjectBudget->curriculum_inc, 2, '.', '') }}"
                                                    {{ (!empty($budget) && $budget->subject_id == $subject->id) ? 'selected' : '' }}>
                                                    {{ $subject->title }} {{ (!empty($budget) && $budget->subject_id == $subject->id) ? 'selected' : '' }}
                                                </option>
                                                @endif
                                                @endforeach --}}
                                            </optgroup>
                                            @endif
                                            @endforeach
                                        </select>

                                        <div class="custom-select-arrow">
                                            <svg width="8" height="4" viewBox="0 0 8 4" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path fill-rule="evenodd" clip-rule="evenodd" d="M4 2.98204L7.33341 0L8 0.596323L4.33329 3.87652C4.24489 3.95559 4.125 4 4 4C3.875 4 3.75511 3.95559 3.66671 3.87652L0 0.596323L0.666588 0L4 2.98204Z" fill="black"></path>
                                            </svg>
                                        </div>
                                    </div>
                                </div>
                                <div class="row d-flex align-items-center mt-5 mb-2">
                                    <div class=" col-md-4">
                                        <label class="form-label platform_school_label m-0 d-inline-block">Delivery Mode<span class="text-danger">*</span><svg data-bs-toggle="tooltip" data-bs-placement="top" title="Credentialed educators are only available for online requirements" width="13" height="14" viewBox="0 0 13 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M6.5 10.25C6.68417 10.25 6.83865 10.1876 6.96345 10.0628C7.08825 9.938 7.15044 9.78373 7.15 9.6V7C7.15 6.81583 7.0876 6.66157 6.9628 6.5372C6.838 6.41283 6.68374 6.35043 6.5 6.35C6.31627 6.34957 6.162 6.41197 6.0372 6.5372C5.9124 6.66243 5.85 6.8167 5.85 7V9.6C5.85 9.78417 5.9124 9.93865 6.0372 10.0635C6.162 10.1883 6.31627 10.2504 6.5 10.25ZM6.5 5.05C6.68417 5.05 6.83865 4.9876 6.96345 4.8628C7.08825 4.738 7.15044 4.58373 7.15 4.4C7.14957 4.21627 7.08717 4.062 6.9628 3.9372C6.83844 3.8124 6.68417 3.75 6.5 3.75C6.31584 3.75 6.16157 3.8124 6.0372 3.9372C5.91284 4.062 5.85044 4.21627 5.85 4.4C5.84957 4.58373 5.91197 4.73822 6.0372 4.86345C6.16244 4.98868 6.3167 5.05087 6.5 5.05ZM6.5 13.5C5.60084 13.5 4.75584 13.3293 3.965 12.9878C3.17417 12.6463 2.48625 12.1833 1.90125 11.5988C1.31625 11.0142 0.853234 10.3263 0.512201 9.535C0.171168 8.74373 0.000434156 7.89873 8.22785e-07 7C-0.000432511 6.10127 0.170301 5.25627 0.512201 4.465C0.854101 3.67373 1.31712 2.98582 1.90125 2.40125C2.48538 1.81668 3.1733 1.35367 3.965 1.0122C4.7567 0.670733 5.6017 0.5 6.5 0.5C7.3983 0.5 8.2433 0.670733 9.035 1.0122C9.8267 1.35367 10.5146 1.81668 11.0988 2.40125C11.6829 2.98582 12.1461 3.67373 12.4885 4.465C12.8308 5.25627 13.0013 6.10127 13 7C12.9987 7.89873 12.828 8.74373 12.4878 9.535C12.1476 10.3263 11.6846 11.0142 11.0988 11.5988C10.5129 12.1833 9.82497 12.6466 9.035 12.9885C8.24504 13.3304 7.40004 13.5009 6.5 13.5ZM6.5 12.2C7.95167 12.2 9.18125 11.6963 10.1888 10.6888C11.1963 9.68125 11.7 8.45167 11.7 7C11.7 5.54833 11.1963 4.31875 10.1888 3.31125C9.18125 2.30375 7.95167 1.8 6.5 1.8C5.04834 1.8 3.81875 2.30375 2.81125 3.31125C1.80375 4.31875 1.3 5.54833 1.3 7C1.3 8.45167 1.80375 9.68125 2.81125 10.6888C3.81875 11.6963 5.04834 12.2 6.5 12.2Z" fill="#C5C6CC" />
                                            </svg>
                                        </label>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="col d-inline-flex align-items-center" style="margin-left:56px">

                                            <div class="form-check-inline d-flex">
                                                <input class="form-check-input custom-input  budget_input"
                                                    type="radio"
                                                    name="delivery_mode"
                                                    id="classType"
                                                    value="Online"
                                                    style="height:18px!important"
                                                    @if (empty($budget) || (!empty($budget) && $budget->delivery_mode == "Online")) checked @endif>
                                                <label class="form-check-label custom-lab" for="classType" style="color:black!important">Online</label>
                                            </div>

                                            <div class="form-check-inline d-flex align-items-center">
                                                <input class="form-check-input custom-input non_credentialed"
                                                    type="radio"
                                                    name="delivery_mode"
                                                    id="nonTeachingType"
                                                    value="Hybrid"
                                                    style="height:18px"
                                                    @if (!empty($budget) && $budget->delivery_mode == "Hybrid") checked @endif>
                                                <label class="form-check-label custom-lab" for="nonTeachingType" style="color:black!important">Hybrid</label>
                                            </div>


                                        </div>
                                    </div>
                                </div>
                                <div>

                                </div>
                                <div>
                                    <h5 class="my-3" style="color:#004CBD">Educator Requirements</h5>
                                </div>
                                <div class=" col-md-4 mt-4">

                                    <label class="form-label platform_school_label m-0 d-inline-block" style="white-space: nowrap;">
                                        Educator pre-requisites<span class="text-danger">*</span>
                                    </label>
                                </div>
                                <!-- //credentialed_or_non_credentialed -->
                                <div class="col-md-6 mt-4">
                                    <div class="col d-inline-flex align-items-center" style="margin-left:48px">

                                        <div class="form-check-inline d-flex">
                                            <input class="form-check-input custom-input credentialed budget_input"
                                                type="radio"
                                                name="educator_profile"
                                                value="Credentialed"
                                                style="height:18px!important"
                                                @if (empty($budget) || (!empty($budget) && $budget->educator_profile == 'Credentialed')) checked @endif>
                                            <label class="form-check-label custom-lab" for="classType" style="color:black!important">Credentialed</label>
                                        </div>

                                        <div class="form-check-inline d-flex align-items-center">
                                            <input class="form-check-input custom-input non_credentialed budget_input"
                                                type="radio"
                                                name="educator_profile"
                                                id="nonTeachingType"
                                                value="Non-credentialed"
                                                style="height:18px"
                                                @if (!empty($budget) && $budget->educator_profile == 'Non-credentialed') checked @endif>
                                            <label class="form-check-label custom-lab" for="nonTeachingType" style="color:black!important">Non-credentialed</label>
                                        </div>


                                    </div>
                                </div>

                            </div>

                            <!-- special education required -->
                            <div class="row d-flex align-items-center my-3">
                                <div class=" col-md-4">
                                    <label style="width:171px" class="form-label platform_school_label m-0 d-inline-block">Special education certification required<span class="text-danger">*</span></label>
                                </div>
                                <div class="col-md-6" style="margin-left:48px!important">
                                    <div class="col d-inline-flex align-items-center">
                                        <div class="form-check-inline d-flex">
                                            <input class="form-check-input custom-input budget_input"
                                                type="radio"
                                                id="classType"
                                                name="special_education_certification"
                                                value="0"
                                                style="height:18px!important"
                                                @if (empty($budget) || (!empty($budget) && $budget->special_education_certification == 0)) checked @endif>
                                            <label class="form-check-label custom-lab" for="classType" style="color:black!important">No</label>
                                        </div>

                                        <div class="form-check-inline d-flex align-items-center ">
                                            <input class="form-check-input custom-input budget_input"
                                                type="radio"
                                                name="special_education_certification"
                                                id="nonTeachingType"
                                                value="1"
                                                style="height:18px"
                                                @if (!empty($budget) && $budget->special_education_certification == 1) checked @endif>
                                            <label class="form-check-label custom-lab" for="nonTeachingType" style="color:black!important">Yes</label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Relevant Experience -->
                            <div class="row d-flex justify-content-between my-4">
                                <div class="col-lg-4 d-flex align-items-center">
                                    <!-- <label for="deliveryMode" class="form-label platform_school_label d-inline-block">Requirement Overview<span class="text-danger">*</span></label> -->
                                    <label for="deliveryMode" class="form-label d-inline-block pt-3" style="white-space: nowrap;">
                                        Relevant experience <span class="text-danger">*</span>
                                    </label>
                                </div>

                                <div class="col-lg-4 position-relative custom-select-wrapper " style="width:250px;margin-right:79px">
                                    <select class="delivery_mode error_calculate_budget select22 budget_input" name="years_of_experience" style="border-radius:32px!important;max-width:300px!important;padding: 4% 3%">
                                        <option class="px-0" value="" hidden {{ empty($budget) ? 'selected' : '' }}>Select experience</option>
                                        <option class="px-0" value="0-3" {{ !empty($budget) && $budget->years_of_experience == '0-3' ? 'selected' : '' }}>0-3 years of experience</option>
                                        <option class="px-0" value="3-6" {{ !empty($budget) && $budget->years_of_experience == '3-6' ? 'selected' : '' }}>3-6 years of experience</option>
                                        <option class="px-0" value="6-10" {{ !empty($budget) && $budget->years_of_experience == '6-10' ? 'selected' : '' }}>6-10 years of experience</option>
                                        <option class="px-0" value="10+" {{ !empty($budget) && $budget->years_of_experience == '10+' ? 'selected' : '' }}>10+ years of experience</option>
                                    </select>

                                    <div class="custom-select-arrow">
                                        <svg width="8" height="4" viewBox="0 0 8 4" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path fill-rule="evenodd" clip-rule="evenodd" d="M4 2.98204L7.33341 0L8 0.596323L4.33329 3.87652C4.24489 3.95559 4.125 4 4 4C3.875 4 3.75511 3.95559 3.66671 3.87652L0 0.596323L0.666588 0L4 2.98204Z" fill="black"></path>
                                        </svg>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Highest Qualification -->
                            <div class="row d-flex justify-content-between my-4">
                                <div class="col-lg-4 d-flex align-items-center">
                                    <!-- <label for="deliveryMode" class="form-label platform_school_label d-inline-block">Requirement Overview<span class="text-danger">*</span></label> -->
                                    <label for="qualification" class="form-label d-inline-block pt-3" style="white-space: nowrap;">
                                        Highest Qualification <span class="text-danger">*</span>
                                    </label>
                                </div>

                                <div class="col-lg-4 position-relative custom-select-wrapper " style="width:250px;margin-right:79px">
                                    <select class="qualification error_calculate_budget select22 budget_input" name="qualification" style="border-radius:32px!important;max-width:300px!important;padding: 4% 3%">
                                        <option class="px-0" value="" hidden {{ empty($budget) ? 'selected' : '' }}>Select Qualification</option>
                                        <option class="px-0" value="bachelor" {{ !empty($budget) && $budget->qualification == 'bachelor' ? 'selected' : '' }}>Bachelor's Degree</option>
                                        <option class="px-0" value="master" {{ !empty($budget) && $budget->qualification == 'master' ? 'selected' : '' }}>Master's Degree</option>
                                        <option class="px-0" value="doctorate" {{ !empty($budget) && $budget->qualification == 'doctorate' ? 'selected' : '' }}>Doctorate</option>
                                    </select>

                                    <div class="custom-select-arrow">
                                        <svg width="8" height="4" viewBox="0 0 8 4" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path fill-rule="evenodd" clip-rule="evenodd" d="M4 2.98204L7.33341 0L8 0.596323L4.33329 3.87652C4.24489 3.95559 4.125 4 4 4C3.875 4 3.75511 3.95559 3.66671 3.87652L0 0.596323L0.666588 0L4 2.98204Z" fill="black"></path>
                                        </svg>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 new_budget_width">
                            <h5 style="color:#004CBD;margin-top:-29px">Instruction requirements</h5>
                            <div class="col-lg-12">
                                <div class="col-lg-12">
                                    <div class="row d-flex justify-content-between my-4">
                                        <div class="col-lg-7 pt-2 d-flex align-items-center">
                                            <label for="deliveryMode" class="form-label d-inline-block" style="display: flex; align-items: center; gap: 5px;">
                                                Number of instructional days<span class="text-danger">*</span>
                                                <svg data-bs-toggle="tooltip" data-bs-placement="top" title="Enter the total number of days the classes will meet" width="13" height="14" viewBox="0 0 13 14" fill="none" xmlns="http://www.w3.org/2000/svg" style="cursor: pointer;">
                                                    <path d="M6.5 10.25C6.68417 10.25 6.83865 10.1876 6.96345 10.0628C7.08825 9.938 7.15044 9.78373 7.15 9.6V7C7.15 6.81583 7.0876 6.66157 6.9628 6.5372C6.838 6.41283 6.68374 6.35043 6.5 6.35C6.31627 6.34957 6.162 6.41197 6.0372 6.5372C5.9124 6.66243 5.85 6.8167 5.85 7V9.6C5.85 9.78417 5.9124 9.93865 6.0372 10.0635C6.162 10.1883 6.31627 10.2504 6.5 10.25ZM6.5 5.05C6.68417 5.05 6.83865 4.9876 6.96345 4.8628C7.08825 4.738 7.15044 4.58373 7.15 4.4C7.14957 4.21627 7.08717 4.062 6.9628 3.9372C6.83844 3.8124 6.68417 3.75 6.5 3.75C6.31584 3.75 6.16157 3.8124 6.0372 3.9372C5.91284 4.062 5.85044 4.21627 5.85 4.4C5.84957 4.58373 5.91197 4.73822 6.0372 4.86345C6.16244 4.98868 6.3167 5.05087 6.5 5.05ZM6.5 13.5C5.60084 13.5 4.75584 13.3293 3.965 12.9878C3.17417 12.6463 2.48625 12.1833 1.90125 11.5988C1.31625 11.0142 0.853234 10.3263 0.512201 9.535C0.171168 8.74373 0.000434156 7.89873 8.22785e-07 7C-0.000432511 6.10127 0.170301 5.25627 0.512201 4.465C0.854101 3.67373 1.31712 2.98582 1.90125 2.40125C2.48538 1.81668 3.1733 1.35367 3.965 1.0122C4.7567 0.670733 5.6017 0.5 6.5 0.5C7.3983 0.5 8.2433 0.670733 9.035 1.0122C9.8267 1.35367 10.5146 1.81668 11.0988 2.40125C11.6829 2.98582 12.1461 3.67373 12.4885 4.465C12.8308 5.25627 13.0013 6.10127 13 7C12.9987 7.89873 12.828 8.74373 12.4878 9.535C12.1476 10.3263 11.6846 11.0142 11.0988 11.5988C10.5129 12.1833 9.82497 12.6466 9.035 12.9885C8.24504 13.3304 7.40004 13.5009 6.5 13.5ZM6.5 12.2C7.95167 12.2 9.18125 11.6963 10.1888 10.6888C11.1963 9.68125 11.7 8.45167 11.7 7C11.7 5.54833 11.1963 4.31875 10.1888 3.31125C9.18125 2.30375 7.95167 1.8 6.5 1.8C5.04834 1.8 3.81875 2.30375 2.81125 3.31125C1.80375 4.31875 1.3 5.54833 1.3 7C1.3 8.45167 1.80375 9.68125 2.81125 10.6888C3.81875 11.6963 5.04834 12.2 6.5 12.2Z" fill="#C5C6CC" />
                                                </svg>
                                            </label>
                                        </div>

                                        <div class="col-lg-4 position-relative">
                                            <input
                                                value="{{ !empty($budget) ? $budget->instructional_days : '' }}"
                                                placeholder="Enter"
                                                name="instructional_days"
                                                class="w-100 px-3 py-2 instructional_days budget_management_input error_calculate_budget budget_input "
                                                style="border-radius:48px; border:1px solid #000000; color:#000000;" />
                                            <div class=" input_absolute  position-absolute">
                                                days
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-lg-12">
                                <div class="row d-flex justify-content-between my-4">
                                    <div class="col-lg-6 pt-3 d-flex align-items-center">
                                        <label for="deliveryMode" class="form-label platform_school_label d-inline-block">Class duration (in hours)<span class="text-danger">*</span></label><svg data-bs-toggle="tooltip" data-bs-placement="top" title="Select duration of each class.
                                            The class duration is rounded up.
                                            If a class meets for 50 min then select 1 hr.
                                            If a class meets for 1hr 15min then select 1.5 hrs."
                                            width="13" height="14" viewBox="0 0 13 14" fill="none" xmlns="http://www.w3.org/2000/svg" style="cursor: pointer;">
                                            <path d="M6.5 10.25C6.68417 10.25 6.83865 10.1876 6.96345 10.0628C7.08825 9.938 7.15044 9.78373 7.15 9.6V7C7.15 6.81583 7.0876 6.66157 6.9628 6.5372C6.838 6.41283 6.68374 6.35043 6.5 6.35C6.31627 6.34957 6.162 6.41197 6.0372 6.5372C5.9124 6.66243 5.85 6.8167 5.85 7V9.6C5.85 9.78417 5.9124 9.93865 6.0372 10.0635C6.162 10.1883 6.31627 10.2504 6.5 10.25ZM6.5 5.05C6.68417 5.05 6.83865 4.9876 6.96345 4.8628C7.08825 4.738 7.15044 4.58373 7.15 4.4C7.14957 4.21627 7.08717 4.062 6.9628 3.9372C6.83844 3.8124 6.68417 3.75 6.5 3.75C6.31584 3.75 6.16157 3.8124 6.0372 3.9372C5.91284 4.062 5.85044 4.21627 5.85 4.4C5.84957 4.58373 5.91197 4.73822 6.0372 4.86345C6.16244 4.98868 6.3167 5.05087 6.5 5.05ZM6.5 13.5C5.60084 13.5 4.75584 13.3293 3.965 12.9878C3.17417 12.6463 2.48625 12.1833 1.90125 11.5988C1.31625 11.0142 0.853234 10.3263 0.512201 9.535C0.171168 8.74373 0.000434156 7.89873 8.22785e-07 7C-0.000432511 6.10127 0.170301 5.25627 0.512201 4.465C0.854101 3.67373 1.31712 2.98582 1.90125 2.40125C2.48538 1.81668 3.1733 1.35367 3.965 1.0122C4.7567 0.670733 5.6017 0.5 6.5 0.5C7.3983 0.5 8.2433 0.670733 9.035 1.0122C9.8267 1.35367 10.5146 1.81668 11.0988 2.40125C11.6829 2.98582 12.1461 3.67373 12.4885 4.465C12.8308 5.25627 13.0013 6.10127 13 7C12.9987 7.89873 12.828 8.74373 12.4878 9.535C12.1476 10.3263 11.6846 11.0142 11.0988 11.5988C10.5129 12.1833 9.82497 12.6466 9.035 12.9885C8.24504 13.3304 7.40004 13.5009 6.5 13.5ZM6.5 12.2C7.95167 12.2 9.18125 11.6963 10.1888 10.6888C11.1963 9.68125 11.7 8.45167 11.7 7C11.7 5.54833 11.1963 4.31875 10.1888 3.31125C9.18125 2.30375 7.95167 1.8 6.5 1.8C5.04834 1.8 3.81875 2.30375 2.81125 3.31125C1.80375 4.31875 1.3 5.54833 1.3 7C1.3 8.45167 1.80375 9.68125 2.81125 10.6888C3.81875 11.6963 5.04834 12.2 6.5 12.2Z" fill="#C5C6CC" />
                                        </svg>
                                    </div>

                                    <div class="col-lg-4 position-relative custom-select-wrapper ">
                                        <select class="class_duration_budget error_calculate_budget budget_input" style="border-radius:32px!important;border:1px solid black" name="class_duration_hours">
                                            <option value="" disabled selected hidden class="placeholder-option">Select</option>
                                            @for ($minutes = 30; $minutes <= 1440; $minutes +=30)
                                                @php
                                                $decimalHour=$minutes / 60;
                                                $label=fmod($decimalHour, 1)==0 ? intval($decimalHour) . ' Hr' : $decimalHour . ' Hr' ;
                                                @endphp

                                                <option value="{{ $minutes }}"
                                                @if (
                                                (!empty($schoolData) && $schoolData->class_duration == $minutes) ||
                                                (!empty($budget) && $budget->class_duration_hours == $minutes)
                                                ) selected @endif>
                                                {{ $label }}
                                                </option>
                                                @endfor
                                        </select>
                                        <div class="custom-select-arrow">
                                            <svg width="8" height="4" viewBox="0 0 8 4" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path fill-rule="evenodd" clip-rule="evenodd" d="M4 2.98204L7.33341 0L8 0.596323L4.33329 3.87652C4.24489 3.95559 4.125 4 4 4C3.875 4 3.75511 3.95559 3.66671 3.87652L0 0.596323L0.666588 0L4 2.98204Z" fill="black" />
                                            </svg>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <div class="col-lg-12">
                                    <div class="row d-flex justify-content-between my-4">
                                        <div class="col-lg-7 pt-3 d-flex align-items-center">
                                            <label for="deliveryMode" class="form-label platform_school_label d-inline-block">Number of non-instructional hours<span class="text-danger">*</span><svg data-bs-toggle="tooltip" data-bs-placement="top" title="Enter a total number of non-instructional hours"
                                                    width="13" height="14" viewBox="0 0 13 14" fill="none" xmlns="http://www.w3.org/2000/svg" style="cursor: pointer;">
                                                    <path d="M6.5 10.25C6.68417 10.25 6.83865 10.1876 6.96345 10.0628C7.08825 9.938 7.15044 9.78373 7.15 9.6V7C7.15 6.81583 7.0876 6.66157 6.9628 6.5372C6.838 6.41283 6.68374 6.35043 6.5 6.35C6.31627 6.34957 6.162 6.41197 6.0372 6.5372C5.9124 6.66243 5.85 6.8167 5.85 7V9.6C5.85 9.78417 5.9124 9.93865 6.0372 10.0635C6.162 10.1883 6.31627 10.2504 6.5 10.25ZM6.5 5.05C6.68417 5.05 6.83865 4.9876 6.96345 4.8628C7.08825 4.738 7.15044 4.58373 7.15 4.4C7.14957 4.21627 7.08717 4.062 6.9628 3.9372C6.83844 3.8124 6.68417 3.75 6.5 3.75C6.31584 3.75 6.16157 3.8124 6.0372 3.9372C5.91284 4.062 5.85044 4.21627 5.85 4.4C5.84957 4.58373 5.91197 4.73822 6.0372 4.86345C6.16244 4.98868 6.3167 5.05087 6.5 5.05ZM6.5 13.5C5.60084 13.5 4.75584 13.3293 3.965 12.9878C3.17417 12.6463 2.48625 12.1833 1.90125 11.5988C1.31625 11.0142 0.853234 10.3263 0.512201 9.535C0.171168 8.74373 0.000434156 7.89873 8.22785e-07 7C-0.000432511 6.10127 0.170301 5.25627 0.512201 4.465C0.854101 3.67373 1.31712 2.98582 1.90125 2.40125C2.48538 1.81668 3.1733 1.35367 3.965 1.0122C4.7567 0.670733 5.6017 0.5 6.5 0.5C7.3983 0.5 8.2433 0.670733 9.035 1.0122C9.8267 1.35367 10.5146 1.81668 11.0988 2.40125C11.6829 2.98582 12.1461 3.67373 12.4885 4.465C12.8308 5.25627 13.0013 6.10127 13 7C12.9987 7.89873 12.828 8.74373 12.4878 9.535C12.1476 10.3263 11.6846 11.0142 11.0988 11.5988C10.5129 12.1833 9.82497 12.6466 9.035 12.9885C8.24504 13.3304 7.40004 13.5009 6.5 13.5ZM6.5 12.2C7.95167 12.2 9.18125 11.6963 10.1888 10.6888C11.1963 9.68125 11.7 8.45167 11.7 7C11.7 5.54833 11.1963 4.31875 10.1888 3.31125C9.18125 2.30375 7.95167 1.8 6.5 1.8C5.04834 1.8 3.81875 2.30375 2.81125 3.31125C1.80375 4.31875 1.3 5.54833 1.3 7C1.3 8.45167 1.80375 9.68125 2.81125 10.6888C3.81875 11.6963 5.04834 12.2 6.5 12.2Z" fill="#C5C6CC" />
                                                </svg></label>
                                        </div>
                                        <div class="col-lg-4 position-relative">
                                            <input
                                                value="{{ !empty($budget) ? $budget->non_instructional_hours : '' }}"
                                                placeholder="Enter"
                                                class="w-100 px-3 py-2 instructional_days budget_management_input error_calculate_budget budget_input"
                                                style="border-radius:48px; border:1px solid #000000; color:#000000;"
                                                name="non_instructional_hours" />
                                            <div class=" input_absolute  position-absolute">
                                                hours
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <div class="row d-flex justify-content-between my-4">
                                    <div class="col-lg-6 d-flex align-items-center">
                                        <label for="deliveryMode" class="form-label platform_school_label d-inline-block">Language of Instruction<span class="text-danger">*</span><svg data-bs-toggle="tooltip" data-bs-placement="top" title="Select the language of instruction. This tells us if a bilingual educator is required."
                                                width="13" height="14" viewBox="0 0 13 14" fill="none" xmlns="http://www.w3.org/2000/svg" style="cursor: pointer;">
                                                <path d="M6.5 10.25C6.68417 10.25 6.83865 10.1876 6.96345 10.0628C7.08825 9.938 7.15044 9.78373 7.15 9.6V7C7.15 6.81583 7.0876 6.66157 6.9628 6.5372C6.838 6.41283 6.68374 6.35043 6.5 6.35C6.31627 6.34957 6.162 6.41197 6.0372 6.5372C5.9124 6.66243 5.85 6.8167 5.85 7V9.6C5.85 9.78417 5.9124 9.93865 6.0372 10.0635C6.162 10.1883 6.31627 10.2504 6.5 10.25ZM6.5 5.05C6.68417 5.05 6.83865 4.9876 6.96345 4.8628C7.08825 4.738 7.15044 4.58373 7.15 4.4C7.14957 4.21627 7.08717 4.062 6.9628 3.9372C6.83844 3.8124 6.68417 3.75 6.5 3.75C6.31584 3.75 6.16157 3.8124 6.0372 3.9372C5.91284 4.062 5.85044 4.21627 5.85 4.4C5.84957 4.58373 5.91197 4.73822 6.0372 4.86345C6.16244 4.98868 6.3167 5.05087 6.5 5.05ZM6.5 13.5C5.60084 13.5 4.75584 13.3293 3.965 12.9878C3.17417 12.6463 2.48625 12.1833 1.90125 11.5988C1.31625 11.0142 0.853234 10.3263 0.512201 9.535C0.171168 8.74373 0.000434156 7.89873 8.22785e-07 7C-0.000432511 6.10127 0.170301 5.25627 0.512201 4.465C0.854101 3.67373 1.31712 2.98582 1.90125 2.40125C2.48538 1.81668 3.1733 1.35367 3.965 1.0122C4.7567 0.670733 5.6017 0.5 6.5 0.5C7.3983 0.5 8.2433 0.670733 9.035 1.0122C9.8267 1.35367 10.5146 1.81668 11.0988 2.40125C11.6829 2.98582 12.1461 3.67373 12.4885 4.465C12.8308 5.25627 13.0013 6.10127 13 7C12.9987 7.89873 12.828 8.74373 12.4878 9.535C12.1476 10.3263 11.6846 11.0142 11.0988 11.5988C10.5129 12.1833 9.82497 12.6466 9.035 12.9885C8.24504 13.3304 7.40004 13.5009 6.5 13.5ZM6.5 12.2C7.95167 12.2 9.18125 11.6963 10.1888 10.6888C11.1963 9.68125 11.7 8.45167 11.7 7C11.7 5.54833 11.1963 4.31875 10.1888 3.31125C9.18125 2.30375 7.95167 1.8 6.5 1.8C5.04834 1.8 3.81875 2.30375 2.81125 3.31125C1.80375 4.31875 1.3 5.54833 1.3 7C1.3 8.45167 1.80375 9.68125 2.81125 10.6888C3.81875 11.6963 5.04834 12.2 6.5 12.2Z" fill="#C5C6CC" />
                                            </svg></label>
                                    </div>
                                    @php
                                    $languages = json_decode($languages[0]->value); // decode the string into an array
                                    @endphp
                                    <div class="col-lg-4 position-relative custom-select-wrapper ">
                                        <select name="language_of_instruction" class="delivery_mode error_calculate_budget budget_input" style="border-radius:32px!important;border:1px solid black">
                                            <option value="" disabled selected hidden class="placeholder-option">Select</option>
                                            @foreach($languages as $lang)
                                            <option value="{{ $lang }}"
                                                @if (!empty($budget) && $budget->language_of_instruction == $lang) selected @endif>
                                                {{ $lang }}
                                            </option>
                                            @endforeach

                                        </select>
                                        <div class="custom-select-arrow">
                                            <svg width="8" height="4" viewBox="0 0 8 4" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path fill-rule="evenodd" clip-rule="evenodd" d="M4 2.98204L7.33341 0L8 0.596323L4.33329 3.87652C4.24489 3.95559 4.125 4 4 4C3.875 4 3.75511 3.95559 3.66671 3.87652L0 0.596323L0.666588 0L4 2.98204Z" fill="black" />
                                            </svg>
                                        </div>
                                    </div>

                                </div>
                            </div>
                            <div class="col-lg-12">
                                <div class="col-lg-12">
                                    <div class="row d-flex justify-content-between">
                                        <div class="col-lg-6 d-flex align-items-center">
                                            <label for="deliveryMode" class="form-label platform_school_label d-inline-block">Expected class size<span class="text-danger">*</span></label>
                                        </div>
                                        <div class="col-lg-4 position-relative">
                                            <input
                                                value="{{ !empty($budget)?$budget->expected_class_size:'' }}"
                                                placeholder="Enter"
                                                class="w-100 px-3 py-2 instructional_days budget_management_input error_calculate_budget budget_input"
                                                style="border-radius:48px; border:1px solid #000000; color:#000000;"
                                                name="expected_class_size" />
                                            <div class=" input_absolute_students  position-absolute">
                                                students
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <p style="font-size:12px">(Max: 30 students)</p>
                            </div>
                        </div>
                    </div>
                    <div class="d-flex align-items-center mt-5">
                        <input @if(!empty($budget) && $budget->school_curriculum_provided==1) checked @endif type="checkbox" value=1 name="school_curriculum_provided" class="budget_input" style="accent-color: #004CBD;">
                        <p class="ms-3" style="color:black;font-size:15px;font-weight:500">Educator will use my school-provided curriculum and teaching materials on the school’s LMS
                            <svg style="margin-left:9px!important" data-bs-toggle="tooltip" data-bs-placement="top" title="Check this option if your school will provide curriculum and lesson plans for teachers to use on your school's LMS. Leave it unchecked if you want the educator to bring curriculum and lesson plans."
                                width="13" height="14" viewBox="0 0 13 14" fill="none" xmlns="http://www.w3.org/2000/svg" style="cursor: pointer;">
                                <path d="M6.5 10.25C6.68417 10.25 6.83865 10.1876 6.96345 10.0628C7.08825 9.938 7.15044 9.78373 7.15 9.6V7C7.15 6.81583 7.0876 6.66157 6.9628 6.5372C6.838 6.41283 6.68374 6.35043 6.5 6.35C6.31627 6.34957 6.162 6.41197 6.0372 6.5372C5.9124 6.66243 5.85 6.8167 5.85 7V9.6C5.85 9.78417 5.9124 9.93865 6.0372 10.0635C6.162 10.1883 6.31627 10.2504 6.5 10.25ZM6.5 5.05C6.68417 5.05 6.83865 4.9876 6.96345 4.8628C7.08825 4.738 7.15044 4.58373 7.15 4.4C7.14957 4.21627 7.08717 4.062 6.9628 3.9372C6.83844 3.8124 6.68417 3.75 6.5 3.75C6.31584 3.75 6.16157 3.8124 6.0372 3.9372C5.91284 4.062 5.85044 4.21627 5.85 4.4C5.84957 4.58373 5.91197 4.73822 6.0372 4.86345C6.16244 4.98868 6.3167 5.05087 6.5 5.05ZM6.5 13.5C5.60084 13.5 4.75584 13.3293 3.965 12.9878C3.17417 12.6463 2.48625 12.1833 1.90125 11.5988C1.31625 11.0142 0.853234 10.3263 0.512201 9.535C0.171168 8.74373 0.000434156 7.89873 8.22785e-07 7C-0.000432511 6.10127 0.170301 5.25627 0.512201 4.465C0.854101 3.67373 1.31712 2.98582 1.90125 2.40125C2.48538 1.81668 3.1733 1.35367 3.965 1.0122C4.7567 0.670733 5.6017 0.5 6.5 0.5C7.3983 0.5 8.2433 0.670733 9.035 1.0122C9.8267 1.35367 10.5146 1.81668 11.0988 2.40125C11.6829 2.98582 12.1461 3.67373 12.4885 4.465C12.8308 5.25627 13.0013 6.10127 13 7C12.9987 7.89873 12.828 8.74373 12.4878 9.535C12.1476 10.3263 11.6846 11.0142 11.0988 11.5988C10.5129 12.1833 9.82497 12.6466 9.035 12.9885C8.24504 13.3304 7.40004 13.5009 6.5 13.5ZM6.5 12.2C7.95167 12.2 9.18125 11.6963 10.1888 10.6888C11.1963 9.68125 11.7 8.45167 11.7 7C11.7 5.54833 11.1963 4.31875 10.1888 3.31125C9.18125 2.30375 7.95167 1.8 6.5 1.8C5.04834 1.8 3.81875 2.30375 2.81125 3.31125C1.80375 4.31875 1.3 5.54833 1.3 7C1.3 8.45167 1.80375 9.68125 2.81125 10.6888C3.81875 11.6963 5.04834 12.2 6.5 12.2Z" fill="#C5C6CC" />
                            </svg>
                        </p>
                    </div>
                    <hr class="mt-5" style="height:1px; width:100%; border:none; background-color:grey;">
                    <div class="d-flex justify-content-center amount_budget_management my-5">
                        <div class="d-flex gap-2">
                            @if(!empty($budget))
                            <div data-button_class="calculate_budget_button_edit" class="border border-success px-3 py-2 calculate_budget_button_edit" style="background-color:#004CBD;color:white;border-radius:33px;cursor:pointer">
                                Total budget
                            </div>
                            @else
                            <div class="budget_div d-flex align-items-center">
                                <div data-button_class="calculate_budget_button" class="border border-success px-3 py-2 calculate_budget_button" style="background-color:#004CBD;color:white;border-radius:33px;cursor:pointer">
                                    Total Budget
                                </div>
                            </div>
                            @endif
                        <div class="px-3 dollar-prefix py-2 position-relative"
                            style="border: 2px solid #004CBD; border-radius: 33px; color: #004CBD; background-color: #f0f0f0; opacity: 0.7; pointer-events: none;">

                            <input
                                readonly
                                style="outline: none; border: none; font-weight: bold; color: #004CBD; background-color: #f0f0f0;"
                                name="calculated_budget"
                                class="text-center calculated_budget"
                                type="text"
                                value="{{ !empty($budget) ? number_format($budget->calculated_budget, 2) : '_____' }}">
                        </div>
                    </div>
            </div>
            <hr class="mt-5" style="height:1px; width:100%; border:none; background-color:grey;">
        </div>
        <div class="modal-footer py-5 mx-4" style="border-top: 0px; display: flex; justify-content: space-between; width: 95%;background-color:#F2F6FB;border-radius:12px">
            <div class="w-100 d-flex justify-content-between gap-3">
                <p style="color:black;font-weight:bold">
                    Save this budget for future reference
                <p>
                    <button type="button" id="post_requirement_submit_budget" class="btn px-2 py-3" data-dismiss="modal" style="border-radius:21px;border:1px solid black;padding:9px 15px!important;background-color:#004CBD;color:white;font-weight:700;visibility:hidden">Post Requirement</button>
                    @if (!empty($budget))
                    <button class="py-2 px-3"
                        data-id="{{ $budget->id }}"
                        type="button"
                        id="edit_budget"
                        style="border-radius: 21px; padding: 6px 15px !important; border:1px solid black; color:black">
                        Edit Budget
                    </button>
                    @else
                    <button class="py-2 px-3"
                        disabled
                        type="button"
                        id="save_budget_new"
                        style="border-radius: 21px; padding: 6px 15px !important; border:1px solid black; color:black">
                        Save Budget
                    </button>
                    @endif
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="dynamicModal" tabindex="-1" role="dialog" aria-labelledby="dynamicModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" style="max-width:611px!important;" role="document">
        <div class="modal-content p-3 " style="box-shadow: 0 0 30px rgba(0, 0, 0, 0.2);border-radius:23px">
            <div class="modal-body budget_screen_pop_up">
                <h5 class="text-center heading lato_text" style="color:#004CBD;font-size:24px;font-weight:bold">Discard Budget</h5>
                <p style="color:#004CBD ;color:#004CBD" class="text-center lato_text sub_heading">The calculated budget is not saved.Closing this popup will permanently remove the saved budget. Do you want to save it?</p>
            </div>
            <div class="modal-footer d-flex justify-content-between" style="border-top:none">
                <button type="button" class="btn cancel_button_budget_popup first_button" style="padding:2% 15%;border-radius:32px;border:1px solid #787777;color:#787777;font-weight:bold">cancel</button>
                <button type="button" class="btn yes_button_budget_popup second_button" style="padding:2% 17%;border-radius:32px;border:1px solid #004CBD;color:#004CBD;font-weight:bold">Yes</button>
            </div>
        </div>
    </div>
</div>