<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddCboColumnInPlatformSchoolRequirementsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('platform_school_requirements', function (Blueprint $table) {
            $table->unsignedBigInteger('cbo_id')->nullable()->after('capacity');
            $table->unsignedBigInteger('added_by')->nullable()->after('finalize_setup');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('platform_school_requirements', function (Blueprint $table) {
            $table->dropColumn(['cbo_id', 'added_by']);
        });
    }
}
