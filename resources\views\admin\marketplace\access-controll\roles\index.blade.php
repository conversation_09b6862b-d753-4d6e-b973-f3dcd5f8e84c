@extends('admin.layouts.master')
@section('title') Role List | Whizara @endsection
@section('content')
    <main class="content">
        <div class="container-fluid p-0">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url('admin-dashboard') }}" class="text-primary">{{ __('messages.dashboard') }}</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Manage Marketplace</li>
                    <li class="breadcrumb-item active" aria-current="page">Access Control</li>
                    <li class="breadcrumb-item active" aria-current="page">{{ __('messages.rolelist') }} </li>
                    <li class="breadcrumb-item active float-right" aria-current="page"><a href="{{ url('admin/k12connections/add-marketplace-role') }}">{{ __('messages.addrole') }}</a></li>
                </ol>
            </nav>
            <div class="table-responsive">
                <table class="table table-striped admin-dataTable" style="width:100%">
                    <thead class="thead-dark">
                        <tr>
                            <th>{{ __('messages.rolename') }}</th>
                            <th>{{ __('messages.status') }}</th>
                            <th>{{ __('messages.action') }}</th>
                        </tr>
                    </thead>
                    <tbody>
                        @if (!empty($admin) && $admin->count())
                            @foreach ($admin as $key => $data)
                                <tr>
                                    <td>{{ $data->name }}</td>
                                    <td>
                                        @if ($data->status == 0)
                                            <button onclick="status_update('{{ $data->id }}',1)" data-data="1" class="btn btn-warning btn-rounded changestatuscls-{{ $data->id }}">{{ __('messages.deactive') }}</button>
                                        @endif
                                        @if ($data->status == 1)
                                            <button onclick="status_update('{{ $data->id }}',0)" data-data="0" class="btn btn-success btn-rounded changestatuscls-{{ $data->id }}">{{ __('messages.active') }}</button>
                                        @endif
                                    </td>
                                    <td>
                                        <div class="w-100 d-flex justify-content-center align-items-center">
                                            &nbsp;
                                            <a href="{{ url('admin/k12connections/add-permission/' . encrypt_str($data->id)) }}" data-id="{{ encrypt_str($data->id) }}">
                                                <button class="btn btn-rounded btn-block btn-xs btn-outline-danger"><i class="fa fa-lock" aria-hidden="true"></i></button>
                                            </a>
                                            &nbsp;
                                            <a href="{{ url('admin/k12connections/edit-marketplace-role/' . encrypt_str($data->id)) }}">
                                                <button class="btn btn-rounded btn-block btn-xs btn-outline-secondary"><i class="fa fa-pencil" aria-hidden="true"></i></button>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        @else
                            <tr>
                                <td colspan="10">No data found.</td>
                            </tr>
                        @endif
                    </tbody>
                </table>
            </div>
        </div>
    </main>
@endsection
@section('scripts')
    <script>
        function status_update(id) {
            var url = base_url + 'role-update-status';
            var status = $('.changestatuscls-' + id).data('data');

            if (status == 0)
                confirm_message = 'Are you sure you want to Deactivate ?';
            else
                confirm_message = 'Are you sure you want to activate ?';
                update_status(id, url, confirm_message);
        }
    </script>
@endsection
