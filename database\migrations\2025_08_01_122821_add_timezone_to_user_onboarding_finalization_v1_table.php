<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddTimezoneToUserOnboardingFinalizationV1Table extends Migration
{
    public function up()
    {
        Schema::table('user_onboarding_finalization_v1', function (Blueprint $table) {
            $table->string('timezone')->nullable()->after('progress');
        });
    }

    public function down()
    {
        Schema::table('user_onboarding_finalization_v1', function (Blueprint $table) {
            $table->dropColumn('timezone');
        });
    }
}
