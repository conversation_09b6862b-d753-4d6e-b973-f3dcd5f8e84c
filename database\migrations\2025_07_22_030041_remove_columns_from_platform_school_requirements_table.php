<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class RemoveColumnsFromPlatformSchoolRequirementsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('platform_school_requirements', function (Blueprint $table) {
            $table->dropColumn([
                'requirement_name',
                'no_class_dates',
                'schedules',
                'regular_days',
                'profileType_requirements',
                'sw_requirements',
                'bg_check_requirements',
                'medical_requirement',
                'certifications_valid',
                'compensation_type',
                'compensation_amount_min',
                'compensation_amount_max',
                'parent_id',
            ]);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('platform_school_requirements', function (Blueprint $table) {
            $table->string('requirement_name')->nullable();
            $table->text('no_class_dates')->nullable();
            $table->text('schedules')->nullable();
            $table->text('regular_days')->nullable();
            $table->text('profileType_requirements')->nullable();
            $table->text('sw_requirements')->nullable();
            $table->text('bg_check_requirements')->nullable();
            $table->text('medical_requirement')->nullable();
            $table->text('certifications_valid')->nullable();
            $table->string('compensation_type')->nullable();
            $table->decimal('compensation_amount_min', 8, 2)->nullable();
            $table->decimal('compensation_amount_max', 8, 2)->nullable();
            $table->unsignedBigInteger('parent_id')->nullable();
        });
    }
}
