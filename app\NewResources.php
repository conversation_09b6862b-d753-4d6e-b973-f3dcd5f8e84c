<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class NewResources extends Model
{
    use SoftDeletes;
    protected $table = 'new_resources';
    protected $fillable = [
        'title',
        'image',
        'image_caption',
        'tags',
        'summary',
        'content',
        'view_reference',
        'status',
        'created_by',
        'updated_by',
    ];

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updatedBy()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }
}
