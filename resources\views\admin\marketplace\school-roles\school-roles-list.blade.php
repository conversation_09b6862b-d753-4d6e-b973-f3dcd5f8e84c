@extends('admin.layouts.master')

@section('title')Manage School Roles | Whizara @endsection

@section('content')
<?php
    $res = get_permission(session('Adminnewlogin')['type']);
    $marketplacePermissions = isset($res['managemarketplace']) ? json_decode($res['managemarketplace'], true) : [];
?>
{{-- MAIN SECTION START --}}
<main class="content">
    <div class="container-fluid p-0">
        {{-- BREADCRUMB START --}}
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                @if (isset($res['dashboard']))
                    @if (array_key_exists('dashboard', $res))
                        @if (in_array('add', json_decode($res['dashboard'], true)))
                            <li class="breadcrumb-item"><a href="{{ url('admin-dashboard') }}" class="text-primary">{{ __('messages.dashboard') }}</a></li>
                        @endif
                    @endif
                @endif
                @if (in_array('platform schools', $marketplacePermissions))
                    <li class="breadcrumb-item active" aria-current="page">Manage School Roles</li>
                @endif
            </ol>
        </nav>
        {{-- BREADCRUMB END --}}

        {{-- TABLE START --}}
        <div class="table-responsive">
            <table id="dataTable" class="table table-striped dtlist" style="width:100%">
                <thead class="thead-dark">
                    <tr>
                        <th>Name</th>
                        <th>Description</th>
                        <th>Role Status</th>
                        <th class="text-center">Action</th>
                    </tr>
                </thead>
                <tbody>

                </tbody>
            </table>
        </div>
        {{-- TABLE END --}}
    </div>
</main>
{{-- MAIN SECTION END --}}
@endsection

@section('scripts')
<link rel="stylesheet" href="{{ asset('css/datatables.min.css') }}">
<script src="{{ asset('js/datatables.min.js') }}"></script>
<script>
    $(function() {
        $('#dataTable').DataTable({
            processing: true,
            serverSide: false,
            ajax: {
                url: "{{ url('/admin/k12connections/manage-school-roles') }}",
                type: "GET",
                dataSrc: function(json) {
                    if (json.success) {
                        return json.roles;
                    }
                    return [];
                }
            },
            columns: [
                { data: 'name' },
                { data: 'description' },
                { data: 'is_system_role' },
                { data: 'actions', orderable: false, searchable: false }
            ],
        });
    });
</script>
@endsection
