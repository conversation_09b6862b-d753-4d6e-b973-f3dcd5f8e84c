<?php

namespace App\Models;

use App\User;
use App\Classes;
use App\Subject;
use App\Models\k12ConnectionCategorizedData;
use App\Models\PlatformSchoolInvites;
use App\Models\v1\Subject as V1Subject;
use App\Models\v1\UserOpportunityPreference;
use App\Schools;
use Auth;
use Carbon\Carbon;
use Carbon\CarbonPeriod;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Casts\Attribute;

class PlatformSchoolRequirements extends Model
{
    use SoftDeletes;
    protected $table = 'platform_school_requirements';
    protected $fillable = [
        'status',
        'is_visible_to_school',
        'requirement_type',
        'requirement_title',
        'school_id',
        'district_school_id',
        'proctor_id',
        'class_type',
        'delivery_mode',
        'subject_area_id',
        'subject_id',
        'grade_levels_id',
        'capacity',
        'description',
        'requirement_tags',
        'address',
        'city',
        'state',
        'zip_code',
        'country',
        'lat',
        'lng',
        'start_date',
        'end_date',
        'time_zone',
        'other_requirements',
        'benefits',
        'is_valid',
        'totalHours',
        'finalize_setup',
        'profileType_requirements',
        'language_requirements',
        'will_choose_educator',
        'credential_check',
        'special_education_certificate',
        'will_follow_provided_curriculum',
        'provide_schedule_access',
        'no_instrtructional_days',
        'class_duration',
        'no_non_instructional_hr',
        'schedule_type',
        'no_class_dates',
        'regular_days',
        'schedule_1_days',
        'schedule_2_days',
        'sch_cal_screenshot',
        'district_cal_screenshot',
        'teacher_schedule_screenshot',
        'class_details',
        'experience',
        'qualifications',
        'total_budget',
        'benefits',
        'cbo_id',
        'added_by',
        'parent_id',
    ];
    protected $casts = [
        'regular_days'    => 'array',
        'schedule_1_days' => 'array',
        'schedule_2_days' => 'array',
    ];

    protected $appends = ['grade_level_names'];


    protected static function booted()
    {
        static::saved(function ($requirement) {
            $workload = $requirement->calculateWorkingLoad();

            // Only update if value has changed
            if ($requirement->totalHours != $workload['working_hours']) {
                $requirement->totalHours = $workload['working_hours'];
                $requirement->saveQuietly(); // prevent infinite loop of events
            }
        });
    }

    public function subject()
    {
        return $this->belongsTo(V1Subject::class, 'subject_id');
    }

    public function addedBy()
    {
        return $this->belongsTo(User::class, 'added_by');
    }

    public function school()
    {
        return $this->belongsTo(Schools::class, 'school_id');
    }

    public function reviewApplicants()
    {
        return $this->hasMany(SchoolReviewApplicants::class, 'requirement_id');
    }
    public function invites()
    {
        return $this->hasMany(PlatformSchoolInvites::class, 'requirement_id');
    }

    public function classes()
    {
        return $this->hasManyThrough(
            k12ConnectionClasses::class,  // Final table
            k12ConnectionPrograms::class, // Intermediate table
            'requirement_id', // Foreign key in k12ConnectionPrograms (jo `platform_school_requirements` ko reference karega)
            'program_id', // Foreign key in k12ConnectionClasses (jo `k12ConnectionPrograms` ko reference karega)
            'id', // Local key in PlatformSchoolRequirements
            'id' // Local key in k12ConnectionPrograms
        );
    }

    public function getGradeLevelNamesAttribute()
    {
        $ids = explode(',', $this->grade_levels_id);
        return Classes::whereIn('id', $ids)->pluck('class_name')->toArray();
    }

    public function chats()
    {
        return $this->hasMany(Chat::class, 'referance_id', 'id');
    }

    public function proctor()
    {
        return $this->belongsTo(PlatformSchoolProctor::class, 'proctor_id');
    }

    public function meetingLinks()
    {
        return $this->hasMany(k12ConnectionMeetingLinks::class, 'requirement_id');
    }

    public function availibilities()
    {
        return $this->hasMany(k12ConnectionMeetingLinks::class, 'requirement_id');
    }

    public function classType()
    {
        return $this->belongsTo(k12ConnectionCategorizedData::class, 'class_type');
    }

    public function userPreferences()
    {
        return $this->hasMany(UserOpportunityPreference::class, 'opportunity_id');
    }

    public function currentUserPreference()
    {
        return $this->hasOne(UserOpportunityPreference::class, 'opportunity_id')
                    ->where('instructor_id', Auth::guard('instructor')->id())
                    ->select('opportunity_id', 'status');
    }

    public function contracts()
    {
        return $this->hasMany(\App\SchoolRequirementContract::class, 'requirement_id');
    }

    public function timezone()
    {
        return $this->hasOne(k12ConnectionCategorizedData::class, 'id', 'time_zone');
    }

    public function savedByInstructors()
    {
        return $this->userPreferences()->where('status', 'saved');
    }

    public function archivedByInstructors()
    {
        return $this->userPreferences()->where('status', 'archived');
    }


    public function getDetailedScheduleAttribute()
    {
        $merged = [];

        foreach (['regular_days', 'schedule_1_days', 'schedule_2_days'] as $field) {
            $slots = $this->$field ?? [];

            if (is_array($slots)) {
                foreach ($slots as $slot) {
                    if (!isset($slot['day'], $slot['start_time'], $slot['end_time'])) {
                        continue;
                    }

                    $day = $slot['day'];

                    if (!isset($merged[$day])) {
                        $merged[$day] = [];
                    }

                    $merged[$day][] = [
                        'start_time' => $slot['start_time'],
                        'end_time'   => $slot['end_time'],
                    ];
                }
            }
        }

        return $merged;
    }

    public function calculateWorkingLoad()
    {
        $start  = Carbon::parse($this->start_date);
        $end    = Carbon::parse($this->end_date);

        // Parse no_class_dates into Carbon objects
        $skipDates = collect(json_decode($this->no_class_dates))
            ->filter()
            ->map(fn($d) => Carbon::parse(trim($d))->toDateString())
            ->toArray();

        $schedule = $this->detailed_schedule; // already merged accessor

        $totalDays = 0;
        $totalHours = 0;

        foreach (CarbonPeriod::create($start, $end) as $date) {
            $dayName = strtolower($date->format('l')); // monday, tuesday...

            // Skip if in no_class_dates
            if (in_array($date->toDateString(), $skipDates)) {
                continue;
            }

            // Check if schedule exists for this weekday
            if (!isset($schedule[$dayName])) {
                continue;
            }

            $totalDays++;

            foreach ($schedule[$dayName] as $slot) {
                $startTime = Carbon::parse($slot['start_time']);
                $endTime   = Carbon::parse($slot['end_time']);
                $hours     = $endTime->diffInMinutes($startTime) / 60;

                $totalHours += $hours;
            }
        }

        return [
            'working_days'  => $totalDays,
            'working_hours' => $totalHours,
        ];
    }
}