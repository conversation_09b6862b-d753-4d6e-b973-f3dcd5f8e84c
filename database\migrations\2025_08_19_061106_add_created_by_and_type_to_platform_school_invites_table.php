<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddCreatedByAndTypeToPlatformSchoolInvitesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up(): void
    {
        Schema::table('platform_school_invites', function (Blueprint $table) {
            $table->enum('type', ['replacement', 'main', 'stand_by', 'sub'])
                  ->default('main')
                  ->after('invited_by');
        });
    }

   /**
    * Reverse the migrations.
    *
    * @return void
    */
    public function down()
    {
        Schema::table('platform_school_invites', function (Blueprint $table) {
            $table->dropConstrainedForeignId('created_by');
            $table->dropColumn('type');
        });
    }
}
