<?php

namespace App;
use Illuminate\Database\Eloquent\Model;

class SettingTermsModel extends Model
{
    protected $table = 'tbl_content_settings';

    protected $fillable = [
        'type',
        'description',
        'document',
        'locale',
        'status'
    ];

	public static function get_single_record($data)
	{
	 $value=self::where($data)->first();
	 return $value;
	}

	public static function get_all_record($data='')
	{
	 $value=self::where($data)->get();
	 return $value;
	}
}


?>