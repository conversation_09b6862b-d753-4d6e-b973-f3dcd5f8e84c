<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddDeadlineDateAndTimeAndInvitedByColumnInPlatformSchoolInvitesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('platform_school_invites', function (Blueprint $table) {
            $table->date('deadline_date')->nullable()->after('user_id');
            $table->time('deadline_time')->nullable()->after('deadline_date');
            $table->unsignedBigInteger('invited_by')->nullable()->after('deadline_time');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('platform_school_invites', function (Blueprint $table) {
            $table->dropColumn(['deadline_date', 'deadline_time', 'invited_by']);
        });
    }
}
