<?php

namespace App;

use App\Models\PlatformSchoolRequirements;
use App\Models\v1\SchoolUser;
use App\User;
use Illuminate\Database\Eloquent\Model;

class SchoolRequirementContract extends Model
{
    protected $table = 'school_requirement_contracts';
    protected $fillable = [
        'requirement_id',
        'legal_first_name',
        'legal_last_name',
        'phone',
        'email',
        'job_title',
        'address',
        'city',
        'state',
        'zip_code',
        'country',
        'client_name',
        'has_purchase_order',
        'purchase_order_ref',
        'status',
        'created_by_id',
        'created_by_type',
        'updated_by_id',
        'updated_by_type',
    ];

    public function versions()
    {
        return $this->hasMany(SchoolRequirementContractVersion::class, 'school_requirement_contract_id');
    }

    public function requirement()
    {
        return $this->belongsTo(PlatformSchoolRequirements::class, 'requirement_id');
    }

    public function createdBy()
    {
        return $this->morphTo('created_by');
    }

    public function updatedBy()
    {
        return $this->morphTo('updated_by');
    }
}
