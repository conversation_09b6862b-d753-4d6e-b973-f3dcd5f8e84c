<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateUserAvailabilityTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('user_availability_v1', function (Blueprint $table) {
            $table->id()->comment('PK: Unique availability ID');
            $table->foreignId('user_id')
                ->constrained('new_onboarding_instructor')
                ->onDelete('cascade')
                ->comment('FK to new_onboarding_instructor.id');
            $table->enum('day_of_week', [
                'sunday', 'monday', 'tuesday', 'wednesday',
                'thursday', 'friday', 'saturday'
            ])->comment('Day of the week');
            $table->time('start_time')->comment('Start time');
            $table->time('end_time')->comment('End time');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('user_availability_v1');
    }
}

