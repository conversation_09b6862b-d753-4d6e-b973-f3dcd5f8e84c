<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterStatusEnumInPlatformSchoolInvites extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::statement("
            ALTER TABLE `platform_school_invites` 
            CHANGE `status` `status` 
            ENUM('pending','accepted','declined','withdraw') 
            CHARACTER SET utf8mb4 
            COLLATE utf8mb4_unicode_ci 
            NOT NULL 
            COMMENT 'The current status of the user invitation'
        ");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        DB::statement("
            ALTER TABLE `platform_school_invites` 
            CHANGE `status` `status` 
            ENUM('pending','accepted','declined') 
            CHARACTER SET utf8mb4 
            COLLATE utf8mb4_unicode_ci 
            NOT NULL 
            COMMENT 'The current status of the user invitation'
        ");
    }
}
