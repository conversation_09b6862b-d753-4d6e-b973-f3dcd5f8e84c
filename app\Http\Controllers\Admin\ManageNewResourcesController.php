<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\NewResources;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class ManageNewResourcesController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $qry = NewResources::with(['createdBy:id,first_name','updatedBy:id,first_name'])->orderByDesc('id');
        $resources = $qry->get(['id','title','tags','created_by','created_at','updated_by','updated_at','status']);
        return view("admin.new-resources.list", compact('resources'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        return view("admin.new-resources.add");
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'summary' => 'nullable|string',
            'content' => 'nullable|string',
            'status' => 'nullable|in:0,1',
            'image' => 'nullable|image|mimes:jpg,jpeg,png,webp|max:2048',
            'image_caption' => 'nullable|string|max:255',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        $data = $validator->validated();
        $data['tags'] = json_encode($request->tags);
        $data['created_by'] = session()->get('Adminnewlogin')['id'] ?? null;
        $data['updated_by'] = session()->get('Adminnewlogin')['id'] ?? null;

        // Upload Image in s3
        if ($request->hasFile('image')) {
            $image = $request->file('image');
            $filename = 'uploads/new-resources/'.uniqid() . '_' . $image->getClientOriginalName();
            uploads3image($filename,$image);
            $data['image'] = $filename;
        }

        $resource = NewResources::create($data);

        return response()->json(['success' => true, 'message' => 'Resource created successfully', 'data' => $resource]);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $resource = NewResources::with(['createdBy:id,first_name,last_name','updatedBy:id,first_name,last_name'])->findOrFail($id);
        return view('admin.new-resources.view', compact('resource'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $resource = NewResources::findOrFail($id);
        return view('admin.new-resources.edit', compact('resource'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'summary' => 'nullable|string',
            'content' => 'nullable|string',
            'status' => 'nullable|in:0,1',
            'image' => 'nullable|image|mimes:jpg,jpeg,png,webp|max:2048',
            'image_caption' => 'nullable|string|max:255',
            'tags' => 'nullable|array',
            'tags.*' => 'string|max:255',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        $resource = NewResources::findOrFail($id);
        $data = $validator->validated();
        $data['tags'] = json_encode($request->tags ?? []);
        $data['updated_by'] = session()->get('Adminnewlogin')['id'] ?? null;

        // Upload Image in s3
        if ($request->hasFile('image')) {
            $image = $request->file('image');
            $filename = 'uploads/new-resources/'.uniqid() . '_' . $image->getClientOriginalName();
            uploads3image($filename,$image);
            $data['image'] = $filename;
        }

        $resource->update($data);

        return response()->json([
            'success' => true,
            'message' => 'Resource updated successfully',
            'data' => $resource,
        ]);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $resource = NewResources::findOrFail($id);
        $resource->delete();
        return response()->json(['success' => true, 'message' => 'Resource deleted successfully']);
    }

    /**
     * Update status via dropdown
     */
    public function updateStatus(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'status' => 'required|in:0,1',
        ]);
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid status',
                'errors' => $validator->errors(),
            ], 422);
        }

        $resource = NewResources::findOrFail($id);
        $resource->status = (int)$request->status;
        $resource->updated_by = session()->get('Adminnewlogin')['id'] ?? null;
        $resource->save();

        return response()->json(['success' => true, 'message' => 'Status updated successfully']);
    }
}
