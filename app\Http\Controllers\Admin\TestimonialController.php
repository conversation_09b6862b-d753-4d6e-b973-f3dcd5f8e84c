<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Testimonials;
use App\V2\Core\Helpers\ApiResponse;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class TestimonialController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $testimonial = Testimonials::latest()->get();
        return view("admin.testimonials.add", compact("testimonial"));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $request->validate([
            'name.*' => 'required|string|max:255',
            'designation.*' => 'required|string|max:255',
            'description.*' => 'required|string',
            'image.*' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);
        $ids = $request->id ?? [];
        $names = $request->name ?? [];
        $designations = $request->designation ?? [];
        $descriptions = $request->description ?? [];
        $images = $request->file('image');
        foreach ($names as $index => $name) {
            if (!empty($ids[$index])) {
                $testimonial = Testimonials::find($ids[$index]);
                if (!$testimonial) continue;
            } else {
                $testimonial = new Testimonials();
            }
            $testimonial->name = $name;
            $testimonial->designation = $designations[$index];
            $testimonial->description = $descriptions[$index];
            if (isset($images[$index]) && $images[$index]->isValid()) {
                $fileName = time() . '_' . $images[$index]->getClientOriginalName();
                $uploaded = uploads3image($fileName, $images[$index]);
                if ($uploaded) {
                    $url = Storage::disk('s3')->url($fileName);
                    $testimonial->image = $url;
                }
            }
            $testimonial->save();
        }
        return redirect()->route('testimonial')->with('success', 'Testimonials saved successfully.');
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroyAjax(Request $request)
    {
        $testimonial = Testimonials::findOrFail($request->id);
        if ($testimonial->image && file_exists(public_path($testimonial->image))) {
            unlink(public_path($testimonial->image));
        }
        $testimonial->delete();
        return response()->json(['success' => true]);
    }

    // ***************Get-All-Testimonial***************
    public function getAllTestimonial()
    {
        try {
            $testimonial = Testimonials::where('status', 1)->get();
            return ApiResponse::success(["status" => "success", "testimonials" => $testimonial]);
        } catch (Exception $e) {
            return ApiResponse::error("Failed to fetch testimonial: " . $e->getMessage(), 500);
        }
    }
    // ***************Get-All-Testimonial***************
}
