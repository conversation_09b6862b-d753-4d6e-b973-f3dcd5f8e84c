<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class CreateSchoolRoleActionsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('school_role_actions', function (Blueprint $table) {
            $table->unsignedBigInteger('role_id')->comment('FK: school_roles.id');
            $table->unsignedBigInteger('action_id')->comment('FK: core_actions.id');

            $table->primary(['role_id', 'action_id']);

            $table->foreign('role_id')->references('id')->on('school_roles')->onDelete('cascade');
            $table->foreign('action_id')->references('id')->on('core_actions')->onDelete('cascade');
        });

        DB::statement("ALTER TABLE `school_role_actions` COMMENT = 'Maps school roles to their allowed actions'");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('school_role_actions');
    }
}
