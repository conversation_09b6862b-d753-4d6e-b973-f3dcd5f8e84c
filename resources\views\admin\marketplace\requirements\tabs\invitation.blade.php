<!-- Invite Button -->
<div class="d-flex justify-content-end gap-2 align-items-center mb-3">
    <button type="button" class="mx-3 btn btn-primary"
        data-toggle="modal"
        data-target="#mainInstructorInvitePopup"
        id="send-invite"
        onclick="inviteMainInstructor('{{ route('admin.marketplace-requirementsInvite') }}', '{{ encrypt_str($data->id) }}')">
        Main Instructor Invite
    </button>
    <button type="button" class="btn btn-primary"
        data-toggle="modal"
        data-target="#standByInvitePopup"
        id="send-invite"
        onclick="inviteStandbyInstructor('{{ route('admin.marketplace-requirementsInvite') }}', '{{ encrypt_str($data->id) }}')">
        Standby Instructor Invite
    </button>
</div>

<!-- Invite History Table -->
<div class="card">
    <div class="card-header">
        Invite History
    </div>
    <div class="card-body p-0">
        @if($data->invites->count() > 0)
            <div class="table-responsive">
                <table class="table table-striped table-bordered mb-0">
                    <thead class="thead-dark">
                        <tr>
                            <th>#</th>
                            <th>Educator</th>
                            <th>Email</th>
                            <th>Deadline</th>
                            <th>Invited At</th>
                            <th>Invited By</th>
                            <th>Invite Type</th>
                            <th>Status</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($data->invites as $index => $invite)
                            @php
                                $isExpired = \Carbon\Carbon::parse($invite->deadline_date . ' ' . $invite->deadline_time)->isPast();
                                $status = $isExpired ? 'Expired' : ucfirst($invite->status ?? 'Pending');
                                $badgeClass = $isExpired ? 'dark' : ($invite->status === 'accepted' ? 'success' : 'info');
                            @endphp
                            <tr>
                                <td>{{ $index + 1 }}</td>
                                <td data-inviteId="{{ $invite->id }}" data-field="name"><a href="{{ url('admin/k12connections/manage-educator', $invite->educator->id) }}">{{ $invite->educator->first_name }} {{ $invite->educator->last_name }}</a></td>
                                <td data-inviteId="{{ $invite->id }}" data-field="email">{{ $invite->educator->email }}</td>
                                <td data-inviteId="{{ $invite->id }}" data-field="deadline">
                                    {{ \Carbon\Carbon::parse($invite->deadline_date)->format('m-d-Y') }}
                                    {{ \Carbon\Carbon::parse($invite->deadline_time)->format('h:i A') }}
                                </td>
                                <td data-inviteId="{{ $invite->id }}" data-field="created_at">
                                    {{ \Carbon\Carbon::parse($invite->create_at)->format('m-d-Y h:i A') }}
                                </td>
                                <td data-inviteId="{{ $invite->id }}" data-field="invitedBy">
                                    @php $invitedBy = \App\User::find($invite->invited_by); @endphp
                                    {{ $invitedBy->first_name ?? 'N/A' }} {{ $invitedBy->last_name ?? '' }}
                                </td>
                                <td data-inviteId="{{ $invite->id }}" data-field="type">
                                    {{ $invite->type }}
                                </td>
                                <td data-inviteId="{{ $invite->id }}" data-field="status">
                                    <span class="badge badge-{{ $badgeClass }}">{{ $status }}</span>
                                </td>
                                <td data-inviteId="{{ $invite->id }}" data-field="action">
                                    <button class="btn btn-sm btn-primary" @if($status != 'Pending') disabled @endif onclick="withdrawInvite('{{ route('admin.marketplace-updateInviteStatus') }}', '{{ $invite->id }}')">Withdraw</button>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        @else
            <p class="text-muted m-3">No invites sent yet.</p>
        @endif
    </div>
</div>

<!-- Modal (only for sending invites) -->
<div class="modal" id="mainInstructorInvitePopup" tabindex="-1" aria-labelledby="mainInstructorInvitePopup" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-xl">
        <div class="modal-content">
            <!-- Content loaded by AJAX -->
        </div>
    </div>
</div>

<!-- Modal (only for sending invites) -->
<div class="modal" id="standByInvitePopup" tabindex="-1" aria-labelledby="standByInvitePopup" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-xl">
        <div class="modal-content">
            <!-- Content loaded by AJAX -->
        </div>
    </div>
</div>


<!-- Scripts moved to parent file -->
