<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class CreateSchoolRolesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('school_roles', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('school_id')->comment('FK: schools.id');

            $table->string('name', 50)->comment('Role name, e.g., Admin, Proctor');
            $table->string('description')->nullable()->comment('Optional description of role');
            $table->boolean('is_system_role')->default(false)->comment('If true, role is seeded by super admin');

            $table->timestamps();

            $table->unique(['school_id', 'name']);
            $table->foreign('school_id')->references('id')->on('schools')->onDelete('cascade');
        });

        DB::statement("ALTER TABLE `school_roles` COMMENT = 'Defines reusable role templates within a school'");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('school_roles');
    }
}
