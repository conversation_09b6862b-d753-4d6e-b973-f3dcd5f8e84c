@extends('admin.layouts.master')

@section('title') Edit Resource | Whizara @endsection

@section('content')
<main class="content">
	<div class="container-fluid p-0">
		<nav aria-label="breadcrumb">
			<ol class="breadcrumb">
				<li class="breadcrumb-item"><a href="{{ url('admin-dashboard') }}" class="text-primary">{{ __('messages.dashboard') }}</a></li>
				<li class="breadcrumb-item"><a href="{{ route('admin.manage-resources.index') }}" class="text-primary">Manage Resources</a></li>
				<li class="breadcrumb-item active" aria-current="page">Edit Resource</li>
			</ol>
		</nav>

		<div class="card">
			<div class="card-body">
				<form method="POST" action="{{ route('admin.manage-resources.update', ['manage_resource' => $resource->id]) }}" enctype="multipart/form-data" id="resourceEditForm">
					@csrf
					@method('PUT')
					<div class="form-group">
						<label>Title</label>
						<input type="text" name="title" class="form-control" value="{{ $resource->title }}" required>
					</div>
					<div class="form-group">
						<label>Image</label>
						@if($resource->image)
							<div class="mb-2"><img src="{{ generateSignedUrl($resource->image) }}" alt="image" width="120"></div>
						@endif
						<input type="file" name="image" accept="image/*" class="form-control">
					</div>
					<div class="form-group">
						<label>Image Caption</label>
						<input type="text" name="image_caption" value="{{ $resource->image_caption }}" class="form-control">
					</div>
					<div class="form-group">
						<label for="tag-input">Tags</label>
						<div class="d-flex flex-wrap align-items-center" id="tags-container" style="cursor: text; min-height: 45px;">
							<div class="chips d-flex mb-2" style="gap: 3px" id="tag-chips">
								@if(is_array(json_decode($resource->tags, true)))
								@foreach(json_decode($resource->tags, true) as $label)
								<span class="badge bg-primary me-1 mb-1">
									{{$label}}
									<span class="p-1 text-white cursor-pointer remove-tag" style="opacity: 0.7" aria-label="Remove" aria-hidden="true" onclick="($(this).parent().remove())">x</span>
									<input type="hidden" name="tags[]" value="{{$label}}">
								</span>
								@endforeach
								@endif
							</div>
							<input type="text" id="tag-input" class="form-control flex-grow-1" placeholder="Type and press Enter">
						</div>
					</div>
					<div class="form-group">
						<label>Summary</label>
						<textarea name="summary" id="summary" class="form-control" rows="4">{{ $resource->summary }}</textarea>
					</div>
					<div class="form-group">
						<label>Content</label>
						<textarea name="content" id="content" class="form-control" rows="6">{{ $resource->content }}</textarea>
					</div>
					<div class="form-group">
						<label>Status</label>
						<select class="form-control" name="status">
							<option value="1" {{ $resource->status == 1 ? 'selected' : '' }}>Active</option>
							<option value="0" {{ $resource->status == 0 ? 'selected' : '' }}>Inactive</option>
						</select>
					</div>
					<div class="d-flex justify-content-end">
						<a href="{{ route('admin.manage-resources.index') }}" class="btn btn-secondary mr-2">Cancel</a>
						<button type="submit" class="btn btn-primary" id="updateResourceBtn">
							<span class="save-loader" style="display: none;"><i class="fa fa-spinner fa-spin"></i></span>
							Update
						</button>
					</div>
				</form>
			</div>
		</div>
	</div>
</main>
@endsection

@section('scripts')
<script src="https://cdn.ckeditor.com/4.22.1/standard/ckeditor.js"></script>
<script>
	CKEDITOR.replace('summary');
	CKEDITOR.replace('content');

	function createTag(label) {
        if (!label) return;

        // prevent duplicates
        let exists = false;
        $("#tags-container input[name='tags[]']").each(function() {
            if ($(this).val().toLowerCase() === label.toLowerCase()) {
                exists = true;
                return false;
            }
        });
        if (exists) return;
        
        const $tag = $(`
            <span class="badge bg-primary me-1 mb-1">
                ${label}
                <span class="p-1 text-white cursor-pointer remove-tag" style="opacity: 0.7" aria-label="Remove" aria-hidden="true">x</span>
                <input type="hidden" name="tags[]" value="${label}">
            </span>
        `);

        $tag.find(".remove-tag").on("click", function () {
            $tag.remove();
        });

        $("#tag-chips").append($tag);
    }

	$(document).ready(function () {
		// Initialize Select2
		$('#tags').select2({
			placeholder: "Select tags"
		});

		// Set existing tags if available
		@if($resource->tags)
			@php
				$existingTags = json_decode($resource->tags, true);
				if (!is_array($existingTags)) {
					$existingTags = [];
				}
			@endphp
			const existingTags = @json($existingTags);
			if (existingTags && existingTags.length > 0) {
				$('#tags').val(existingTags).trigger('change');
			}
		@endif

		// Generate tags button functionality
		$("#generate-tags").click(function () {
			// Predefined tags
			let tags = ["School", "Locations", "Address", "Educators", "Others"];

			// Select all predefined tags
			$("#tags").val(tags).trigger("change");
		});

		// AJAX form submission
		$('#resourceEditForm').on('submit', function(e) {
			e.preventDefault();

			// Update CKEditor instances
			for (instance in CKEDITOR.instances) {
				CKEDITOR.instances[instance].updateElement();
			}
			
			let title = $('input[name="title"]');
			let summary = $('#summary');
			let content = $('#content');
			let image = $('input[name="image"]');
			let imageCaption = $('input[name="image_caption"]');

			// Client-side validation
			if (!title.val().trim()) {
				title.parent()[0].scrollIntoView()
				alertify.error('Title is required');
				return;
			}
			if (!summary.val().trim()) {
				summary.parent()[0].scrollIntoView()
				alertify.error('Summary is required');
				return;
			}
			if (!content.val().trim()) {
				content.parent()[0].scrollIntoView()
				alertify.error('Content is required');
				return;
			}
			if (image.val() && !imageCaption.val().trim()) {
				imageCaption.parent()[0].scrollIntoView()
				alertify.error('Image Caption is required when an image is uploaded');
				return;
			} else if(imageCaption.val().trim()) {
				imageCaption.val('')
			}

			let formData = new FormData(this);
			let submitBtn = $('#updateResourceBtn');

			// Show loader and disable button
			submitBtn.find('.save-loader').show();
			submitBtn.prop('disabled', true);

			$.ajax({
				url: $(this).attr('action'),
				method: 'POST',
				data: formData,
				processData: false,
				contentType: false,
				headers: {
					'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
				},
				success: function(response) {
					if (response.success) {
						alertify.success(response.message);

						// Redirect after delay
						setTimeout(function() {
							window.location.href = "{{ route('admin.manage-resources.index') }}";
						}, 1500);
					} else {
						alertify.error(response.message || 'Something went wrong');
					}
				},
				error: function(xhr) {
					if (xhr.status === 422) {
						let errors = xhr.responseJSON.errors;
						let errorMsg = Object.values(errors)[0][0];
						alertify.error('Validation error: ' + errorMsg);
					} else {
						alertify.error('An unexpected error occurred');
					}
				},
				complete: function() {
					// Hide loader and enable button
					submitBtn.find('.save-loader').hide();
					submitBtn.prop('disabled', false);
				}
			});
		});
		
        $("#tag-input").on("keydown", function (e) {
            const value = $(this).val().trim();

            if ((e.key === "Enter" || e.key === ",") && value !== "") {
                e.preventDefault();
                createTag(value);
                $(this).val("");
            }

            if (e.key === "Backspace" && value === "") {
                const $lastTag = $("#tags-container .badge").last();
                if ($lastTag.length) $lastTag.remove();
            }
        });

        $("#tags-container").on("click", function () {
            $("#tag-input").focus();
        });

	});
</script>
@endsection


