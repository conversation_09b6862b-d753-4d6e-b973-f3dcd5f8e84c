@extends('admin.layouts.master') @section('title')
View Educator | Whizara
@endsection @section('content')

@php
    $res = get_permission(session('Adminnewlogin')['type']);
    $issetResManageApplication = isset($res['manageapplication']);
    if($issetResManageApplication){
        $arrResManageApplication = json_decode($res['manageapplication'], true);
    }else{
        $arrResManageApplication = [];
    }

    $issetResManageInstructor = isset($res['manageinstructor']);
    if($issetResManageInstructor){
        $arrResManageInstructor = json_decode($res['manageinstructor'] ,true);
    }else{
        $arrResManageInstructor = [];
    }
@endphp

<style>
    .card .card-body.signature {
        height: 450px;
        overflow-y: scroll;
        width: 90%;
        margin: 0 auto;
    }
    .pe-none {
        pointer-events: none;
    }
    .ans-text {
        width: calc(100%);
        border-radius: 12px;
        padding: 10px;
        resize: vertical;
        height: 110px;
    }
    .border-light {
        border-color: #cdd0d3 !important;
    }
    td:first-of-type {
        width: 0% !important;
    }
</style>

<main class="content">
    <div class="container-fluid p-0">
        <!-- BREADCRUMB START -->
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item active" aria-current="page">Educator List</li>
                <li class="breadcrumb-item active" aria-current="page"> View Educator</li>
            </ol>
        </nav>
        <!-- BREADCRUMB END -->
        <!-- PROFILE DETAILS SECTION START -->
        <div class="row justify-content-center">
            <div class="col-lg-2 col-md-3 col-6 text-center">
                <div class="profile_img_box text-center mb-3">
                    @if ($educator->instructor->image != null)
                        <img src="{{ generateSignedUrl($educator->instructor->image) }}" alt="image">
                    @else
                        <img src="{{ default_user_placeholder('male') }}" alt="User" class="img-fluid">
                    @endif
                </div>
            </div>
            <div class="col-lg-10 col-md-9">
                <div class="row">
                    <div class="col-lg-12">
                        <div class="card">
                            <div class="card-header border-bottom">
                                <h5 class="mb-0">Educator Details <b></b></h5>
                                <input type="hidden" name="educator_id" id="educator_id" value="{{ $educator->id }}">
                            </div>
                            <div class="card-body p-0">
                                <ul class="list-group list-group-flush">
                                    <li class="list-group-item d-flex">
                                        <span class="col-lg-3 col-md-3 col-4 pl-0">Educator Name:</span>
                                        <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0">{{$educator->instructor->first_name . ' ' . $educator->instructor->last_name}}</h6>
                                        <span class="col-lg-2 col-md-2 col-4 pl-0">Email :</span>
                                        <h6 class="col-lg-4 col-md-4 col-8 pr-0 mb-0">{{ $educator->instructor->email }} &nbsp;
                                        @if ($educator->instructor->email_verify_status == 1)
                                            <span class="btn btn-success">Verified</span>
                                        @else
                                            <span class="btn btn-danger">Not Verified</span>
                                        @endif
                                        </h6>
                                    </li>
                                    <li class="list-group-item d-flex">
                                        <span class="col-lg-3 col-md-3 col-4 pl-0">City :</span>
                                        <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0">{{$educator->instructor->city}}</h6>
                                        <span class="col-lg-2 col-md-2 col-4 pl-0">State:</span>
                                        <h6 class="col-lg-4 col-md-4 col-8 pr-0 mb-0">{{$educator->instructor->state}}</h6>
                                    </li>
                                    <li class="list-group-item d-flex">
                                        <span class="col-lg-3 col-md-3 col-4 pl-0">Zip :</span>
                                        <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0">{{$educator->instructor->zipcode}}</h6>
                                        <span class="col-lg-2 col-md-2 col-4 pl-0">Country:</span>
                                        <h6 class="col-lg-4 col-md-4 col-8 pr-0 mb-0"> {{$educator->instructor->country}}</h6>
                                    </li>

                                    <li class="list-group-item d-flex">
                                        <span class="col-lg-3 col-md-3 col-4 pl-0">Source :</span>
                                        <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0">{{ $educator->instructor->about }}</h6>
                                        <span class="col-lg-2 col-md-2 col-4 pl-0">Educator :</span>
                                        <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0">{{ $educator->instructor->educator ?? 'Educator Not Defined' }}</h6>
                                    </li>

                                    <li class="list-group-item d-flex">
                                        @php
                                            $res = get_permission(session('Adminnewlogin')['type']);
                                            $perms = [];
                                            // Try multiple known keys for backward compatibility
                                            $keysToTry = ['manage_applicants', 'manageinstructor', 'manageapplication', 'manage_applicant'];
                                            foreach ($keysToTry as $k) {
                                                if (isset($res[$k])) { $perms = json_decode($res[$k] ?? '[]', true) ?: []; break; }
                                            }
                                            $normalizedPerms = array_map(function($v){ return strtolower(trim($v)); }, $perms);
                                            $canStatusChange = in_array('change status', $normalizedPerms);
                                        @endphp
                                        @if($canStatusChange)
                                        <div class="col-lg-3 col-md-3 col-4 pl-0 d-flex align-items-center gap-2">
                                            <input type="checkbox" @if(!empty($educator->instructor->is_background_check)) checked @endif name="is_background_check" id="is_background_check" value="1" data-user-id='{{$educator->instructor->id}}'>
                                            <label class="pt-2 pl-2">Background Check</label>
                                        </div>
                                        @endif

                                        <label class="pt-2 px-3">Substitute</label>
                                        <div class="col-lg-3 col-md-3 col-3 pl-0 d-flex align-items-center gap-2">
                                            <select class="form-control" name="is_sub" id="is_sub">
                                                <option value="2" @if(!empty($educator->open_to_substitute_opportunity == 2)) selected @endif >Hired as sub only</option>
                                                <option value="1" @if(!empty($educator->open_to_substitute_opportunity == 1)) selected @endif >Open to sub roles</option>
                                                <option value="0" @if(!empty($educator->open_to_substitute_opportunity == 0)) selected @endif >Not open to sub roles</option>
                                            </select>
                                        </div>
                                        <input type="hidden" name="user_id" id="user_id" value="{{ $educator->user_id }}">

                                        @if($canStatusChange)
                                            <div class="col-lg-4 col-md-4 pl-0 d-flex align-items-center gap-2">
                                                <label class="pt-2 px-3">Status:</label>
                                                @php
                                                    $normalized = $normalizedPerms ?? [];
                                                    $canStateChangeOnlyLocal = in_array('state change only', $normalized) || in_array('approve/decline', $normalized);
                                                    $canAcceptApplicationLocal = in_array('accept application', $normalized) || in_array('approve & send contract', $normalized);
                                                    $canRejectApplicationLocal = in_array('reject application', $normalized) || in_array('decline & send notification', $normalized);
                                                    $status = $educator->instructor->user_status;

                                                    // Define all possible options with their labels and conditions
                                                    $options = [
                                                        'InProgress' => ['label' => 'Pending'],
                                                        'UnderReview' => ['label' => 'Under Review'],
                                                        'ChangeRequested' => ['label' => 'Request Change'],
                                                        'Unsure' => ['label' => 'Unsure'],
                                                        'ApprovedWithoutContract' => ['label' => 'Approve', 'condition' => $canStateChangeOnlyLocal],
                                                        'RejectWithoutNotification' => ['label' => 'Decline', 'condition' => $canStateChangeOnlyLocal],
                                                        'Approved' => ['label' => 'Approve & Send Contract', 'condition' => $canAcceptApplicationLocal],
                                                        'Withdraw' => ['label' => 'Withdraw', 'condition' => $canAcceptApplicationLocal],
                                                        'Declined' => ['label' => 'Decline & Send Notification', 'condition' => $canRejectApplicationLocal],
                                                        'Active' => ['label' => 'Activate'],
                                                    ];

                                                    // Define the options available for each specific status.
                                                    $statusOptions = [
                                                        'InProgress' => ['InProgress'],
                                                        'UnderReview' => ['UnderReview', 'ChangeRequested', 'ApprovedWithoutContract', 'RejectWithoutNotification', 'Approved', 'Declined', 'Unsure'],
                                                        'Unsure' => ['Unsure', 'ChangeRequested', 'ApprovedWithoutContract', 'RejectWithoutNotification', 'Approved', 'Declined'],
                                                        'ChangeRequested' => ['ChangeRequested', 'RejectWithoutNotification', 'Declined'],
                                                        'Active' => ['Active', 'RejectWithoutNotification', 'Declined'],
                                                        'Declined' => ['Declined', 'ChangeRequested', 'ApprovedWithoutContract', 'RejectWithoutNotification', 'Approved'],
                                                        'Approved' => ['Approved', 'Withdraw', 'RejectWithoutNotification', 'Declined'],
                                                        'ApprovedWithoutContract' => ['ApprovedWithoutContract', 'ChangeRequested', 'Approved', 'RejectWithoutNotification', 'Declined'],
                                                        'RejectWithoutNotification' => ['RejectWithoutNotification', 'ChangeRequested', 'ApprovedWithoutContract', 'Approved', 'Declined'],
                                                    ];

                                                    // Get the allowed options for the current status
                                                    $allowedOptions = $statusOptions[$status] ?? [];
                                                @endphp

                                                <select data-user-id='{{ $educator->instructor->id }}' class="form-control" @if ($status == 'InProgress') disabled @endif id="userStatus" data-toggle="select2" name="userStatus">
                                                    @foreach ($allowedOptions as $optionKey)
                                                        @php
                                                            $optionData = $options[$optionKey];
                                                            $showOption = !isset($optionData['condition']) || $optionData['condition'];
                                                        @endphp

                                                        @if ($showOption)
                                                            <option
                                                                value="{{ $optionKey }}"
                                                                @if ($optionKey == $status) selected hidden @endif>
                                                                {{ $optionData['label'] }}
                                                            </option>
                                                        @endif
                                                    @endforeach
                                                </select>
                                            </div>
                                        @endif
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- PROFILE DETAILS SECTION END -->

        <!-- STEPS SECTION START -->
        <div class="row">
            <div class="col-lg-12">
                <div class="card">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            {{-- STEP-1 US WORK AUTHORIZATION --}}
                            @if ($issetResManageApplication)
                                @if (in_array('US Work Authorization', $arrResManageApplication))
                                    <li class="breadcrumb-item " aria-current="page">
                                        <a href="{{ url('admin/k12connections/application/step1/') }}" class="
                                            <?php
                                                if (request()->segment(4) == 'step1') {
                                                    echo 'text-primary';
                                                }
                                            ?>">
                                            US Work Authorization
                                        </a>
                                    </li>
                                @endif
                            @endif
                            {{-- STEP-1 US WORK AUTHORIZATION END --}}

                            {{-- STEP-3 EDUCATION AND EXPERIENCE --}}
                            @if ($issetResManageApplication)
                                @if (in_array('Education & teaching experience', $arrResManageApplication))
                                    <li class="breadcrumb-item " aria-current="page">
                                        <a href="{{ url('admin/k12connections/application/step3/') }}" class="
                                            <?php
                                                if (request()->segment(4) == 'step3') {
                                                    echo 'text-primary';
                                                }
                                            ?>">
                                            Education & Experience
                                        </a>
                                    </li>
                                @endif
                            @endif
                            {{-- STEP-3 EDUCATION AND EXPERIENCE END--}}

                            {{-- STEP-4 YOUR TEACHING PREFERENCES --}}
                            @if ($issetResManageApplication)
                                @if (in_array('Your Teaching Preferences', $arrResManageApplication))
                                    <li class="breadcrumb-item " aria-current="page">
                                        <a href="{{ url('admin/k12connections/application/step4/') }}" class="
                                            <?php
                                                if (request()->segment(4) == 'step4') {
                                                    echo 'text-primary';
                                                }
                                            ?>">
                                            Your Teaching Preferences
                                        </a>
                                    </li>
                                @endif
                            @endif
                            {{-- STEP-4 YOUR TEACHING PREFERENCES END--}}

                            {{-- STEP-5 PROFILE --}}
                            <li class="breadcrumb-item " aria-current="page">
                                <a href="{{ url('admin/k12connections/application/step5/') }}" class="
                                    <?php
                                        if (request()->segment(4) == 'step5') {
                                            echo 'text-primary';
                                        }
                                    ?>">
                                    Profile
                                </a>
                            </li>
                            {{-- STEP-5 PROFILE--}}

                            {{-- STEP-6 FREE RESPONSES AND QUIZZ --}}
                            @if ($issetResManageApplication)
                                @if (in_array('Quiz', $arrResManageApplication))
                                    <li class="breadcrumb-item " aria-current="page">
                                        <a href="{{ url('admin/k12connections/application/step6/') }}" class="
                                            <?php
                                                if (request()->segment(4) == 'step6') {
                                                    echo 'text-primary';
                                                }
                                            ?>"
                                            >Free Responses and Quiz
                                        </a>
                                    </li>
                                @endif
                            @endif
                            {{-- STEP-6 FREE RESPONSES AND QUIZZ END--}}

                            {{-- STEP-7 AGREEMENT AND CONTRACT --}}
                            <li class="breadcrumb-item " aria-current="page">
                                <a href="{{ url('admin/k12connections/application/step7/') }}" class="
                                    <?php
                                        if (request()->segment(4) == 'step7') {
                                            echo 'text-primary';
                                        }
                                    ?>"
                                    >Agreement
                                </a>
                            </li>
                            {{-- STEP-7 AGREEMENT AND CONTRACT --}}

                            {{-- STEP-8 ASSESSMENT --}}
                            @if ($issetResManageApplication)
                                <li class="breadcrumb-item " aria-current="page">
                                    <a href="{{ url('admin/k12connections/application/step8/') }}" class="
                                        <?php
                                            if (request()->segment(4) == 'step8') {
                                                echo 'text-primary';
                                            }
                                        ?>">
                                        Assesment
                                    </a>
                                </li>
                            @endif
                            {{-- STEP-8 ASSESSMENT END--}}
                        </ol>
                        <ol class="breadcrumb">
                            {{-- AVAILABILITY --}}
                            <li class="breadcrumb-item " aria-current="page">
                                <a href="{{ url('admin/k12connections/manage-educator/'.$educator->user_id.'/availability') }}" class="
                                    <?php
                                        if (request()->segment(5) == 'availability') {
                                            echo 'text-primary';
                                        }
                                    ?>">
                                    Availability
                                </a>
                            </li>
                            {{-- AVAILABILITY END --}}

                            {{-- SUBJECTS --}}
                            <li class="breadcrumb-item " aria-current="page">
                                <a href="{{ url('admin/k12connections/manage-educator/'.$educator->user_id.'/subjects') }}" class="
                                    <?php
                                        if (request()->segment(5) == 'subjects') {
                                            echo 'text-primary';
                                        }
                                    ?>">
                                    Subjects
                                </a>
                            </li>
                            {{-- SUBJECTS END --}}

                            {{-- SUBSTITUTES --}}
                            {{-- SUBSTITUTES END --}}

                            {{-- PHONE NUMBER --}}
                            <li class="breadcrumb-item " aria-current="page">
                                <a href="{{ url('admin/k12connections/manage-educator/'.$educator->user_id.'/phone') }}" class="
                                    <?php
                                        if (request()->segment(5) == 'phone') {
                                            echo 'text-primary';
                                        }
                                    ?>">
                                    Phone Number
                                </a>
                            </li>
                            {{-- PHONE NUMBER END --}}

                            {{-- NOTIFICATION --}}
                            <li class="breadcrumb-item " aria-current="page">
                                <a href="{{ url('admin/k12connections/manage-educator/'.$educator->user_id.'/notification') }}" class="
                                    <?php
                                        if (request()->segment(5) == 'notification') {
                                            echo 'text-primary';
                                        }
                                    ?>">
                                    Notification
                                </a>
                            </li>
                            {{-- NOTIFICATION END --}}

                            {{-- LOCATIONS --}}
                            @if (\Illuminate\Support\Str::contains($educator->instructor->teach, 'in-person'))
                                <li class="breadcrumb-item" aria-current="page">
                                    <a href="{{ url('admin/k12connections/manage-educator/'.$educator->user_id.'/locations') }}" class="{{ request()->segment(5) == 'locations' ? 'text-primary' : '' }}">Locations</a>
                                </li>
                            @endif
                            {{-- LOCATIONS --}}
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
        <!-- STEPS SECTION END -->

        <!-- APPLICATION SECTION START -->
        <div class="row">
            <div class="col-lg-12">
                <div class="card">
                    {{-- STEP-1 US WORK AUTHORIZATION --}}
                    @if ($issetResManageApplication)
                        @if (in_array('US Work Authorization', $arrResManageApplication))
                            @include('admin.marketplace.application-steps.step-1')
                        @endif
                    @endif
                    {{-- STEP-1 US WORK AUTHORIZATION END  --}}

                    {{-- STEP-2 EDUCATION AND EXPERIENCE --}}
                    @if ($issetResManageApplication)
                        @if (in_array('Education & teaching experience', $arrResManageApplication))
                            @include('admin.marketplace.application-steps.step-3')
                        @endif
                    @endif
                    {{-- STEP-2 EDUCATION AND EXPERIENCE END --}}

                    {{-- STEP-4 TEACHING PREFRENCE --}}
                    @if ($issetResManageApplication)
                        @if (in_array('Your Teaching Preferences', $arrResManageApplication))
                            @include('admin.marketplace.application-steps.step-4')
                        @endif
                    @endif
                    {{-- STEP-4 TEACHING PREFRENCE END --}}

                    {{-- STEP-5 PROFILE --}}
                    @include('admin.marketplace.application-steps.step-5')
                    {{-- STEP-5 PROFILE END --}}

                    {{-- STEP-6 FREE RESPONSE AND QUIZ --}}
                    @if ($issetResManageApplication)
                        @if (in_array('Quiz', $arrResManageApplication))
                            @include('admin.marketplace.application-steps.step-6')
                        @endif
                    @endif
                    {{-- STEP-6 FREE RESPONSE AND QUIZ END --}}

                    {{-- STEP-7 PROFILE --}}
                    @include('admin.marketplace.application-steps.step-7')
                    {{-- STEP-7 PROFILE END --}}

                    {{-- STEP-8 ASSESSMENT --}}
                    @include('admin.marketplace.application-steps.step-8')
                    {{-- STEP-8 ASSESSMENT END --}}

                    {{-- AVAILABILITY --}}
                    @include('admin.marketplace.educator.overview-steps.availability')
                    {{-- AVAILABILITY END --}}

                    {{-- SUBJECTS --}}
                    @include('admin.marketplace.educator.overview-steps.subjects')
                    {{-- SUBJECTS END --}}

                    {{-- SUBSTITUTES --}}
                    {{-- SUBSTITUTES END --}}

                    {{-- PHONE NUMBER --}}
                    @include('admin.marketplace.educator.overview-steps.phone')
                    {{-- PHONE NUMBER END --}}

                    {{-- NOTIFICATION --}}
                    @include('admin.marketplace.educator.overview-steps.notification')
                    {{-- NOTIFICATION END --}}

                    {{-- LOCATIONS --}}
                    @include('admin.marketplace.educator.overview-steps.locations')
                    {{-- LOCATIONS END --}}
                </div>
            </div>
        </div>
        <!-- APPLICATION SECTION END -->
    </div>
</main>
<!-- MAIN SECTION END -->
@endsection
@section('scripts')
<script>
  $(document).ready(function () {
    $('#is_sub').on('change', function () {
      const is_sub = $(this).val();
      const user_id = $('#user_id').val(); // Make sure this input exists

      $.ajax({
        url: '{{ route("admin.update-educator-substitute") }}',
        type: 'POST',
        data: {
          is_sub: is_sub,
          user_id: user_id,
          _token: '{{ csrf_token() }}'
        },
        beforeSend: function () {
          // Optionally show a loader
        },
        success: function (response) {
          if (response.success) {
            alertify.success('Substitute preference updated successfully.');
          } else {
            alertify.error(response.message || 'Failed to update.');
          }
        },
        error: function (xhr) {
          if (xhr.status === 422) {
            const errorMsg = xhr.responseJSON.message || 'Validation error occurred.';
            alertify.error('Validation error: ' + errorMsg);
          } else {
            alertify.error('An unexpected error occurred.');
          }
        }
      });
    });
  });
</script>
@endsection