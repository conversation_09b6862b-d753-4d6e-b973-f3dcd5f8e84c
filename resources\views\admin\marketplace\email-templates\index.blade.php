@extends('admin.layouts.master')

@section('title', 'Email Management | Whizara')

@section('content')
<div class="container-fluid">
    <h3 class="mb-4">Email Management</h3>

    <!-- Tabs -->
    <ul class="nav nav-tabs" id="emailTabs" role="tablist">
        <li class="nav-item">
            <a class="nav-link active" id="templates-tab" data-toggle="tab" href="#templates" role="tab">Templates</a>
        </li>
        <li class="nav-item">
            <a class="nav-link" id="layouts-tab" data-toggle="tab" href="#layouts" role="tab">Layouts</a>
        </li>
    </ul>

    <div class="tab-content mt-3">
        <!-- Templates Tab -->
        <div class="tab-pane fade show active" id="templates" role="tabpanel">
            <table id="templatesTable" class="table table-striped table-bordered">
                <thead>
                    <tr>
                        <th>Title</th>
                        <th>Subject</th>
                        <th>Tags</th>
                        <th>Layout</th>
                        <th>Created By</th>
                        <th>Updated By</th>
                        <th>Action</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($templates as $template)
                        <tr>
                            <td>{{ $template->title }}</td>
                            <td>{{ Str::limit($template->subject, 40) }}</td>
                            <td>
                                @if($template->tags)
                                    @foreach($template->tags as $tag)
                                        <span class="badge badge-info">{{ $tag }}</span>
                                    @endforeach
                                @endif
                            </td>
                            <td>{{ optional($template->layout)->title }}</td>
                            <td>{{ optional($template->createdBy)->first_name }}</td>
                            <td>{{ optional($template->updatedBy)->first_name }}</td>
                            <td>
                                <a href="{{ route('admin.email-template.template.edit', $template->id) }}" class="btn btn-sm btn-primary">Edit</a>
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>

        <!-- Layouts Tab -->
        <div class="tab-pane fade" id="layouts" role="tabpanel">
            <table id="layoutsTable" class="table table-striped table-bordered">
                <thead>
                    <tr>
                        <th>Title</th>
                        <th>Tags</th>
                        <th>Created By</th>
                        <th>Updated By</th>
                        <th>Action</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($layouts as $layout)
                        <tr>
                            <td>{{ $layout->title }}</td>
                            <td>
                                @if($layout->tags)
                                    @foreach($layout->tags as $tag)
                                        <span class="badge badge-info">{{ $tag }}</span>
                                    @endforeach
                                @endif
                            </td>
                            <td>{{ optional($layout->createdBy)->first_name }}</td>
                            <td>{{ optional($layout->updatedBy)->first_name }}</td>
                            <td>
                                <a href="{{ route('admin.email-template.layout.edit', $layout->id) }}" class="btn btn-sm btn-primary">Edit</a>
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(function() {
    $('#layoutsTable').DataTable();
    $('#templatesTable').DataTable();
});
</script>
@endpush
