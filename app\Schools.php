<?php

namespace App;

use App\Models\v1\SchoolUser;
use Illuminate\Database\Eloquent\Model;

class Schools extends Model
{
    protected $table = 'schools';
    protected $fillable = [
        'school_name',
        'user_id',
        'nces_id',
        'organization_type',
        'email',
        'phone_number',
        'website_url',
        'image',
        'about',
        'school_rating',
        'address',
        'city',
        'state',
        'district_id',
        'cbo_id',
        'country',
        'zipcode',
        'latitude',
        'longitude',
        'timezone',
        'locale',
        'email_notification',
        'app_notification',
        'customer_type',
        'grade_levels_id',
        'enrollment_count',
        'status',
        'trial_ends_at',
    ];

    public static function get_all_client_record($data)
    {
        $value=self::where($data)->orderBy('id','desc')->get();
        return $value;
    }
}
