<?php

namespace App\Models\v1;

use Illuminate\Database\Eloquent\Model;
use App\OnboardingInstructor;

class UserNotificationPreference extends Model
{
    protected $table = 'user_notification_preferences_v1';

    protected $fillable = [
        'user_id',
        'notification_type',
        'in_app_notifications',
        'email_notifications',
        'realtime_email_notifications',
        'daily_summary_emails',
    ];

    public function instructor()
    {
        return $this->belongsTo(OnboardingInstructor::class, 'user_id');
    }
}
