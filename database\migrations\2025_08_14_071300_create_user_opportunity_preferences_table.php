<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateUserOpportunityPreferencesTable extends Migration
{
    public function up(): void
    {
        Schema::create('user_opportunity_preferences', function (Blueprint $table) {
            $table->id();
            
            // Foreign keys to other tables
            $table->unsignedBigInteger('instructor_id'); // from new_onboarding_instructor
            $table->unsignedBigInteger('opportunity_id'); // from platform_school_requirements
            
            // Enum for preference states
            $table->enum('status', [
                'saved',
                'archived'
            ])->default('saved');
            
            $table->timestamps();
            
            // Foreign key constraints
            $table->foreign('instructor_id')
                ->references('id')
                ->on('new_onboarding_instructor')
                ->onDelete('cascade');
            
            $table->foreign('opportunity_id')
                ->references('id')
                ->on('platform_school_requirements')
                ->onDelete('cascade');
            
            // To avoid duplicate preference entries for the same instructor/opportunity
            $table->unique(['instructor_id', 'opportunity_id']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('user_opportunity_preferences');
    }
}
