<?php
namespace App\Http\Middleware;

use App\Models\v1\SchoolUser;
use App\User;
use Closure;
use Auth;
class CheckSchoolSession
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        if(!session('schoolloginsession')) {
            return redirect('/schools');
        }
        
        $userId = session('schoolloginsession');
        $user = SchoolUser::find($userId['id']);
        if (!$user) {
            session()->forget('schoolloginsession');
            return redirect('/schools');
        }
        if ($user->temp_password_changed == 0 && $user->cust_type =='Platform') {
            session()->forget('schoolloginsession');
            return redirect()->route('/schools/auth/reset-password', ['user_id' => $user->id]);
        }

        return $next($request);
    }
}
