<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddOptionalInviteIdAndOfferDescriptionColumnInPlatformSchoolReviewApplicantsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('platform_school_review_applicants', function (Blueprint $table) {
            $table->unsignedBigInteger('invite_id')->nullable()->after('instructor_id');
            $table->foreign('invite_id')->references('id')->on('platform_school_invites')->onDelete('set null');
            $table->text('offer_description')->nullable()->after('invite_id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('platform_school_review_applicants', function (Blueprint $table) {
            $table->dropForeign(['invite_id']);
            $table->dropColumn(['invite_id', 'offer_description']);
        });
    }
}
