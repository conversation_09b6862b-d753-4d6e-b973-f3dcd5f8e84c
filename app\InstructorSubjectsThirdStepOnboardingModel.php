<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use App\Models\v1\Subject;
use App\Models\v1\SubjectArea;

class InstructorSubjectsThirdStepOnboardingModel extends Model
{
    protected $table = 'onboarding_instructor_subjects';

    public function subSubject()
    {
        return $this->belongsTo(Subject::class, 'sub_subject', 'id');
    }

    public function subjectArea()
    {
        return $this->belongsTo(SubjectArea::class, 'subject', 'id');
    }
}