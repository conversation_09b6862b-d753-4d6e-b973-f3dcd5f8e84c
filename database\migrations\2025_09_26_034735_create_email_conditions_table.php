<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateEmailConditionsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('email_conditions', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('model_name')->comment('Model to evaluate condition on, e.g., Requirement, Class, School');
            $table->string('relation_name')->nullable()->comment('Optional relation name for counts, e.g., invites, logs');
            $table->string('field_name')->nullable()->comment('Field to compare, e.g., created_at, status');
            $table->enum('operator',['=','!=','>','<','>=','<=','in','not_in'])->comment('Comparison operator');
            $table->string('value')->nullable()->comment('Value to compare against, can be numeric, string, or date');
            $table->text('description')->nullable()->comment('Human-readable explanation of the condition');
            $table->integer('priority')->default(0)->comment('Priority for condition evaluation');
            $table->enum('logical_operator',['AND','OR'])->default('AND')->comment('Logical operator with other conditions');
            $table->boolean('active')->default(true)->comment('Whether this condition is active');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('email_conditions');
    }
}
