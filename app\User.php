<?php

namespace App;

use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletes;
use Carbon\Carbon;
class User extends Authenticatable
{
    use Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    use SoftDeletes;
    protected $fillable = [
        'user_id', 'first_name', 'last_name', 'full_name', 'email', 'password', 'temp_password', 'temp_password_changed', 'gender', 'type', 'profile_status', 'image', 'phone_number', 'about', 'address', 'country', 'state', 'city', 'zipcode', 'application', 'district', 'organizationtype', 'organizationname', 'status', 'email_verify_status', 'department', 'cust_type', 'teach', 'teacher_type', 'cbo', 'grade', 'remember_token', 'latitude', 'longitude', 'is_background_check', 'is_medical_check', 'description', 'inpersonrate', 'onlinerate', 'review_deadline', 'email_verify_time', 'start_date', 'submission_date', 'social_id', 'social_type', 'is_contract', 'is_resubmit', 'is_approved', 'is_sub', 'is_special', 'email_notification', 'app_notification', 'website_url','login_at',
    ];

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [
        'password', 'remember_token',
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
    ];

    public function getJWTIdentifier()
    {
        return $this->getKey();
    }
    public function getJWTCustomClaims()
    {
        return [];
    }

    public function availability()
    {
        return $this->hasOne(AvailabilityModel::class);
    }
    public function teachingPreference()
    {
        return $this->hasOne(UserThirdStepModel::class);
    }

    public function programs()
    {
        return $this->belongsToMany(Programs::class, 'tbl_invite_programs', 'user_id', 'program_id')->withPivot('id', 'status', 'is_approved', 'type', 'admin_type', 'replacement_type', 'has_requested', 'parent_id', 'is_makeup', 'deadline', 'is_sub_only','is_standby','requested_by')->where('program_status','!=', 'Draft');
    }
    public function mainInsProgram()
    {
        $currentDate = now()->toDateString();
        return $this->belongsToMany(Programs::class, 'program_notes', 'user_id', 'program_id')
            ->withPivot('program_id', 'user_id', 'attendance', 'rating', 'content_taught', 'other_feedback', 'comment', 'note', 'class_date', 'day', 'start_time', 'end_time', 'status')
            ->where('program_status', '!=', 'Draft')  // Filters programs not in 'Draft' status
            ->whereNotNull('program_notes.user_id')   // Ensures 'user_id' is not null
            ->whereNull('program_notes.sub_user_id');
            // ->where("start_date", "<",  $currentDate)
            // ->where("end_date", "<",  $currentDate); // Ensures 'sub_user_id' is null
    }

    public function standby()
    {
        return $this->belongsToMany(Programs::class, 'tbl_invite_programs', 'user_id', 'program_id')->withPivot('id', 'status', 'is_approved', 'type', 'admin_type', 'replacement_type', 'has_requested', 'parent_id', 'is_makeup', 'deadline', 'is_sub_only','is_standby','requested_by')->where('program_status','!=', 'Draft')->where('tbl_invite_programs.is_standby',1)->where('tbl_invite_programs.status',1)->whereNull('tbl_invite_programs.admin_type')->whereNotNull('tbl_invite_programs.is_approved');
    }

    public function availableLocations()
    {
        return $this->hasMany(AvailablityLocationModel::class);
    }



    public function activeClasses()
    {
        $currentDate = now()->toDateString();
        return $this->hasMany(ProgramNote::class, 'user_id')->whereNull(['program_notes.status','program_notes.note','program_notes.sub_user_id'])->where('program_notes.class_date', '>=', $currentDate);
    }
    public function activeSubClasses()
    {
        $currentDate = now()->toDateString();
        return $this->hasMany(ProgramNote::class, 'sub_user_id')->whereNull(['program_notes.status','program_notes.note'])->where('program_notes.class_date', '>=', $currentDate);
    }
    /*public function activeSubClasses()
    {
        $currentDate = now()->toDateString();

        return $this->hasManyThrough(
            ProgramNote::class,
            ProgramNoteSub::class,
            'user_id',  // Foreign key on ProgramNoteSub
            'id',       // Local key on User
            'id',       // Local key on ProgramNote
            'program_note_id'  // Foreign key on ProgramNote
        )
            ->where('program_notes.status', null)
            ->where('program_notes.class_date', '>=', $currentDate);
    }

    public function subProgramNotes()
    {
        return $this->hasManyThrough(
            ProgramNote::class,
            ProgramNoteSub::class,
            'user_id',  // Foreign key on ProgramNoteSub
            'id',       // Local key on User
            'id',       // Local key on ProgramNote
            'program_note_id'  // Foreign key on ProgramNote
        );
    }
    */

    public function programNotes()
    {
        return $this->hasMany(ProgramNote::class)->orderBy('program_notes.class_date');
    }
        public function subProgramNotes()
    {
        return $this->hasMany(ProgramNote::class,'sub_user_id')->orderBy('program_notes.class_date');
    }

    /**
     * Method to get program notes for the next day
     */
    public function tomorrowClasses()
    {
        $class_date = now()->addDay()->toDateString();

        return $this->programNotes()->where('class_date', $class_date);
    }

    /**
     * Method to get program notes for the today
     */
    public function todayClasses()
    {
        $class_date = now()->toDateString();

        return $this->programNotes()->where('class_date', $class_date);
    }

        /**
     * Method to get program notes for the next day
     */
    public function tomorrowSubClasses()
    {
        $class_date = now()->addDay()->toDateString();

        return $this->subProgramNotes()->where('class_date', $class_date);
    }

    /**
     * Method to get program notes for the today
     */
    public function todaySubClasses()
    {
        $class_date = now()->toDateString();

        return $this->subProgramNotes()->where('class_date', $class_date);
    }

    public function programNoteAmounts()
    {
        return $this->hasMany(ProgramNoteAmount::class);
    }
    /**
     * Scope a query to only include active users.
     */
    public function scopeActive(Builder $query): void
    {
        $query->where('users.status', "1");
        if(@session('Adminnewlogin')['type']==4){
            $query->join(
                "tbl_invite_application_recruiters",
                "tbl_invite_application_recruiters.application_id",
                "=",
                "users.id"
            );
            $query->where(["tbl_invite_application_recruiters.type" => "Recruiter", "tbl_invite_application_recruiters.user_id" => session('Adminnewlogin')['id']]);
            $query->orderBy("tbl_invite_application_recruiters.id", "desc");
        }

    }
    /**
     * Scope a query to only include special users.
     */
    public function scopeSpecial(Builder $query): void
    {
        $query->where('is_special', "1");
    }
    /**
     * Get completed programs by user.
     */
    public function completedPrograms()
    {
        $currentDate = now()->toDateString();

        return $this->belongsToMany(Programs::class, 'tbl_invite_programs', 'user_id', 'program_id')
            ->where(function ($query) use ($currentDate) {
                $query->where('program_status', 'Completed')
                    ->orWhere('end_date', '<', $currentDate);
            })
            ->where([
                'tbl_invite_programs.is_approved' => '1',
                'tbl_invite_programs.status' => '1',
            ])
            ->where('tbl_invite_programs.is_standby', '!=','1')
            ->withPivot('id', 'is_approved');
    }

    public function completedClassPrograms()
    {
        $currentDate = now()->toDateString();

        return $this->belongsToMany(Programs::class, 'tbl_invite_programs', 'user_id', 'program_id')
            // ->where(function ($query) use ($currentDate) {
            //     $query->where('program_status', 'Completed')
            //         ->orWhere('end_date', '<', $currentDate);
            // })
            // ->whereHas('programNoteAmounts')
            ->where([
                'tbl_invite_programs.is_approved' => '1',
                'tbl_invite_programs.status' => '1',
            ])
            ->where('tbl_invite_programs.is_standby', '!=','1')
            ->withPivot('id', 'is_approved');
    }


    public function reimbursements()
    {
        return $this->hasMany(Reimbursement::class);
    }

    public function backgroundVerifications()
    {
        return $this->hasMany(BackgroundMedicalModel::class, 'application_id');
    }


    /**
     * Get completed programs by user.
     */
    public function activePrograms()
    {
        $currentDate = now()->toDateString();

        return $this->belongsToMany(Programs::class, 'tbl_invite_programs', 'user_id', 'program_id')
            ->where(function ($query) use ($currentDate) {
                $query
                    // ->where('program_status', '!=', 'Completed')
                    ->where('end_date', '>', $currentDate);
            })
            ->where([
                'tbl_invite_programs.is_approved' => '1',
                'tbl_invite_programs.status' => '1',
                'tbl_invite_programs.has_requested' => '0',
            ]);
    }

    public function recruitedPrograms()
    {

        return $this->belongsToMany(Programs::class, 'tbl_invite_program_owners', 'user_id', 'program_id');
    }

    public function createdPrograms()
    {

        return $this->hasMany(Programs::class, 'created_by');
    }

    /**
     * Scope a query to only include substitute users.
     */
    public function scopeSubstitute(Builder $query): void
    {
        $query->where('is_sub', "1");
    }

    public function subjects()
    {
        return $this->hasMany(UserSubjectsModel::class, 'user_id');
    }

    public function certificates()
    {
        return $this->hasMany(UserSecondStepModel::class, 'user_id');
    }

    /**
     * Get active standby programs for user.
     */
    public function activeStandByPrograms()
    {
        $currentDate = now()->toDateString();

        return $this->belongsToMany(Programs::class, 'tbl_invite_programs', 'user_id', 'program_id')
            ->where(function ($query) use ($currentDate) {
                $query
                    ->where('end_date', '>', $currentDate);
            })
            ->where([
                'tbl_invite_programs.is_standby' => '1',
                'tbl_invite_programs.is_approved' => '1',
                'tbl_invite_programs.status' => '1',
            ]);
    }

    public function nextWeekClasses()
    {
        $startOfNextWeek = Carbon::now()->startOfWeek()->addWeek()->toDateString();
        $endOfNextWeek = Carbon::now()->endOfWeek()->addWeek()->toDateString();

        return $this->hasMany(ProgramNote::class, 'user_id')
        ->whereNull(['program_notes.status','program_notes.note','program_notes.sub_user_id'])
        ->whereBetween('class_date', [$startOfNextWeek, $endOfNextWeek]);
    }

    public function nextWeekSubClasses()
    {
        $startOfNextWeek = Carbon::now()->startOfWeek()->addWeek()->toDateString();
        $endOfNextWeek = Carbon::now()->endOfWeek()->addWeek()->toDateString();

        return $this->hasMany(ProgramNote::class, 'sub_user_id')
        ->whereNull(['program_notes.status','program_notes.note'])
        ->whereBetween('class_date', [$startOfNextWeek, $endOfNextWeek]);
    }

    public function notifications()
    {
        return $this->hasMany(notification::class, 'user_id');
    }

    public function unreadNotifications()
    {
        return $this->notifications()->unread()->count();
    }

    public function invitePrograms()
    {
        return $this->hasMany(invite_programs::class, 'user_id');
    }

    public function hourlyRates()
    {
        return $this->hasOne(UserFourthStepModel::class);
    }

    public function chatMessages()
    {
        return $this->hasOne(LastChatModel::class, 'to_id');
    }

    public function requirements()
    {
        return $this->hasMany(NewSchoolPostRequirementModel::class, 'id', 'requirement_id');
    }

    public function shortList()
    {
        return $this->hasOne(ShortlistInstructorModel::class, 'user_id', 'id');
    }

    public function education()
    {
        return $this->hasOne(UserEducationModel::class, 'user_id');
    }
}
