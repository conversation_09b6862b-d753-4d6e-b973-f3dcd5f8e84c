<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Blade;
use App\View\Components\Marketplace\Educator\ProgressStepper;
use App\View\Components\Marketplace\Admin\EducatorInvite;

class ViewServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap any application services.
     */
    public function boot()
    {
        Blade::component('marketplace.educator.progress-stepper', ProgressStepper::class);
        Blade::component('marketplace.admin.educator-invite', EducatorInvite::class);
    }

    /**
     * Register any application services.
     */
    public function register()
    {
        //
    }
}
