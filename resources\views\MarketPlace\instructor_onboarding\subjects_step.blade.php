<style>
    .subject-row {
        background-color: #fff;
        border: 1px solid #e0e0e0;
        border-radius: 16px;
    }
    select.subject_fields {
        border: 2px solid black;
        border-radius: 50px;
        padding: 13px;
    }
    .slider-container {
        position: relative;
        height: 20px;
        /* Provides space for the thumb and track */
        display: flex;
        align-items: center;
    }

    .slider-range {
        -webkit-appearance: none;
        appearance: none;
        width: 100%;
        height: 12px;
        /* The thickness of the track line */
        background: linear-gradient(to right, #003da5 0%, #d4e3f5 0%);
        /* Initial gradient */
        border-radius: 50px;
        outline: none;
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        z-index: 1;
    }

    /* --- Webkit (Chrome, Safari) Thumb --- */
    .slider-range::-webkit-slider-thumb {
        -webkit-appearance: none;
        appearance: none;
        width: 18px;
        height: 18px;
        border-radius: 50%;
        background: #003da5;
        /* Solid blue thumb from the design */
        cursor: pointer;
        position: relative;
        z-index: 3;
        /* Ensures thumb is on top */
    }

    /* --- <PERSON><PERSON> (Firefox) Thumb --- */
    .slider-range::-moz-range-thumb {
        width: 18px;
        height: 18px;
        border-radius: 50%;
        background: #003da5;
        cursor: pointer;
        border: none;
        position: relative;
        z-index: 3;
    }

    .slider-dots {
        position: absolute;
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        pointer-events: none;
        z-index: 2;
    }

    .slider-dots span {
        height: 9px;
        width: 9px;
        background-color: #003da5;
        border-radius: 50%;
        border: 2px solid #003da5;
        box-sizing: border-box;
        margin-left: 4px;
    }
    .slider-dots span.active {
        border-color: #fff;
        background-color: #fff;
    }
    .add-more-btn {
        background-color: transparent;
        border: 2px solid #003da5;
        color: #003da5;
        border-radius: 50px;
        padding: 8px 16px;
        font-weight: 500;
    }
    .add-more-btn:hover {
        background-color: #003da5;
        color: #fff;
    }
    input[type="file"].form-control {
        padding: 10px 16px;
        border-radius: 50px;
        background-color: #f8f9fa;
    }
    textarea.form-control {
        padding: 12px 16px;
        font-size: 1rem;
        background-color: #f8f9fa;
    }
    .image-preview {
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        border: 2px solid #003da5;
        padding: 10px 15px;
        margin-top: 10px;
        margin-left: 12px;
        border-radius: 50px;
        width: fit-content;
        background-color: #f5f7fa;
        font-size: 14px;
    }

    .download-icon svg {
        vertical-align: middle;
        transition: transform 0.2s ease;
    }

    .download-icon:hover svg {
        transform: scale(1.1);
    }

    .select2-container--default .select2-selection--single{
        background-color: #fff !important;
        border: 1px solid #aaa !important;
        border-radius: 50px !important;
        padding: 22px !important;
    }
    span#select2-subject_0-container{
        margin-top: -12px !important;
    }
    .select2-container--default .select2-selection--single .select2-selection__arrow {
        top: 9px !important;
        right: 15px !important;
    }
</style>
@for ($i = 0; $i < 7; $i++)
    {{-- Set to 1 for initial UI --}}
    @php
        $sliderColors = [
        0 => '#3C89FB',
        1 => '#FC697D',
        2 => '#7EC7A9',
        3 => '#FC9F43',
        4 => '#3C89FB',
        5 => '#FC697D',
        6 => '#FC9F43',
    ];
        $proficiency = !empty($userSubjects[$i]) ? $userSubjects[$i]['proficiency'] : 5; // Default selected proficiency
        $ra1 = ($proficiency / 5) * 100;
        $sty1 = "linear-gradient(to right, {$sliderColors[0]} 0%, {$sliderColors[0]} {$ra1}%, #d4e3f5 {$ra1}%, #d4e3f5 100%)";
    @endphp
    <div class="subject-row @if(empty($userSubjects[$i])) d-none @endif p-4 rounded-4 shadow-sm mb-4">
        <label class="form-label fw-semibold text-dark mb-2">Subject<span class="text-danger">*</span></label>
        <select class="form-select sub_subject select2 subject_fields" name="subjects[{{$i}}][subject_code]" id="subject_{{$i}}" data-placeholder="Search and select" value="@if(!empty($userSubjects) && !empty($userSubjects[$i])){{ $userSubjects[$i]['subject_code'] }}@endif" >
            @foreach ($subjectAreas as $subjectArea)
                <optgroup data-id="{{$subjectArea->id}}" label="{{$subjectArea->subject_area}}" data-selected="@if(!empty($userSubjects) && !empty($userSubjects[$i])){{ $userSubjects[$i]['subject_code'] }}@endif"></optgroup>
            @endforeach
        </select>
        <input type="hidden" name="subjects[{{$i}}][subject_name]" id="subject_area_id_{{$i}}" value="">

        <label class="form-label fw-semibold text-dark mt-4 mb-2">Proficiency</label>
        <div class="slider-container">
            <div id="proficiency-dots" class="slider-dots">
                <span style="border-color: #003da5 !important; background-color: #003da5 !important;"></span>
                <span></span>
                <span></span>
                <span></span>
                <span></span>
                <span style="margin-right: 3px;"></span>
            </div>
            <input type="range" id="proficiency-slider{{ $i > 0 ? $i + 1 : '' }}" min="0" max="5" step="1" class="slider-range input-slider{{ $i > 0 ? $i + 1 : '' }}" value="{{ $proficiency }}" data-color="{{ $sliderColors[$i] }}" style="background:{{$sty1}}" name="subjects[{{$i}}][proficiency]" oninput="updateSliderVisuals(this)" />
	    <p class="number--label{{ $i > 0 ? $i + 1 : '' }} rangenumber" style="margin-bottom:0px;">{{ $proficiency }}</p>
        </div>

        <div class="mb-4 mt-4">
            <label class="form-label fw-semibold text-dark mb-2">Upload a Lesson Plan</label>
            <input type="file" id="lesson_plan_{{ $key }}" class="form-control lesson_plan_{{$i}}" style="padding: 18px; border-radius: 25px; opacity: 0; position: absolute; z-index:-1 width: 100%;" name="subjects[{{$i}}][lesson_plan]" accept=".doc,.docx,.pdf,.png,.jpg,.jpeg">
            <span class="lesson_plan_{{$i}}" style="padding: 12px 20px;border: 1px solid #d8dadc;width: 100%;display: flex;border-radius: 32px;justify-content: space-between;color: #757575;align-items: center;">
                Upload a file
                <svg width="17" height="17" viewBox="0 0 17 17"
                    fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                        d="M16 11V14.3333C16 14.7754 15.8244 15.1993 15.5118 15.5118C15.1993 15.8244 14.7754 16 14.3333 16H2.66667C2.22464 16 1.80072 15.8244 1.48816 15.5118C1.17559 15.1993 1 14.7754 1 14.3333V11"
                        stroke="#A7A9B7" stroke-width="1.5" stroke-linecap="round"
                        stroke-linejoin="round">
                    </path>
                    <path d="M13.3333 5.16667L9.16667 1L5 5.16667" stroke="#A7A9B7"
                        stroke-width="1.5" stroke-linecap="round"
                        stroke-linejoin="round"></path>
                    <path d="M9 2V12" stroke="#A7A9B7" stroke-width="1.5"
                        stroke-linecap="round" stroke-linejoin="round"></path>
                </svg>
            </span>
            @php
                $filePath = $userSubjects[$i]['file_url'] ?? '';
                $fileUrl = $filePath ? \Illuminate\Support\Facades\Storage::disk('s3')->url($filePath) : null;
            @endphp

            @if(!empty($fileUrl) && !empty($userSubjects[$i]['file_name']))
                <span class="uploaded-documents d-flex align-items-center">
                    <a href="{{ $fileUrl }}" download title="Download" class="text_of_file image-preview d-flex align-items-center text-decoration-none">
                        <span class="file-name me-2 text-primary">{{ basename($userSubjects[$i]['file_name']) }}</span>
                        <span class="download-icon ms-2 fw-5">
                            <svg width="20" height="20" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M8.5 1V11M8.5 11L4.5 7M8.5 11L12.5 7" stroke="#004CBD" stroke-width="1.9" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M2.5 15H14.5" stroke="#004CBD" stroke-width="1.9" stroke-linecap="round"/>
                            </svg>
                        </span>
                    </a>
                </span>
            @endif
        </div>
        <div class="mb-4">
            <label class="form-label fw-semibold text-dark mb-2">Description</label>
            <textarea name="subjects[{{$i}}][description]" rows="2" class="form-control form-control-lg rounded-4 pt-4 pb-0" placeholder="Enter description or notes for review purposes">@if(!empty($userSubjects) && !empty($userSubjects[$i])){{ $userSubjects[$i]['lesson_plan_note'] }}@endif</textarea>
        </div>

    </div>
@endfor

<!-- Add More button -->
<button type="button" id="addMoreSubjects" class="@if(!$moreSubjects) d-none @endif btn add-more-btn d-inline-flex align-items-center">
    <i class="fa fa-plus me-2"></i> Add More
</button>

<script>
    // Function to update the slider's visual state
    function updateSliderVisuals(slider) {
        const value = parseInt(slider.value, 10);
        const min = parseInt(slider.min, 10);
        const max = parseInt(slider.max, 10);

        // 1. Calculate the percentage for the gradient background
        // We add a small offset to the percentage to ensure the color
        // extends slightly past the dot for a cleaner look.
        const percentage = ((value - min) / (max - min)) * 100;

        // 2. Update the background gradient of the slider track
        slider.style.background = `linear-gradient(to right, #003da5 ${percentage}%, #d4e3f5 ${percentage}%)`;

        // 3. Update the dot border colors
        const dots = document.querySelectorAll('#proficiency-dots span');
        dots.forEach((dot, index) => {
            if (index <= value) {
                dot.classList.add('active');
            } else {
                dot.classList.remove('active');
            }
        });
    }
    // Ensure the slider is correctly styled on initial page load
    const initialSlider = document.getElementById('proficiency-slider');
    if (initialSlider) {
        updateSliderVisuals(initialSlider);
    }
    $('input[name="add_more_subjects"]').on('change', function() {
        console.log('Add more subjects changed');
        const moreSubjects = $('[name="add_more_subjects"]:checked').val();
        $('#addMoreSubjects').toggleClass('d-none', moreSubjects !== 'yes');
    });
</script>
