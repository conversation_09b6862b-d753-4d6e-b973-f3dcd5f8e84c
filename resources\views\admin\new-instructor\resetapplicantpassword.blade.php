@extends('web.onboarding-new.layouts.master')

@section('title')
    Reset Password | Whizara
@endsection

@section('content')
<style>
    .loginarea__wraper {
        box-shadow: none !important;
        border: none;
        padding: 21px 20px;
    }

    a#platformschoolresetpasswordformbtn {
        margin-top: 0 !important;
    }

    .footerareas {
        display: none;
    }
    .login__form.eye-btnn.eye-right {
        margin-top: 10px !important;
    }

    .login__form {
        margin-top: 0px !important;
    }
</style>
<div class="container">
    <div class="row justify-content-center align-items-center">
        <!-- Left Side: Image -->
        <div class="col-lg-6 col-md-6 text-center mb-md-0">
            <img src="{{ asset('website/img/logo/image.png') }}" alt="Login Illustration" class="img-fluid" style="max-width: 100%; height: auto; width: 75%; box-sizing: border-box;">
        </div>

        <!-- Right Side: Form -->
        <div class="col-lg-6 col-md-6">
            <div class="loginarea__wraper">
                <div class="text-start mb-4">
                    <img src="{{ asset('website/img/logo/whizara-new-logo.svg') }}" alt="Whizara Logo" style="height: 75px;">
                    <p class="mt-3 font-weight-bold" style="font-size: 22px; font-weight: 800;">Let's Set Up Your Instructor Account</p>
                    <p style="color: #666;">You’ve been invited to join Whizara! Complete your account setup by verifying your temporary password and setting a new one.</p>
                </div>

                <form class="pt-3" action="javascript:void(0);" method="post" id="resetapplicantpasswordform">
                    <input type="hidden" name="user_id" value="{{ $newApplicant->id }}">
                    <input type="hidden" name="user_temp_id" value="{{ $newApplicant->user_id }}">
                    <div class="row">
                        <div class="col-xl-12 col-md-12 ">
                            <div class="login__form">
                                <label class="form__label">Email address*</label>
                                <input class="common__login__input" type="text" placeholder="Enter Email Address" id="email" name="email" value="{{ $newApplicant->email }}">
                                <div style="color: red;" class="email_error w-100"></div>
                            </div>
                        </div>

                        <div class="col-xl-12 col-md-12 ">
                            <div class="login__form eye-btnn eye-right">
                                <label class="form__label">Temporary Password*</label>
                                <input class="common__login__input passid" type="password" placeholder="Enter Password" id="tempPassword" name="temp_password">
                                <div style="color: red;" class="password_error w-100"></div>
                            </div>
                        </div>

                        <div class="col-xl-12 col-md-12 ">
                            <div class="login__form eye-btnn eye-right">
                                <label class="form__label">New Password*</label>
                                <input class="common__login__input passid" type="password" placeholder="Enter Password" id="newPassword" name="new_password" value="">
                                <span id="eyeclose_new" onclick="togglePassword('newPassword', 'eyeclose_new', 'eyeopen_new')">
                                    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M8.94409 4.08409C9.29485 4.03032 9.64923 4.00358 10.0041 4.00409C13.1841 4.00409 16.1741 6.29409 17.9141 10.0041C17.648 10.5687 17.3474 11.1164 17.0141 11.6441C16.9083 11.8079 16.8527 11.9991 16.8541 12.1941C16.8563 12.4123 16.9299 12.6238 17.0636 12.7964C17.1972 12.9689 17.3837 13.0929 17.5944 13.1496C17.8052 13.2063 18.0287 13.1924 18.2309 13.1102C18.433 13.0279 18.6027 12.8818 18.7141 12.6941C19.18 11.962 19.5847 11.1927 19.9241 10.3941C19.9777 10.2693 20.0054 10.1349 20.0054 9.99909C20.0054 9.86327 19.9777 9.72887 19.9241 9.60409C17.9041 4.91409 14.1041 2.00409 10.0041 2.00409C9.53476 2.00173 9.06618 2.04189 8.60409 2.12409C8.47277 2.14642 8.34713 2.19439 8.23435 2.26527C8.12157 2.33615 8.02385 2.42855 7.94678 2.53719C7.8697 2.64584 7.81479 2.7686 7.78516 2.89847C7.75553 3.02834 7.75177 3.16277 7.77409 3.29409C7.79642 3.42541 7.84439 3.55105 7.91527 3.66383C7.98615 3.77662 8.07855 3.87433 8.18719 3.95141C8.29584 4.02848 8.4186 4.0834 8.54847 4.11303C8.67834 4.14266 8.81277 4.14642 8.94409 4.12409V4.08409ZM1.71409 0.294092C1.62085 0.200853 1.51016 0.126892 1.38834 0.0764319C1.26652 0.0259715 1.13595 0 1.00409 0C0.872232 0 0.741664 0.0259715 0.619842 0.0764319C0.49802 0.126892 0.38733 0.200853 0.294092 0.294092C0.105788 0.482395 0 0.73779 0 1.00409C0 1.27039 0.105788 1.52579 0.294092 1.71409L3.39409 4.80409C1.97966 6.16562 0.853948 7.79808 0.0840915 9.60409C0.0290282 9.73026 0.000606775 9.86643 0.000606775 10.0041C0.000606775 10.1417 0.0290282 10.2779 0.0840915 10.4041C2.10409 15.0941 5.90409 18.0041 10.0041 18.0041C11.8012 17.9917 13.5559 17.4566 15.0541 16.4641L18.2941 19.7141C18.3871 19.8078 18.4977 19.8822 18.6195 19.933C18.7414 19.9838 18.8721 20.0099 19.0041 20.0099C19.1361 20.0099 19.2668 19.9838 19.3887 19.933C19.5105 19.8822 19.6211 19.8078 19.7141 19.7141C19.8078 19.6211 19.8822 19.5105 19.933 19.3887C19.9838 19.2668 20.0099 19.1361 20.0099 19.0041C20.0099 18.8721 19.9838 18.7414 19.933 18.6195C19.8822 18.4977 19.8078 18.3871 19.7141 18.2941L1.71409 0.294092ZM8.07409 9.48409L10.5241 11.9341C10.3551 11.9826 10.1799 12.0061 10.0041 12.0041C9.47366 12.0041 8.96495 11.7934 8.58988 11.4183C8.21481 11.0432 8.00409 10.5345 8.00409 10.0041C8.00204 9.82828 8.02562 9.6531 8.07409 9.48409ZM10.0041 16.0041C6.82409 16.0041 3.83409 13.7141 2.10409 10.0041C2.75018 8.57784 3.66716 7.29067 4.80409 6.21409L6.57409 8.00409C6.15834 8.76289 5.99983 9.63604 6.12235 10.4925C6.24487 11.3491 6.64181 12.1428 7.25362 12.7546C7.86543 13.3664 8.65912 13.7633 9.51563 13.8858C10.3721 14.0084 11.2453 13.8498 12.0041 13.4341L13.5941 15.0041C12.5052 15.645 11.2675 15.9897 10.0041 16.0041Z" fill="#6F6C90"></path></svg>
                                </span>

                                <span id="eyeopen_new" onclick="togglePassword('newPassword', 'eyeclose_new', 'eyeopen_new')" style="display:none;cursor: pointer;">
                                    <svg width="20" height="16" viewBox="0 0 20 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M19.9235 7.60391C17.9035 2.91391 14.1035 0.00390625 10.0035 0.00390625C5.90348 0.00390625 2.10348 2.91391 0.0834848 7.60391C0.0284215 7.73007 0 7.86625 0 8.00391C0 8.14156 0.0284215 8.27774 0.0834848 8.40391C2.10348 13.0939 5.90348 16.0039 10.0035 16.0039C14.1035 16.0039 17.9035 13.0939 19.9235 8.40391C19.9785 8.27774 20.007 8.14156 20.007 8.00391C20.007 7.86625 19.9785 7.73007 19.9235 7.60391ZM10.0035 14.0039C6.83348 14.0039 3.83348 11.7139 2.10348 8.00391C3.83348 4.29391 6.83348 2.00391 10.0035 2.00391C13.1735 2.00391 16.1735 4.29391 17.9035 8.00391C16.1735 11.7139 13.1735 14.0039 10.0035 14.0039ZM10.0035 4.00391C9.21236 4.00391 8.439 4.2385 7.7812 4.67803C7.12341 5.11755 6.61072 5.74227 6.30797 6.47317C6.00522 7.20408 5.926 8.00834 6.08034 8.78427C6.23468 9.56019 6.61565 10.2729 7.17506 10.8323C7.73447 11.3917 8.4472 11.7727 9.22312 11.927C9.99905 12.0814 10.8033 12.0022 11.5342 11.6994C12.2651 11.3967 12.8898 10.884 13.3294 10.2262C13.7689 9.56839 14.0035 8.79503 14.0035 8.00391C14.0035 6.94304 13.5821 5.92562 12.8319 5.17548C12.0818 4.42533 11.0644 4.00391 10.0035 4.00391ZM10.0035 10.0039C9.60792 10.0039 9.22124 9.88661 8.89234 9.66685C8.56345 9.44708 8.3071 9.13473 8.15573 8.76927C8.00435 8.40382 7.96474 8.00169 8.04191 7.61373C8.11908 7.22576 8.30957 6.8694 8.58927 6.58969C8.86898 6.30999 9.22534 6.11951 9.6133 6.04234C10.0013 5.96517 10.4034 6.00477 10.7689 6.15615C11.1343 6.30752 11.4467 6.56387 11.6664 6.89277C11.8862 7.22166 12.0035 7.60834 12.0035 8.00391C12.0035 8.53434 11.7928 9.04305 11.4177 9.41812C11.0426 9.79319 10.5339 10.0039 10.0035 10.0039Z" fill="#6F6C90"></path></svg>
                                </span>
                                <div style="color: red;" class="password_error w-100"></div>
                            </div>
                        </div>

                        <div class="col-xl-12 col-md-12 ">
                            <div class="login__form eye-btnn eye-right">
                                <label class="form__label">Confirm Password*</label>
                                <input class="common__login__input passid" type="password" placeholder="Enter Password" id="confirmPassword" name="confirm_password" value="">
                                <span id="eyeclose_confirm" onclick="togglePassword('confirmPassword', 'eyeclose_confirm', 'eyeopen_confirm')">
                                    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M8.94409 4.08409C9.29485 4.03032 9.64923 4.00358 10.0041 4.00409C13.1841 4.00409 16.1741 6.29409 17.9141 10.0041C17.648 10.5687 17.3474 11.1164 17.0141 11.6441C16.9083 11.8079 16.8527 11.9991 16.8541 12.1941C16.8563 12.4123 16.9299 12.6238 17.0636 12.7964C17.1972 12.9689 17.3837 13.0929 17.5944 13.1496C17.8052 13.2063 18.0287 13.1924 18.2309 13.1102C18.433 13.0279 18.6027 12.8818 18.7141 12.6941C19.18 11.962 19.5847 11.1927 19.9241 10.3941C19.9777 10.2693 20.0054 10.1349 20.0054 9.99909C20.0054 9.86327 19.9777 9.72887 19.9241 9.60409C17.9041 4.91409 14.1041 2.00409 10.0041 2.00409C9.53476 2.00173 9.06618 2.04189 8.60409 2.12409C8.47277 2.14642 8.34713 2.19439 8.23435 2.26527C8.12157 2.33615 8.02385 2.42855 7.94678 2.53719C7.8697 2.64584 7.81479 2.7686 7.78516 2.89847C7.75553 3.02834 7.75177 3.16277 7.77409 3.29409C7.79642 3.42541 7.84439 3.55105 7.91527 3.66383C7.98615 3.77662 8.07855 3.87433 8.18719 3.95141C8.29584 4.02848 8.4186 4.0834 8.54847 4.11303C8.67834 4.14266 8.81277 4.14642 8.94409 4.12409V4.08409ZM1.71409 0.294092C1.62085 0.200853 1.51016 0.126892 1.38834 0.0764319C1.26652 0.0259715 1.13595 0 1.00409 0C0.872232 0 0.741664 0.0259715 0.619842 0.0764319C0.49802 0.126892 0.38733 0.200853 0.294092 0.294092C0.105788 0.482395 0 0.73779 0 1.00409C0 1.27039 0.105788 1.52579 0.294092 1.71409L3.39409 4.80409C1.97966 6.16562 0.853948 7.79808 0.0840915 9.60409C0.0290282 9.73026 0.000606775 9.86643 0.000606775 10.0041C0.000606775 10.1417 0.0290282 10.2779 0.0840915 10.4041C2.10409 15.0941 5.90409 18.0041 10.0041 18.0041C11.8012 17.9917 13.5559 17.4566 15.0541 16.4641L18.2941 19.7141C18.3871 19.8078 18.4977 19.8822 18.6195 19.933C18.7414 19.9838 18.8721 20.0099 19.0041 20.0099C19.1361 20.0099 19.2668 19.9838 19.3887 19.933C19.5105 19.8822 19.6211 19.8078 19.7141 19.7141C19.8078 19.6211 19.8822 19.5105 19.933 19.3887C19.9838 19.2668 20.0099 19.1361 20.0099 19.0041C20.0099 18.8721 19.9838 18.7414 19.933 18.6195C19.8822 18.4977 19.8078 18.3871 19.7141 18.2941L1.71409 0.294092ZM8.07409 9.48409L10.5241 11.9341C10.3551 11.9826 10.1799 12.0061 10.0041 12.0041C9.47366 12.0041 8.96495 11.7934 8.58988 11.4183C8.21481 11.0432 8.00409 10.5345 8.00409 10.0041C8.00204 9.82828 8.02562 9.6531 8.07409 9.48409ZM10.0041 16.0041C6.82409 16.0041 3.83409 13.7141 2.10409 10.0041C2.75018 8.57784 3.66716 7.29067 4.80409 6.21409L6.57409 8.00409C6.15834 8.76289 5.99983 9.63604 6.12235 10.4925C6.24487 11.3491 6.64181 12.1428 7.25362 12.7546C7.86543 13.3664 8.65912 13.7633 9.51563 13.8858C10.3721 14.0084 11.2453 13.8498 12.0041 13.4341L13.5941 15.0041C12.5052 15.645 11.2675 15.9897 10.0041 16.0041Z" fill="#6F6C90"></path></svg>
                                </span>

                                <span id="eyeopen_confirm" onclick="togglePassword('confirmPassword', 'eyeclose_confirm', 'eyeopen_confirm')" style="display:none;cursor: pointer;">
                                    <svg width="20" height="16" viewBox="0 0 20 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M19.9235 7.60391C17.9035 2.91391 14.1035 0.00390625 10.0035 0.00390625C5.90348 0.00390625 2.10348 2.91391 0.0834848 7.60391C0.0284215 7.73007 0 7.86625 0 8.00391C0 8.14156 0.0284215 8.27774 0.0834848 8.40391C2.10348 13.0939 5.90348 16.0039 10.0035 16.0039C14.1035 16.0039 17.9035 13.0939 19.9235 8.40391C19.9785 8.27774 20.007 8.14156 20.007 8.00391C20.007 7.86625 19.9785 7.73007 19.9235 7.60391ZM10.0035 14.0039C6.83348 14.0039 3.83348 11.7139 2.10348 8.00391C3.83348 4.29391 6.83348 2.00391 10.0035 2.00391C13.1735 2.00391 16.1735 4.29391 17.9035 8.00391C16.1735 11.7139 13.1735 14.0039 10.0035 14.0039ZM10.0035 4.00391C9.21236 4.00391 8.439 4.2385 7.7812 4.67803C7.12341 5.11755 6.61072 5.74227 6.30797 6.47317C6.00522 7.20408 5.926 8.00834 6.08034 8.78427C6.23468 9.56019 6.61565 10.2729 7.17506 10.8323C7.73447 11.3917 8.4472 11.7727 9.22312 11.927C9.99905 12.0814 10.8033 12.0022 11.5342 11.6994C12.2651 11.3967 12.8898 10.884 13.3294 10.2262C13.7689 9.56839 14.0035 8.79503 14.0035 8.00391C14.0035 6.94304 13.5821 5.92562 12.8319 5.17548C12.0818 4.42533 11.0644 4.00391 10.0035 4.00391ZM10.0035 10.0039C9.60792 10.0039 9.22124 9.88661 8.89234 9.66685C8.56345 9.44708 8.3071 9.13473 8.15573 8.76927C8.00435 8.40382 7.96474 8.00169 8.04191 7.61373C8.11908 7.22576 8.30957 6.8694 8.58927 6.58969C8.86898 6.30999 9.22534 6.11951 9.6133 6.04234C10.0013 5.96517 10.4034 6.00477 10.7689 6.15615C11.1343 6.30752 11.4467 6.56387 11.6664 6.89277C11.8862 7.22166 12.0035 7.60834 12.0035 8.00391C12.0035 8.53434 11.7928 9.04305 11.4177 9.41812C11.0426 9.79319 10.5339 10.0039 10.0035 10.0039Z" fill="#6F6C90"></path></svg>
                                </span>
                                <div style="color: red;" class="password_error w-100"></div>
                            </div>
                        </div>

                        <div class="form-group form-check mb-3 m-4">
                            <input type="checkbox" class="form-check-input" name="terms" id="terms" required>
                            <label class="form-check-label" for="terms">I agree to the <a href="https://www.whizara.com/termsofservice">Whizara Terms of service</a> and <a href="https://www.whizara.com/privacy-policy">Privacy Policy</a></label>
                            <div class="terms_error" style="color: red;"></div>
                        </div>
                    </div>
                    <div class="login__button">
                        <a class="default__button text-right" href="javascript:void(0);" id="resetapplicantpasswordformbtn" type="button" data-loading-text="<i class='fa fa-spinner fa-spin'></i>Sign In">Sign In</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection