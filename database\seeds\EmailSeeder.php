<?php

use App\Models\v1\{EmailLayout, EmailTemplate};
use Illuminate\Database\Seeder;

class EmailSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $layout = EmailLayout::create([
            'title' => 'School Temaplate Layout',
            'slug' => 'school-temaplate-layout',
            'header_html' => '<h1>Welcome Header</h1>',
            'footer_html' => '<p>Footer text</p>',
            'tags' => ['school', 'sender:system'],
            'created_by' => 1,
        ]);
        EmailTemplate::create([
            'title' => 'Send Credencial',
            'slug' => 'school_user_send_credencial',
            'layout_id' => $layout->id,
            'subject' => 'Welcome to our platform!',
            'body_html' => '<p>Hello {{name}}, thanks for registering.</p>',
            'tags' => ['school', 'user', 'send', 'credencial'],
            'created_by' => 1,
        ]);
    }
}
