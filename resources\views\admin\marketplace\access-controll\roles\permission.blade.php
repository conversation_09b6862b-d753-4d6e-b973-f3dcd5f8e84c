@extends('admin.layouts.master')
@section('title') Permission Page | Whizara @endsection
@section('content')
<main class="content">
    <div class="container-fluid p-0">
        <!-- BREADCRUMB START -->
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ url('admin-dashboard') }}" class="text-primary">Dashboard</a></li>
                <li class="breadcrumb-item active" aria-current="page">Manage Marketplace</li>
                <li class="breadcrumb-item active" aria-current="page">Access Control</li>
                <li class="breadcrumb-item active" aria-current="page"><a href="{{ url('admin/k12connections/manage-marketplace-roles') }}">Role</a></li>
                <li class="breadcrumb-item active" aria-current="page">Set Permission</li>
            </ol>
        </nav>
        <!-- BREADCRUMB END -->

        <!-- PERMISSION FORM START -->
        <form id="marketplace_roles_permission_form">
            @csrf
            <input type="hidden" name="roleId" id="roleId" value="{{ $roleId }}">
            <div class="card">
                <div class="card-body">
                    <div id="permissionsAccordion" class="accordion"></div>
                    <div class="d-flex justify-content-md-end justify-content-between mt-3 pt-3">
                        <button type="button" class="btn btn-secondary mr-2">Cancel</button>
                        <button type="submit" id="submitmarketplacerolespermissionbutton" class="btn btn-primary">Submit</button>
                    </div>
                </div>
            </div>
        </form>
        <!-- PERMISSION FORM END -->
    </div>
</main>

<style>
/* Accordion UX tweaks */
.accordion-toggle { cursor: pointer; }
.accordion-toggle .chevron { transition: transform .2s ease; transform: rotate(0deg); }
.accordion-toggle:not(.collapsed) .chevron { transform: rotate(90deg); }
</style>

{{-- Jquery CDN --}}
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
(function($){
    // Track features the user actually interacted with
    const touchedFeatures = new Set();
    // Get existing permissions from PHP
    const existingPermissions = @json($existingPermissions ?? []);

    // Define modules + features + actions with dependencies
    const modules = [
        {
            name: 'Educators',
            features: {
                'Manage Applicants': {
                    actions: ['View List', 'View', 'Edit', 'Delete', 'Observation Note', 'Change Status', 'Approve/Decline', 'Approve & Send Contract', 'Decline & Send Notification'],
                    dependencies: {
                        'view_list': {
                            type: 'prerequisite',
                            appliesTo: ['view', 'edit', 'delete', 'change_status', 'approve_send_contract', 'decline_send_notification', 'observation_note', 'approve_decline']
                        },
                        'change_status': {
                            type: 'prerequisite',
                            appliesTo: ['approve_decline', 'approve_send_contract', 'decline_send_notification']
                        }
                    }
                },
                'Manage Educator': {
                    actions: ['View', 'Change Status'],
                    dependencies: {
                        'view': {
                            type: 'prerequisite',
                            appliesTo: ['change_status']
                        }
                    }
                },
                'Subject Approval': {
                    actions: ['View'],
                    dependencies: {}
                }
            }
        }
    ];

    // Slugify helper
    function slugify(text) {
        return String(text).toLowerCase().replace(/[^a-z0-9]+/g, '_').replace(/^_|_$/g, '');
    }

    // Renderer
    function renderPermissionsAccordion(targetSelector, modulesData) {
        const $accordion = $(targetSelector).empty();

        modulesData.forEach(function(module, moduleIndex){
            const moduleId = 'module_'+moduleIndex+'_'+slugify(module.name);
            const $moduleCard = $(`
                <div class="card mb-2">
                    <div class="card-header p-2" id="${moduleId}_header">
                        <div class="accordion-toggle d-flex align-items-center justify-content-between" role="button" data-toggle="collapse" data-target="#${moduleId}_collapse" aria-expanded="true" aria-controls="${moduleId}_collapse">
                            <h6 class="mb-0">${module.name}</h6>
                            <svg class="chevron" width="16" height="16" viewBox="0 0 16 16" fill="currentColor" xmlns="http://www.w3.org/2000/svg"><path d="M5.5 3.5l5 4.5-5 4.5" stroke="currentColor" stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round"/></svg>
                        </div>
                    </div>
                    <div id="${moduleId}_collapse" class="collapse show" data-parent="#permissionsAccordion">
                        <div class="card-body p-2">
                            <div id="${moduleId}_features" class="accordion"></div>
                        </div>
                    </div>
                </div>
            `);

            const $featuresAccordion = $moduleCard.find('#'+moduleId+'_features');

            Object.keys(module.features).forEach(function(featureName, featureIndex){
                const featureId = moduleId+'_feature_'+featureIndex+'_'+slugify(featureName);
                const featureData = module.features[featureName];

                const $featureCard = $(`
                    <div class="card">
                        <div class="card-header p-2" id="${featureId}_header">
                            <div class="accordion-toggle d-flex align-items-center justify-content-between collapsed" role="button" data-toggle="collapse" data-target="#${featureId}_collapse" aria-expanded="false" aria-controls="${featureId}_collapse">
                                <h6 class="mb-0">${featureName}</h6>
                                <svg class="chevron" width="14" height="14" viewBox="0 0 16 16" fill="currentColor" xmlns="http://www.w3.org/2000/svg"><path d="M5.5 3.5l5 4.5-5 4.5" stroke="currentColor" stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round"/></svg>
                            </div>
                        </div>
                        <div id="${featureId}_collapse" class="collapse" data-parent="#${moduleId}_features">
                            <div class="card-body p-2 d-flex flex-wrap" id="${featureId}_body"></div>
                        </div>
                    </div>
                `);

                const $featureBody = $featureCard.find('#'+featureId+'_body');
                const featureKey = slugify(featureName);
                let existing = [];
                if (existingPermissions[featureKey] && existingPermissions[featureKey].permission_setting) {
                    try {
                        existing = JSON.parse(existingPermissions[featureKey].permission_setting);
                    } catch (e) {
                        const setting = existingPermissions[featureKey].permission_setting;
                        if (setting && setting !== '[]') {
                            existing = setting.replace(/[\[\]"]/g, '').split(', ');
                        }
                    }
                }

                featureData.actions.forEach(function(action){
                    const actionKey = slugify(action);
                    const inputName = featureKey+'[]';
                    const inputId = featureId+'_action_'+actionKey;
                    const isChecked = existing.includes(action) || existing.includes(actionKey);

                    const $check = $(`
                        <div class="form-check mr-3 mb-2">
                            <input class="form-check-input feature-permission" type="checkbox" value="${action}" name="${inputName}" id="${inputId}" data-feature-id="${featureId}" data-feature-key="${featureKey}" data-action="${actionKey}">
                            <label class="form-check-label" for="${inputId}">${action}</label>
                        </div>
                    `);

                    if (isChecked) $check.find('input').prop('checked', true);
                    $featureBody.append($check);
                });

                // Apply initial dependency rules
                Object.entries(featureData.dependencies).forEach(([dependentAction, rule]) => {
                    if (rule.type === 'prerequisite') {
                        const $prereqCheckbox = $featureBody.find(`input.feature-permission[data-action="${dependentAction}"]`);
                        if ($prereqCheckbox.length && !$prereqCheckbox.is(':checked')) {
                            rule.appliesTo.forEach(targetAction => {
                                $featureBody.find(`input.feature-permission[data-action="${targetAction}"]`).prop('checked', false).prop('disabled', true);
                            });
                        }
                    }
                });

                $featuresAccordion.append($featureCard);
            });

            $accordion.append($moduleCard);
        });
    }

    // Form submission handler (no change needed here)
    function collectPermissions() {
        const permissions = {};
        $('input.feature-permission:checked').each(function() {
            const action = $(this).val();
            const featureKey = $(this).data('feature-key');
            if (!featureKey) return;
            if (!permissions[featureKey]) permissions[featureKey] = [];
            permissions[featureKey].push(action);
        });
        return { permissions, touched: Array.from(touchedFeatures) };
    }

    // Handle form submission
    $('#marketplace_roles_permission_form').on('submit', function(e) {
        e.preventDefault();
        const { permissions, touched } = collectPermissions();
        const formData = new FormData(this);

        // Append all permissions, even those that are not selected but were "touched"
        modules.forEach(module => {
            Object.keys(module.features).forEach(featureName => {
                const featureKey = slugify(featureName);
                if (touched.includes(featureKey)) {
                    const selectedActions = permissions[featureKey] || [];
                    const allActions = module.features[featureName].actions.map(a => slugify(a));

                    // Add existing and newly selected permissions to formData under top-level key
                    selectedActions.forEach(action => {
                        formData.append(`${featureKey}[]`, action);
                    });

                    // To ensure backend knows about deselected permissions, we might need a more complex solution, but
                    // for now, we'll just send the "touched" list and the selected permissions.
                }
            });
        });

        touched.forEach(function(featureKey) {
            formData.append('touched_features[]', featureKey);
        });

        $('#submitmarketplacerolespermissionbutton').prop('disabled', true).text('Saving...');

        $.ajax({
            url: '{{ url("admin/k12connections/add-permission") }}',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            error: function(xhr, status, error) {
                alert('Error saving permissions: ' + error);
            },
            complete: function() {
                $('#submitmarketplacerolespermissionbutton').prop('disabled', false).text('Submit');
            }
        });
    });

    // Event handler to manage dependencies dynamically
    $(function(){
        renderPermissionsAccordion('#permissionsAccordion', modules);

        $(document).on('change', 'input.feature-permission', function(){
            const $this = $(this);
            const actionKey = $this.data('action');
            const featureKey = $this.data('feature-key');
            const featureLabel = $this.closest('.card').find('.card-header h6').text();
            const moduleLabel = $this.closest('[data-parent="#permissionsAccordion"]').prev().find('h6').text();
            const moduleKey = slugify(moduleLabel);
            const featureData = modules.find(m => slugify(m.name) === moduleKey).features[featureLabel];
            const $featureBody = $this.closest('.card-body');

            if (featureKey) touchedFeatures.add(featureKey);

            if (featureData && featureData.dependencies) {
                Object.entries(featureData.dependencies).forEach(([dependentAction, rule]) => {
                    const $prereqCheckbox = $featureBody.find(`input.feature-permission[data-action="${dependentAction}"]`);
                    if (rule.type === 'prerequisite' && $prereqCheckbox.length) {
                        const isPrereqChecked = $prereqCheckbox.is(':checked');
                        rule.appliesTo.forEach(targetAction => {
                            const $targetCheckbox = $featureBody.find(`input.feature-permission[data-action="${targetAction}"]`);
                            if ($targetCheckbox.length) {
                                if (isPrereqChecked) {
                                    $targetCheckbox.prop('disabled', false);
                                } else {
                                    $targetCheckbox.prop('checked', false).prop('disabled', true);
                                }
                            }
                        });
                    }
                });
            }
        });
    });
})(jQuery);
</script>
@endsection
