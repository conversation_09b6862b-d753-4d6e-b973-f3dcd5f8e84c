<section>
    @if (isset($progress))
    <div id="form_progress_indicator">
        @php
            $progressValue = (int) round($progress['percentage']); // For step completion
        @endphp
        <label class="form-label fw-semibold">
            <span class="text-primary" id="progress_percentage">{{ (int) $progressValue }}% Complete</span>
        </label>

        <div class="custom-progress-bar mb-3">
            <div class="progress-track" id="progress_track" style="--progress-width: {{ (int) $progressValue }}%;">
                @for ($i = 0; $i <= 5; $i++)
                    <div class="progress-step {{ $i <= $progressValue ? 'completed' : '' }}"></div>
                @endfor
            </div>
        </div>
    </div>
    @endif
    <form id="availabilityScheduleForm" class="needs-validation d-none">
        <x-dynamic-form-group :fields="$config['availability']['fields']" />
    </form>
    
    <form id="addSubjectsForm" class="needs-validation d-none">
        <x-dynamic-form-group :fields="$config['subjects']['fields']" />
    </form>
    
    <form id="addSubstitutesForm" class="needs-validation d-none">
        <x-dynamic-form-group :fields="$config['substitutes']['fields']" />
    </form>
    
    <form id="addPhoneNumberForm" class="needs-validation d-none">
        <x-dynamic-form-group :fields="$config['phoneNumber']['fields']" />
    </form>
    
    <form id="notificationForm" class="needs-validation d-none">
        <x-dynamic-form-group :fields="$config['notifications']['fields']" />
    </form>
</section>
