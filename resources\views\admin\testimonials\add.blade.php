@extends('admin.layouts.master')

@section('title', 'Add Testimonial')

@section('content')
<main class="content">
    <div class="container-fluid p-0">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item">
                    <a href="{{ url('admin-dashboard') }}" class="text-primary">{{ __('messages.dashboard') }}</a>
                </li>
                <li class="breadcrumb-item active" aria-current="page">Add Testimonial</li>
            </ol>
        </nav>
        <form method="post" action="{{ route('testimonial.store') }}" id="addtestimonial" enctype="multipart/form-data">
            @csrf
            <div class="row justify-content-center">
                <div class="col-lg-12 col-md-12">
                    <div class="card">
                        <div class="card-body">
                            <div id="testimonialRows">
                                @forelse($testimonial as $key => $test)
                                <div class="testimonialRow border rounded p-3 mb-3 position-relative">
                                    <input type="hidden" name="id[]" value="{{ $test->id }}">

                                    <div class="row">
                                        <div class="col-md-6 form-group">
                                            <label>Name</label>
                                            <input type="text" class="form-control" name="name[]" value="{{ $test->name }}" required>
                                        </div>
                                        <div class="col-md-6 form-group">
                                            <label>Designation</label>
                                            <input type="text" class="form-control" name="designation[]" value="{{ $test->designation }}" required>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6 form-group">
                                            <label>Image</label>
                                            <input type="file" class="form-control" name="image[]">
                                            <div class="mt-2">
                                                @if($test->image)
                                                    <a href="{{ generateSignedUrl($test->image) }}" target="_blank">View/Download Image</a>
                                                @endif
                                            </div>
                                        </div>
                                        <div class="col-md-5 form-group">
                                            <label>Description</label>
                                            <textarea class="form-control" name="description[]" required>{{ $test->description }}</textarea>
                                        </div>
                                    </div>

                                    <div class="action-btns mt-5 mr-5">
                                        @if($key == 0)
                                        <button type="button" class="btn btn-success addRow">+</button>
                                        @else
                                        <button type="button" class="btn btn-danger removeRowDB" data-id="{{ $test->id }}">-</button>
                                        @endif
                                    </div>
                                </div>
                                @empty
                                <div class="testimonialRow border rounded p-3 mb-3 position-relative">
                                    <div class="row">
                                        <div class="col-md-6 form-group">
                                            <label>Name</label>
                                            <input type="text" class="form-control" name="name[]" placeholder="Enter name" required>
                                        </div>
                                        <div class="col-md-6 form-group">
                                            <label>Designation</label>
                                            <input type="text" class="form-control" name="designation[]" placeholder="Enter designation" required>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6 form-group">
                                            <label>Image</label>
                                            <input type="file" class="form-control" name="image[]">
                                        </div>
                                        <div class="col-md-5 form-group">
                                            <label>Description</label>
                                            <textarea class="form-control" name="description[]" placeholder="Enter description" required></textarea>
                                        </div>
                                    </div>
                                    <div class="action-btns mt-5 mr-4">
                                        <button type="button" class="btn btn-success addRow">+</button>
                                    </div>
                                </div>
                                @endforelse
                            </div>

                            <div class="col-md-12 d-flex justify-content-md-end justify-content-between mt-3">
                                <a href="{{ redirect()->getUrlGenerator()->previous() }}" class="btn btn-secondary mr-2">{{ __('messages.cancel') }}</a>
                                <button type="submit" class="btn btn-primary">{{ __('messages.add') }}</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</main>

<style>
    .testimonialRow {
        position: relative;
        padding-right: 60px;
    }
    .action-btns {
        position: absolute;
        top: 50%;
        right: 15px;
        transform: translateY(-50%);
    }
</style>
@endsection

@section('scripts')
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
    $(document).ready(function() {
        $(document).on("click", ".addRow", function() {
            let newRow = $(".testimonialRow:first").clone();
            newRow.find("input[type=text], textarea").val("");
            newRow.find("input[type=file]").val("");
            newRow.find("input[type=hidden]").remove();
            newRow.find("img").remove();
            newRow.find(".addRow")
                .removeClass("btn-success addRow")
                .addClass("btn-danger removeRow")
                .text("-");
            $("#testimonialRows").append(newRow);
        });

        $(document).on("click", ".removeRow", function() {
            $(this).closest(".testimonialRow").remove();
        });

        $(document).on("click", ".removeRowDB", function() {
            if (!confirm("Are you sure you want to delete this testimonial?")) return;
            let row = $(this).closest(".testimonialRow");
            let id = $(this).data("id");

            $.ajax({
                url: "{{ url('testimonial-delete') }}",
                type: "POST",
                data: {
                    id: id,
                    _token: "{{ csrf_token() }}"
                },
                success: function(res) {
                    if (res.success) {
                        row.remove();
                    }
                }
            });
        });
    });
</script>
@endsection