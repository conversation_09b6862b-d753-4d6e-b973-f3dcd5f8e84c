<?php

use Carbon\Carbon;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class InsertManageMarketplaceIntoTblModules extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::table('tbl_modules')->insert([
            'title' => 'Manage Marketplace',
            'type' => 'managemarketplace',
            'mod_action' => json_encode([
                "applicants",
                "programs",
                "requirements",
                "platform schools",
                "delete applicants",
                "delete programs",
                "delete requirements",
                "delete platform schools"
            ]),
            'status' => 1,
            'sort_order' => 14,
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
        ]);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        DB::table('tbl_modules')->where('type', 'managemarketplace')->delete();
    }
}
