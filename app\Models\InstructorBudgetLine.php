<?php

namespace App\Models;

use App\Models\v1\Subject;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class InstructorBudgetLine extends Model
{
    use SoftDeletes;

    protected $table = 'instructor_budget_lines';

    protected $fillable = [
        'approved_id',
        'subject_code',
        'subject_title',
        'base_pay',
        'experience_pay',
        'education_pay',
        'non_teaching',
        'special_education',
        'total'
    ];

    public function approved()
    {
        return $this->belongsTo(InstructorBudgetApprovedModel::class, 'approved_id');
    }

    public function subject()
    {
        return $this->belongsTo(Subject::class, 'subject_code', 'subject_code');
    }
}
