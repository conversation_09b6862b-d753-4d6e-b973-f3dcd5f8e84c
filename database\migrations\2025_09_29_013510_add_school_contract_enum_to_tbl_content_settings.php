<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

class AddSchoolContractEnumToTblContentSettings extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Update the enum to include 'School Contract'
        DB::statement("ALTER TABLE tbl_content_settings MODIFY COLUMN type ENUM('aboutus', 'terms-conditions', 'privacy-policy', 'activity-terms-condition', 'Online contract', 'In person contract', 'hybrid contract', 'Online Logistics/Prerequisites', 'In person Logistics/Prerequisites', 'New Instructor Contract','How Whizara Works', 'Online New Instructor Contract', 'In Person New Instructor Contract', 'School Contract')");

        // Insert the School Contract record
        DB::table('tbl_content_settings')->insert([
            'type' => 'School Contract',
            'description' => null,
            'document' => null,
            'locale' => 'en',
            'created_at' => now(),
            'updated_at' => now(),
        ]);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // Remove the School Contract record
        DB::table('tbl_content_settings')->where('type', 'School Contract')->delete();

        // Revert the enum to previous state
        DB::statement("ALTER TABLE tbl_content_settings MODIFY COLUMN type ENUM('aboutus', 'terms-conditions', 'privacy-policy', 'activity-terms-condition', 'Online contract', 'In person contract', 'hybrid contract', 'Online Logistics/Prerequisites', 'In person Logistics/Prerequisites', 'New Instructor Contract','How Whizara Works', 'Online New Instructor Contract', 'In Person New Instructor Contract')");
    }
}
