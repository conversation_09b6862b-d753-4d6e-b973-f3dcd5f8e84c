<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class ServeSchoolsApp
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        // If request starts with /schools but isn't hitting API/static file
        if (str_starts_with($request->getRequestUri(), '/schools')) {

            // Ignore real files (css, js, assets) under /schools
            // $path = public_path($request->path());
            // if (file_exists($path)) {
            //     return $next($request);
            // }

            // Return Angular index.html
            return response()->file(public_path('schools/index.html'));
        }

        return $next($request);
    }
}
