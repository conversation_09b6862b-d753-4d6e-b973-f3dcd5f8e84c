<div class="table-responsive">
    <table class="table table-striped table-bordered mb-0" id="{{ $tableId ?? '' }}">
        <thead class="thead-dark">
            <tr>
                <th>#</th>
                <th>Educator Name</th>
                <th>State</th>
                <th>Subject Area</th>
                <th>Subject Name</th>
                <th>Grade Levels</th>
                <th>Certified States</th>
                <th>Certification</th>
                @if(!isset($showShortlist) || $showShortlist)
                    <th>Shortlist</th>
                @endif
                <th>Shortlisted By</th>
            </tr>
        </thead>
        <tbody>
            @forelse(($rows ?? []) as $index => $r)
                <tr>
                    <td>{{ $index + 1 }}</td>
                    <td>
                        {{ $r['name'] ?? '-' }}
                        <span class="shortlist-meta d-none" data-user-id="{{ $r['user_id'] ?? 0 }}" data-requirement-id="{{ $r['requirement_id'] ?? 0 }}"></span>
                    </td>
                    <td>{{ $r['state'] ?? '-' }}</td>
                    <td>{{ $r['subject_area'] ?? '-' }}</td>
                    <td>{{ $r['subject_name'] ?? '-' }}</td>
                    <td>{{ $r['grade_levels'] ?? '-' }}</td>
                    <td>{{ $r['certified_states'] ?? '-' }}</td>
                    <td>
                        @if(!empty($r['has_certification']))
                            <span class="text-success"><i class="fa fa-check"></i></span>
                        @else
                            <span class="text-muted">-</span>
                        @endif
                    </td>
                    @if(!isset($showShortlist) || $showShortlist)
                        <td>
                            @php
                                $liked = isset($r['shortlist_status']) && (int) $r['shortlist_status'] === 1;
                                $disliked = isset($r['shortlist_status']) && (int) $r['shortlist_status'] === 0;
                            @endphp
                            <a href="javascript:void(0)" class="mx-1 shortlist-like" data-user-id="{{ $r['user_id'] ?? 0 }}" data-requirement-id="{{ $r['requirement_id'] ?? 0 }}" data-status="1" title="Like">
                                <i class="fa fa-thumbs-up {{ $liked ? 'text-success' : 'text-muted' }}"></i>
                            </a>
                            <a href="javascript:void(0)" class="mx-1 shortlist-dislike" data-user-id="{{ $r['user_id'] ?? 0 }}" data-requirement-id="{{ $r['requirement_id'] ?? 0 }}" data-status="0" title="Dislike">
                                <i class="fa fa-thumbs-down {{ $disliked ? 'text-danger' : 'text-muted' }}"></i>
                            </a>
                        </td>
                    @endif
                    <td>{{ $r['updated_by'] ?? '-' }}</td>
                </tr>
            @empty
                <tr>
                    <td colspan="10" class="text-center text-muted">No data</td>
                </tr>
            @endforelse
        </tbody>
    </table>
</div>

