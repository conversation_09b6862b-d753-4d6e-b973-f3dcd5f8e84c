<?php

namespace App\View\Components;

use App\Models\PlatformSchoolRequirements;
use App\Models\SchoolReviewApplicants;
use App\OnboardingInstructor;
use Illuminate\View\Component;
use App\Classes;
use App\Models\PlatformSchoolInvites;

class PublicProfile extends Component
{
    public $instructor;
    public $proposal;
    public $totalProposedAmount;
    public $licenseFee;
    public $instructorName;
    public $type;
    public $data;
    public $educationList;
    public $certified;
    public $certificateList;
    public $expDateList;
    public $teachingStateList;
    public $isLiked;
    public $isDisliked;
    public $classNames;
    public $keywords;
    public $languages;
    public $programs;
    public $subjectsList;
    public $formats;
    public $compensation;
    public $deliveryMode;
    public $educations;
    public $college;
    public $graduationYear;
    public $GPA;
    public $major;
    public $minor;
    public $transcripts;
    public $transcriptNames;
    public $referencesList;
    public $invite;
    public $inviteStatus;
    public $inviteStatusTxt;
    public $otherEducation;
    public $requirement;

    /**
     * Create a new component instance.
     */
    public function __construct(OnboardingInstructor $instructor, string $type, PlatformSchoolRequirements $data = null, SchoolReviewApplicants $proposal = null, PlatformSchoolInvites $invite = null,$requirement)
    {

       
        $this->instructor = $instructor;
        $this->type = $type;
        $this->data = $data;
        $this->proposal = $proposal;
        $this->invite = $invite;
        // Process the data
        $this->instructorName = "{$instructor->first_name} {$instructor->last_name}";
        $this->processProposalRate();
        $this->processEducation();
        $this->processTeachingPreferences();
        $this->processShortlistStatus();
        $this->processSubjects();
        $this->processOtherDetails();
        $this->processreferences();
        $this->processInvite();
        $this->requirement=$requirement;
    }

    private function processProposalRate()
    {
        if ($this->proposal && $this->data) {
            $this->totalProposedAmount = $this->proposal->proposed_rate * floatval($this->data->totalHours);
            $this->licenseFee = $this->totalProposedAmount < 33.75 ? 1000 :
                ($this->totalProposedAmount < 44.99
                    ? 1125
                    : ($this->totalProposedAmount < 67.49
                        ? 1200
                        : ($this->totalProposedAmount < 134.99
                            ? 1350
                            : 1800)));
        }
    }

    private function processEducation()
    {
        $this->educationList = [];
        $this->certified = '';
        $this->certificateList = [];
        $this->expDateList = [];
        $this->teachingStateList = [];

        if (!empty($this->instructor->step2)) {
            if (!empty($this->instructor->step2->profile_type) && $this->instructor->step2->profile_type == 'Certified Teacher') {
                $this->certified = 'Certified';
            }

            if (!empty($this->instructor->step2->education)) {
                foreach ($this->instructor->step2->education as $certificate) {
                    $this->educationList[] = $certificate->education;
                    $states = json_decode($certificate->states);
                    $this->teachingStateList[] = implode(',', $states);
                    $this->expDateList[] = $certificate->certification_year;
                    $this->certificateList[] = $certificate->certificate;
                }
            }
        }
    }

    private function processTeachingPreferences()
    {
        $this->classNames = [];
        if (!empty($this->instructor->step3) && !empty($this->instructor->step3->i_prefer_to_teach)) {
            $teachingPreference = explode(',', $this->instructor->step3->i_prefer_to_teach);
            $this->classNames = collect($teachingPreference)->map(function ($gradeId) {
                $grade = Classes::find($gradeId);
                return $grade ? $grade->class_name : null;
            })->filter()->toArray();
        }

        $this->keywords = !empty($this->instructor->step5->profile_tags) ? explode(',', $this->instructor->step5->profile_tags) : [];
        $this->languages = !empty($this->instructor->step3->language_teach_that_i_teach) ? explode(',', $this->instructor->step3->language_teach_that_i_teach) : [];
        $this->programs = !empty($this->instructor->step3->program_type) ? explode(',', $this->instructor->step3->program_type) : [];
    }

    private function processShortlistStatus()
    {
        $this->isLiked = false;
        $this->isDisliked = false;

        if (!empty($this->instructor->shortList)) {
            foreach ($this->instructor->shortList as $shortList) {
                if ($shortList->status === 1 && $shortList->requirement_id == $this->data->id) {
                    $this->isLiked = true;
                }
                if ($shortList->status === 0 && $shortList->requirement_id == $this->data->id) {
                    $this->isDisliked = true;
                }
            }
        }
    }

    private function processSubjects()
    {
        $this->subjectsList = [];
        if (!empty($this->instructor->step3->subjects)) {
            foreach ($this->instructor->step3->subjects as $subject) {
                $this->subjectsList[] = [
                    'name' => v1SubjectAreaName($subject->subject),
                    'sub_subject' => v1SubjectName($subject->sub_subject),
                    'proficiency' => $subject->proficiency . '/5'
                ];
            }
        }
    }

    private function processOtherDetails()
    {
        $this->formats = !empty($this->instructor->step3->format) ? explode(',', $this->instructor->step3->format) : [];
        $this->compensation = !empty($this->instructor->step3->compensation) ? explode(',', $this->instructor->step3->compensation) : [];
        $this->deliveryMode = !empty($this->data->delivery_mode) ? explode(',', $this->data->delivery_mode) : [];

        if (!empty($this->instructor->step2)) {
            $this->educations = explode(',', $this->instructor->step2->highest_level_of_education);
            $this->college = explode(',', $this->instructor->step2->school_college_name);
            $this->graduationYear = explode(',', $this->instructor->step2->month_and_year_graduation);
            $this->GPA = explode(',', $this->instructor->step2->GPA);
            $this->major = explode(',', $this->instructor->step2->major);
            $this->minor = explode(',', $this->instructor->step2->minor);
            $this->transcripts = explode(',', $this->instructor->step2->transcript);
            $this->transcriptNames = explode(',', $this->instructor->step2->transcript_name);
            $this->otherEducation = !empty($this->instructor->step2->other_education) ? explode(',', $this->instructor->step2->other_education) : '';
        }
    }

    private function processReferences()
    {
        $this->referencesList = [];

        if (!empty($this->instructor->step2) && !empty($this->instructor->step2->references)) {
            $this->referencesList = $this->instructor->step2->references;
        }
    }

    private function processInvite()
    {
        if (!empty($this->invite) && !empty($this->invite->status)) {
            $this->inviteStatus = $this->invite->status;
            if ($this->invite->status == 'accepted') {
                $this->inviteStatusTxt = 'Accepted';
            } else if ($this->invite->status == 'declined') {
                $this->inviteStatusTxt = 'Declined';
            } else if ($this->invite->status == 'pending') {
                $this->inviteStatusTxt = 'Invited';
            } else {
                $this->inviteStatusTxt = 'Invite';
            }
            
            // $this->inviteStatus = $this->invite->status;
        } else {
            $this->inviteStatusTxt = 'Invite';
        }
    }

    public function render()
    {
        return view('components.public_profile', [
            'instructor' => $this->instructor,
            'instructorName' => $this->instructorName,
            'data' => $this->data,
            'totalProposedAmount' => $this->totalProposedAmount,
            'licenseFee' => $this->licenseFee,
            'proposal' => $this->proposal,
            'educationList' => $this->educationList,
            'certified' => $this->certified,
            'certificateList' => $this->certificateList,
            'expDateList' => $this->expDateList,
            'teachingStateList' => $this->teachingStateList,
            'isLiked' => $this->isLiked,
            'isDisliked' => $this->isDisliked,
            'classNames' => $this->classNames,
            'keywords' => $this->keywords,
            'languages' => $this->languages,
            'programs' => $this->programs,
            'subjectsList' => $this->subjectsList,
            'formats' => $this->formats,
            'compensation' => $this->compensation,
            'deliveryMode' => $this->deliveryMode,
            'educations' => $this->educations,
            'college' => $this->college,
            'graduationYear' => $this->graduationYear,
            'GPA' => $this->GPA,
            'major' => $this->major,
            'minor' => $this->minor,
            'transcripts' => $this->transcripts,
            'transcriptNames' => $this->transcriptNames,
            'references' => $this->referencesList,
            'invite' => $this->invite,
            'inviteStatus' => $this->inviteStatus,
            'inviteStatusTxt' => $this->inviteStatusTxt,
            'otherEducation' => $this->otherEducation,
            'requirement'=>$this->requirement,
        ]);
    }
}
