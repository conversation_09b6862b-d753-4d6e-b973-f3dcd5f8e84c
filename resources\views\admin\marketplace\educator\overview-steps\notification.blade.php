@if (request()->segment(5) == 'notification')
    <div class="card-body p-2 px-3">
        <ul class="list-group list-group-flush">
            @if (!empty($educator->notificationPreference) && $educator->notificationPreference->isNotEmpty())
                @foreach ($educator->notificationPreference as $notification)
                    <li class="list-group-item d-flex flex-wrap">
                        <span class="col-lg-3 col-md-3 col-4 pl-0">Notification Type:</span>
                        <h6 class="col-lg-9 col-md-9 col-8 pr-0 mb-0">
                            {{ $notification->notification_type }}
                        </h6>

                        <span class="col-lg-3 col-md-3 col-4 pl-0">In App Notification:</span>
                        <div class="col-lg-9 col-md-9 col-8 pr-0 mb-0">
                            <input type="checkbox" name="in_app_notification[]" disabled value="1" {{ $notification->in_app_notifications ? 'checked' : '' }}>
                        </div>

                        <span class="col-lg-3 col-md-3 col-4 pl-0">Email Notification:</span>
                        <div class="col-lg-9 col-md-9 col-8 pr-0 mb-0">
                            <input type="checkbox" name="email_notification[]" disabled value="1" {{ $notification->email_notifications ? 'checked' : '' }}>
                        </div>

                        <span class="col-lg-3 col-md-3 col-4 pl-0">Real-Time Email Notification:</span>
                        <div class="col-lg-9 col-md-9 col-8 pr-0 mb-0">
                            <input type="checkbox" name="real_time_notification[]" disabled value="1" {{ $notification->realtime_email_notifications ? 'checked' : '' }}>
                        </div>

                        <span class="col-lg-3 col-md-3 col-4 pl-0">Daily Summary Emails:</span>
                        <div class="col-lg-9 col-md-9 col-8 pr-0 mb-0">
                            <input type="checkbox" name="daily_summary_email[]" disabled value="1" {{ $notification->daily_summary_emails ? 'checked' : '' }}>
                        </div>
                    </li>
                @endforeach
            @else
                <li class="list-group-item d-flex">No data found</li>
            @endif
        </ul>
    </div>
@endif