<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSchoolRequirementContractsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('school_requirement_contracts', function (Blueprint $table) {
            // Primary key
            $table->id();

            // Requirement
            $table->unsignedBigInteger('requirement_id')->nullable();
            $table->foreign('requirement_id')->references('id')->on('platform_school_requirements')->onDelete('cascade');

            // Legal and contact information
            $table->string('legal_first_name')->nullable();
            $table->string('legal_last_name')->nullable();
            $table->string('phone')->nullable();
            $table->string('email')->nullable();
            $table->string('job_title')->nullable();
            $table->text('address')->nullable();

            // Client organization name
            $table->string('client_name')->nullable();

            // Purchase order
            $table->boolean('has_purchase_order')->default(false);
            $table->string('purchase_order_ref')->nullable();

            // Status field
            $table->enum('status', [ 'draft', 'pending_approval', 'in_review', 'approved', 'on_hold', 'cancelled', 'completed' ])->default('pending_approval');

            // Polymorphic user tracking
            $table->morphs('created_by'); // created_by_id, created_by_type
            $table->morphs('updated_by'); // updated_by_id, updated_by_type

            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('school_requirement_contracts');
    }
}
