@if (request()->segment(5) == 'locations')
    <div class="card-body p-2 px-3">
        <ul class="list-group list-group-flush">
            @if ($educator && $educator->locations->isNotEmpty())
                @foreach ($educator->locations as $location)
                    <li class="list-group-item d-flex">
                        <span class="col-lg-3 col-md-3 col-4 pl-0">Location:</span>
                        <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0">{{ $location->location ?? '-' }}</h6>

                        <span class="col-lg-3 col-md-3 col-4 pl-0">Address:</span>
                        <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0">{{ $location->address ?? '-' }}</h6>
                    </li>

                    <li class="list-group-item d-flex">
                        <span class="col-lg-3 col-md-3 col-4 pl-0">Latitude:</span>
                        <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0">{{ $location->lat ?? '-' }}</h6>

                        <span class="col-lg-3 col-md-3 col-4 pl-0">Longitude:</span>
                        <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0">{{ $location->lng ?? '-' }}</h6>
                    </li>

                    <li class="list-group-item d-flex">
                        <span class="col-lg-3 col-md-3 col-4 pl-0">Radius:</span>
                        <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0">{{ $location->radius ?? '-' }} Miles</h6>
                    </li>
                @endforeach
            @else
                <li class="list-group-item">No data found</li>
            @endif
        </ul>
    </div>
@endif
