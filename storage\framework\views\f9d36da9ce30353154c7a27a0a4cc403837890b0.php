

<?php $__env->startSection('title'); ?> <?php echo e(__('messages.dashboard')); ?> | Whizara <?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>

<!-- MAIN SECTION START -->
<main class="content">
    <div class="container-fluid p-0">
        <!-- BREADCRUMB START -->
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="<?php echo e(url('admin-dashboard')); ?>" class="text-primary"><?php echo e(__('messages.dashboard')); ?></a></li>
                <li class="breadcrumb-item active" aria-current="page">Content Management</li>
                <!-- <li class="breadcrumb-item active" aria-current="page"><?php echo e(__('messages.setting_terms_list')); ?></li> -->
                <li class="breadcrumb-item"><a href="<?php echo e(url('list-school-management-setting')); ?>">Dropdown Content Management</a></li>
            </ol>
        </nav>

        <div class="table-responsive">
            <table class="table table-striped admin-dataTable" style="width:100%">
                <thead class="thead-dark">
                    <tr>
                        <!-- <th>#</th> -->
                        <th style="text-align: left">Title</th>
                        
                        <th>Document</th>
                        <th><?php echo e(__('messages.status')); ?></th>
                        <th><?php echo e(__('messages.action')); ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php if(!empty($setting) && $setting->count()): ?>
                    <?php $__currentLoopData = $setting; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $data): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <tr>
                        <!-- <th scope="row"><?php echo e(++$key); ?></th> -->
                        <td><?php echo e(ucfirst($data->type)); ?></td>
                        
                        <td>
                            <?php if($data->document): ?>
                            
                                <a target="_blank" href="<?php echo e(generateSignedUrl($data->document)); ?>">File </a>
                           
                            <?php endif; ?>
                            
                            </td>
                        <td>
                            <?php if($data->status == 0): ?>
                            <button onclick="status_update('<?php echo e($data->id); ?>',1)" data-data="1" class="btn btn-warning btn-rounded changestatuscls-<?php echo e($data->id); ?>">
                                <?php echo e(__('messages.deactive')); ?></button>
                            <?php endif; ?>
                            <?php if($data->status == 1): ?>
                            <button onclick="status_update('<?php echo e($data->id); ?>',0)" data-data="0" class="btn btn-success btn-rounded changestatuscls-<?php echo e($data->id); ?>">
                                <?php echo e(__('messages.active')); ?></button>
                            <?php endif; ?>
                        </td>
                        <td>
                            <div class="w-100 d-flex justify-content-center align-items-center">
                                <!-- <a class="delete_terms"  href="javascript:void(0)" data-id="<?php echo e(encrypt_str($data->id)); ?>">
                        <button class="btn btn-rounded btn-block btn-xs btn-outline-danger"><i class="fa fa-trash" aria-hidden="true"></i></button>
                   </a> -->
                                <a href="<?php echo e(url('viewtermssetting/'.encrypt_str($data->id))); ?>">
                                    <button class="btn btn-rounded btn-block btn-xs btn-outline-secondary"><i class="fa fa-pencil-square-o" aria-hidden="true"></i></button>
                                </a>
                            </div>
                        </td>

                    </tr>


                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    <?php else: ?>
                    <tr>
                        <td colspan="10">No data found.</td>
                    </tr>
                    <?php endif; ?>
                </tbody>
                <!-- <tfoot>

                    <tr>
                        <th>#</th>
                        <th><?php echo e(__('messages.type')); ?></th>
                        
                        <th><?php echo e(__('messages.status')); ?></th>
                        <th><?php echo e(__('messages.action')); ?></th>

                    </tr>
                </tfoot> -->
            </table>
        </div>
        <!-- END -->

        <!-- EDIT PROFILE SECTION END -->
    </div>
</main>
<!-- MAIN SECTION END -->



<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
    function status_update(id) {
        var url = base_url + 'terms-setting-changes';

        var status = $('.changestatuscls-' + id).data('data');

        if (status == 0)
            confirm_message = 'Are you sure you want to Deactivate ?';
        else
            confirm_message = 'Are you sure you want to activate ?';
        update_status(id, url, confirm_message);
    }
</script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('admin.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\whizara\whizara\resources\views/admin/settingterms/listsettingterms.blade.php ENDPATH**/ ?>