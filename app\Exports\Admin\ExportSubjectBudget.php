<?php

namespace App\Exports\admin;

use App\Models\v1\BudgetState;
use App\Models\v1\Subject;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class ExportSubjectBudget implements FromCollection, WithHeadings, WithMapping
{
    public $stateId;

    public function __construct($stateId = null)
    {
        $this->stateId = $stateId;
    }

    /**
    * @return \Illuminate\Support\Collection
    */
    public function collection()
    {
        $stateId = $this->stateId ?? BudgetState::first()->id;
        return Subject::with(['subjectArea','budgets' => function ($q) use ($stateId) {
                    $q->whereNotNull('state_id')
                      ->where('state_id', $stateId);
                }])->get();
    }

    public function map($subject): array
    {
        return [
            $subject->subjectArea->code,
            $subject->subjectArea->subject_area,
            $subject->subject_code,
            $subject->title,
            $subject->description,
            optional($subject->subjectBudget)->base_pay_0_3,
            optional($subject->subjectBudget)->pay_3_6,
            optional($subject->subjectBudget)->pay_6_10,
            optional($subject->subjectBudget)->pay_10_plus,
            optional($subject->subjectBudget)->masters_inc,
            optional($subject->subjectBudget)->doctorate_inc,
            optional($subject->subjectBudget)->non_tech_time,
            optional($subject->subjectBudget)->curriculum_inc
        ];
    }

    public function headings(): array
    {
        return [
            'Code', 'Subject Area', 'SCED Course Code', 'Course Title', 'Course Description',
            'Base Pay (0-3 yrs)', 'Incremental Pay (3-6 yrs)', 'Incremental Pay (6-10 yrs)',
            'Incremental Pay (10+ yrs)', "Master's Degree Increment", 'Doctorate Increment',
            'Non-Teaching Time Compensation', 'Curriculum increment'
        ];
    }
}
