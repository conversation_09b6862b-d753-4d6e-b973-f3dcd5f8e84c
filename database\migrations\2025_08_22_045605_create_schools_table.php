<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSchoolsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('schools', function (Blueprint $table) {
            $table->id();
            $table->string('school_name', 255);
            $table->string('user_id')->nullable();
            $table->string('nces_id')->unique()->nullable();
            $table->enum('organization_type', ['public', 'charter', 'private'])->default('public');
            $table->string('email', 191)->unique();
            $table->string('phone_number', 20)->unique()->nullable();
            $table->string('website_url', 255)->nullable();
            $table->string('image', 255)->nullable();

            // Profile Details
            $table->text('about')->nullable();
            $table->decimal('school_rating', 3, 2)->default(0.00);

            // Address
            $table->string('address', 255)->nullable();
            $table->string('city', 100)->nullable();
            $table->string('state', 100)->nullable();
            $table->integer('district_id')->nullable();  // must match int(11)
            $table->integer('cbo_id')->nullable();       // must match int(11)
            $table->string('country', 100)->nullable();
            $table->string('zipcode', 20)->nullable();
            $table->decimal('latitude', 10, 7)->nullable();
            $table->decimal('longitude', 10, 7)->nullable();

            // Preferences
            $table->string('timezone', 100)->default('UTC');
            $table->string('locale', 10)->default('en');
            $table->boolean('email_notification')->default(false);
            $table->boolean('app_notification')->default(true);

            // Classification
            $table->enum('customer_type', ['service', 'platform', 'other'])->default('other');
            $table->string('grade_levels_id')->nullable();
            $table->string('enrollment_count')->nullable();

            // Status & Trial
            $table->enum('status', ['active', 'inactive', 'suspended', 'pending'])->default('pending');
            $table->dateTime('trial_ends_at')->nullable();

            // Soft Deletes & Tracking
            $table->softDeletes();
            $table->timestamps();

            // Indexes
            $table->index('email', 'idx_email');
            $table->index('phone_number', 'idx_phone');
            $table->index('status', 'idx_status');
            $table->index(['city', 'state', 'country'], 'idx_location');
            $table->index('district_id', 'idx_district');
            $table->index('cbo_id', 'idx_cbo');

            // Foreign Keys
            $table->foreign('district_id')
                ->references('id')->on('tbl_districts')
                ->onDelete('set null');

            $table->foreign('cbo_id')
                ->references('id')->on('tbl_cbo')
                ->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('schools');
    }
}
