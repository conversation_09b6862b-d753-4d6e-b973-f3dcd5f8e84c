<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddTempPasswordChangedColumnInNewOnboardingInstructorTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('new_onboarding_instructor', function (Blueprint $table) {
            $table->tinyInteger('temp_password_changed')->default(1)->after('password');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('new_onboarding_instructor', function (Blueprint $table) {
            $table->dropColumn(['temp_password_changed']);
        });
    }
}
