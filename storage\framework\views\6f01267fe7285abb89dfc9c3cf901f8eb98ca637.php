    <nav id="sidebar" class="sidebar">
        <div class="sidebar-content js-simplebar">
            <a class="sidebar-brand" style="background: #FF7F00;" href="<?php echo e(url('/admin-dashboard')); ?>">
                <svg width="292" height="81" viewBox="0 0 292 81" style="height: 61px;width: auto;" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                    <rect width="292" height="81" fill="url(#pattern0)" />
                    <defs>
                        <pattern id="pattern0" patternContentUnits="objectBoundingBox" width="1" height="1">
                            <use xlink:href="#image0_436_7853" transform="scale(0.00342466 0.0123457)" />
                        </pattern>
                        <image id="image0_436_7853" width="292" height="81" xlink:href="data:image/png;base64,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" />
                    </defs>
                </svg>
            </a>

            <ul class="sidebar-nav">
                <?php $res = get_permission(session('Adminnewlogin')['type']); ?>
                <?php if(isset($res['dashboard'])): ?>
                <?php if(array_key_exists('dashboard',$res)): ?>
                <?php if(in_array('view',json_decode($res['dashboard'] ,true))): ?>
                <li class="sidebar-item <?php if (request()->segment(1) == 'admin-dashboard') {
                                            echo 'active';
                                        } ?>">
                    <a class="sidebar-link" href="<?php echo e(url('admin-dashboard')); ?>">
                        <i class="align-middle" data-feather="monitor"></i>
                        <span class="align-middle"><?php echo e(__('messages.dashboard')); ?></span>
                    </a>
                </li>
                <?php endif; ?>
                <?php endif; ?>
                <?php endif; ?>


                <?php if(isset($res['role'])): ?>
                <?php if(array_key_exists('role',$res)): ?>
                <?php if(in_array('view',json_decode($res['role'] ,true))): ?>
                <li class="sidebar-item <?php if (request()->segment(1) == 'manage-role'  || request()->segment(1) == 'add-permission'  || request()->segment(1) == 'add-role' || request()->segment(1) == 'edit-role') {
                                            echo 'active';
                                        } ?>">
                    <a class="sidebar-link" href="<?php echo e(url('manage-role')); ?>">
                        <i class="align-middle" data-feather="anchor"></i>
                        <span class="align-middle">Manage Role</span>
                    </a>
                </li>
                <?php endif; ?>
                <?php endif; ?>
                <?php endif; ?>
                <?php if(isset($res['staff'])): ?>
                <?php if(array_key_exists('staff',$res)): ?>
                <?php if(in_array('view',json_decode($res['staff'] ,true))): ?>
                <li class="sidebar-item <?php if (request()->segment(1) == 'add-admin-management' || request()->segment(1) == 'add-admin' || request()->segment(1) == 'edit-admin' || request()->segment(1) == 'viewdetails' || request()->segment(1) == 'change-staff-password') {
                                            echo 'active';
                                        } ?>">
                    <a class="sidebar-link" href="<?php echo e(url('add-admin-management')); ?>">
                        <i class="align-middle" data-feather="users"></i>
                        <span class="align-middle">Manage Staff</span>
                    </a>
                </li>
                <?php endif; ?>
                <?php endif; ?>
                <?php endif; ?>




                <?php if(isset($res['manageschool'])): ?>
                <?php if(in_array('view',json_decode($res['manageschool'] ,true))): ?>
                <?php if(in_array('view',json_decode($res['manageschool'] ,true))): ?>
                <li class="sidebar-item <?php if (request()->segment(1) == 'school-list' or request()->segment(1) == 'add-school' or request()->segment(1) == 'viewschool' or request()->segment(1) == 'viewschooldetails' or request()->segment(1) == 'change-school-password' or request()->segment(1) === 'viewschoolprogram') {
                                            echo 'active';
                                        } ?>">
                    <a class="sidebar-link" href="<?php echo e(url('school-list')); ?>">
                        <i class="align-middle" data-feather="map-pin"></i>
                        <span class="align-middle">Manage School </span>
                    </a>
                </li>
                <?php endif; ?>
                <?php endif; ?>
                <?php endif; ?>



                <?php if(isset($res['manageinstructor'])): ?>
                <?php if(in_array('view',json_decode($res['manageinstructor'] ,true))): ?>
                <?php if(in_array('view',json_decode($res['manageinstructor'] ,true))): ?>
                <li class="sidebar-item <?php if (request()->segment(1) == 'instructor-list' or request()->segment(1) == 'edit-instructor' or request()->segment(1) == 'viewinstructordetails' or request()->segment(1) == 'view-mail') {
                                            echo 'active';
                                        } ?>">
                    <a class="sidebar-link" href="<?php echo e(url('instructor-list')); ?>">
                        <i class="align-middle" data-feather="users"></i>
                        <span class="align-middle">Manage Instructor </span>
                    </a>
                </li>
                <?php endif; ?>
                <?php endif; ?>
                <?php endif; ?>

                <?php if(isset($res['managemarketplace'])): ?>
                    <?php
                        $permissions = json_decode($res['managemarketplace'], true);
                        $marketItems = ['applicants', 'programs', 'requirements', 'platform schools'];
                        $hasAnyMarketplacePermission = count(array_intersect($permissions, $marketItems)) > 0;

                        // New Permission Check for Applicant
                        $marketplacePermission = [];
                        if (isset($res['manage_applicants'])) {
                            $marketplacePermission = json_decode($res['manage_applicants'], true);
                        }
                    ?>

                    <?php if($hasAnyMarketplacePermission): ?>
                        <li class="sidebar-item <?php if(in_array(request()->segment(3), ['manage-instructor', 'application', 'add-applicant', 'programs', 'add-program', 'view-program', 'requirements', 'view-requirements', 'edit-requirements', 'add-requirements', 'manage-platform-schools', 'add-platform-schools', 'view-platform-schools', 'edit-platform-schools', 'manage-educator', 'manage-subject-approval', 'applied-requests', 'program-invites', 'manage-school-roles', 'manage-marketplace-roles', 'add-marketplace-role', 'edit-marketplace-role', 'add-permission', 'manage-resources', 'add-resource', 'edit-resource', 'manage-email-schedules']) || in_array(request()->segment(2), ['email-template'])): ?> active <?php endif; ?>" id="marketplace">
                            <a href="#requestInst" data-toggle="collapse" class="sidebar-link collapsed">
                                <i class="align-middle" data-feather="sliders"></i>
                                <span class="align-middle">Manage Marketplace</span>
                            </a>

                            <ul id="requestInst" class="sidebar-dropdown list-unstyled collapse <?php if(in_array(request()->segment(3), ['manage-instructor', 'application', 'add-applicant', 'programs', 'add-program', 'view-program', 'requirements', 'view-requirements', 'add-requirements', 'edit-requirements', 'manage-platform-schools', 'add-platform-schools', 'view-platform-schools', 'edit-platform-schools', 'manage-educator', 'manage-subject-approval', 'applied-requests', 'program-invites', 'manage-school-roles', 'manage-marketplace-roles', 'add-marketplace-role', 'edit-marketplace-role', 'add-permission', 'manage-resources', 'add-resource', 'edit-resource', 'manage-email-schedules']) || in_array(request()->segment(2), ['email-template'])): ?> show <?php endif; ?>" data-parent="#sidebar">

                                <?php if(in_array('View List', $marketplacePermission) || in_array('view list', array_map('strtolower', $marketplacePermission))): ?>
                                    <li class="sidebar-item <?php if(in_array(request()->segment(3), ['manage-instructor', 'application', 'add-applicant'])): ?> active <?php endif; ?>">
                                        <a class="sidebar-link" href="<?php echo e(url('admin/k12connections/manage-instructor/ALL')); ?>">
                                            <span class="align-middle">Manage Applicants</span>
                                        </a>
                                    </li>
                                <?php endif; ?>

                                
                                <li class="sidebar-item">
                                    <a href="#marketplaceRequests" data-toggle="collapse" class="sidebar-link <?php if(in_array(request()->segment(3), ['manage-marketplace-roles', 'add-marketplace-role', 'edit-marketplace-role', 'add-permission'])): ?> text-white
                                     <?php else: ?> collapsed <?php endif; ?>">
                                        <span class="align-middle">Access Control</span>
                                    </a>
                                    <ul id="marketplaceRequests" class="sidebar-dropdown list-unstyled collapse <?php if(in_array(request()->segment(3), ['manage-marketplace-roles', 'add-marketplace-role', 'edit-marketplace-role', 'add-permission'])): ?> show <?php endif; ?>" data-parent="#marketplace">
                                        <li class="sidebar-item <?php if(in_array(request()->segment(3), ['manage-marketplace-roles', 'add-marketplace-role', 'edit-marketplace-role', 'add-permission'])): ?> active <?php endif; ?>">
                                            <a class="sidebar-link" href="<?php echo e(url('admin/k12connections/manage-marketplace-roles')); ?>">
                                                <span class="align-middle">Roles</span>
                                            </a>
                                        </li>
                                    </ul>
                                </li>

                                
                                <li class="sidebar-item <?php if(in_array(request()->segment(3), ['manage-resources', 'add-resource', 'edit-resource'])): ?> active <?php endif; ?>">
                                    <a class="sidebar-link" href="<?php echo e(url('admin/k12connections/manage-resources')); ?>">
                                        <span class="align-middle">Manage Resources</span>
                                    </a>
                                </li>
                                

                                <?php if(in_array('platform schools', $permissions)): ?>

                                <li class="sidebar-item <?php if(in_array(request()->segment(3), ['manage-educator'])): ?> active <?php endif; ?>">
                                    <a class="sidebar-link" href="<?php echo e(url('admin/k12connections/manage-educator')); ?>">
                                        <span class="align-middle">Manage Educator</span>
                                    </a>
                                </li>
                                <?php endif; ?>

                                <?php if(in_array('platform schools', $permissions)): ?>
                                    <li class="sidebar-item <?php if(in_array(request()->segment(3), ['manage-subject-approval'])): ?> active <?php endif; ?>">
                                        <a class="sidebar-link" href="<?php echo e(url('admin/k12connections/manage-subject-approval/All')); ?>">
                                            <?php
                                                $pendingSubjectCount = \App\Models\v1\UserAdditionalSubject::where('status', 'pending')->count();
                                            ?>
                                            <span class="align-middle">Manage Subject Approval <?php echo e($pendingSubjectCount > 0 ? '('.$pendingSubjectCount.')' : ''); ?> </span>
                                        </a>
                                    </li>
                                <?php endif; ?>

                                <?php if(in_array('programs', $permissions)): ?>
                                    <li class="sidebar-item <?php if(in_array(request()->segment(3), ['programs', 'add-program', 'view-program'])): ?> active <?php endif; ?>">
                                        <a class="sidebar-link" href="<?php echo e(url('admin/k12connections/programs/ALL')); ?>">
                                            <span class="align-middle">Manage Program</span>
                                        </a>
                                    </li>
                                <?php endif; ?>

                                <?php if(in_array('requirements', $permissions)): ?>
                                    <li class="sidebar-item <?php if(in_array(request()->segment(3), ['requirements', 'view-requirements', 'add-requirements', 'edit-requirements'])): ?> active <?php endif; ?>">
                                        <a class="sidebar-link" href="<?php echo e(url('admin/k12connections/requirements')); ?>">
                                            <span class="align-middle">Manage Requirements</span>
                                        </a>
                                    </li>
                                <?php endif; ?>

                                <li class="sidebar-item">
                                    <a href="#marketplaceRequests" data-toggle="collapse" class="sidebar-link <?php if(in_array(request()->segment(3), ['applied-requests', 'program-invites'])): ?> text-white
                                     <?php else: ?> collapsed <?php endif; ?>">
                                        <span class="align-middle">Manage Requests</span>
                                    </a>
                                    <ul id="marketplaceRequests" class="sidebar-dropdown list-unstyled collapse <?php if(in_array(request()->segment(3), ['applied-requests', 'program-invites'])): ?> show <?php endif; ?>" data-parent="#marketplace">
                                        <li class="sidebar-item <?php if(request()->segment(3) === 'applied-requests'): ?> active <?php endif; ?>">
                                            <a class="sidebar-link" href="<?php echo e(url('admin/k12connections/applied-requests')); ?>">
                                                <span class="align-middle">Applied Requests</span>
                                            </a>
                                        </li>
                                        <li class="sidebar-item <?php if(request()->segment(3) === 'program-invites'): ?> active <?php endif; ?>">
                                            <a class="sidebar-link" href="<?php echo e(url('admin/k12connections/program-invites')); ?>">
                                                <span class="align-middle">Program Invites</span>
                                            </a>
                                        </li>
                                    </ul>
                                </li>

                                <?php if(in_array('platform schools', $permissions)): ?>
                                    <li class="sidebar-item <?php if(in_array(request()->segment(3), ['manage-platform-schools', 'add-platform-schools', 'view-platform-schools', 'edit-platform-schools'])): ?> active <?php endif; ?>">
                                        <a class="sidebar-link" href="<?php echo e(url('admin/k12connections/manage-platform-schools')); ?>">
                                            <span class="align-middle">Manage Platform Schools</span>
                                        </a>
                                    </li>
                                <?php endif; ?>

                                <?php if(in_array('platform schools', $permissions)): ?>
                                    <li class="sidebar-item <?php if(in_array(request()->segment(3), ['manage-school-roles'])): ?> active <?php endif; ?>">
                                        <a class="sidebar-link" href="<?php echo e(url('admin/k12connections/manage-school-roles')); ?>">
                                            <span class="align-middle">Manage School Roles</span>
                                        </a>
                                    </li>
                                <?php endif; ?>


                                  <?php if(in_array('platform schools', $permissions)): ?>
                                    <li class="sidebar-item" <?php if(in_array(request()->segment(3), ['manage-Budget-schools'])): ?> active <?php endif; ?>>
                                        <a class="sidebar-link" href="<?php echo e(url('admin/k12connections/manage-Budget-schools')); ?>">
                                            <span class="align-middle">Manage Budget Schools</span>
                                        </a>
                                    </li>
                                <?php endif; ?>

                                <li class="sidebar-item <?php if(in_array(request()->segment(2), ['email-template'])): ?> active <?php endif; ?>">
                                    <a class="sidebar-link" href="<?php echo e(url('admin/email-template')); ?>">
                                        <span class="align-middle">Email Template</span>
                                    </a>
                                </li>

                                <li class="sidebar-item <?php if(request()->segment(3) == 'manage-email-schedules'): ?> active <?php endif; ?>">
                                    <a class="sidebar-link" href="<?php echo e(url('admin/k12connections/manage-email-schedules')); ?>">
                                        <span class="align-middle">Manage Email Schedules</span>
                                    </a>
                                </li>

                            </ul>
                        </li>
                    <?php endif; ?>
                <?php endif; ?>

                <?php if(isset($res['managenotice'])): ?>
                <?php if(in_array('view',json_decode($res['managenotice'] ,true))): ?>
                <?php if(in_array('view',json_decode($res['managenotice'] ,true))): ?>
                <li class="sidebar-item <?php if (request()->segment(1) == 'notice-list' or  request()->segment(1) == 'view-mail') {
                                            echo 'active';
                                        } ?>">
                    <a class="sidebar-link" href="<?php echo e(url('notice-list')); ?>">
                        <i class="align-middle" data-feather="users"></i>
                        <span class="align-middle">Manage Notice </span>
                    </a>
                </li>
                <?php endif; ?>
                <?php endif; ?>
                <?php endif; ?>

                <?php if(isset($res['manageapplication'])): ?>
                <?php if(in_array('view',json_decode($res['manageapplication'] ,true))): ?>
                <?php if(in_array('view',json_decode($res['manageapplication'] ,true))): ?>
                <li class="sidebar-item <?php if (request()->segment(1) == 'new-application-list' or request()->segment(1) == 'All-application-list' or request()->segment(1) == 'Under-Reveiw-application-list' or request()->segment(1) == 'Resubmit-request-application-list' or request()->segment(1) == 'Pending-Interview-application-list' or request()->segment(1) == 'Approved-application-list' or request()->segment(1) == 'Active-application-list' or request()->segment(1) == 'Reject-application-list' or request()->segment(1) == 'view-application') {
                                            echo 'active';
                                        } ?>">
                    <a class="sidebar-link" href="<?php echo e(url('new-application-list')); ?>">
                        <i class="align-middle" data-feather="file-text"></i>
                        <span class="align-middle">Manage Application </span>
                    </a>
                </li>
                <?php endif; ?>
                <?php endif; ?>
                <?php endif; ?>

                <?php if(isset($res['manageprogram'])): ?>
                <?php if(in_array('view',json_decode($res['manageprogram'] ,true))): ?>
                <?php if(in_array('view',json_decode($res['manageprogram'] ,true))): ?>
                <li class="sidebar-item <?php if (request()->segment(1) == 'programs' or request()->segment(1) == 'view-program' or request()->segment(1) == 'add-program' or  request()->segment(1) == 'edit-program') {
                                            echo 'active';
                                        } ?>">
                    <a class="sidebar-link" href="<?php echo e(url('programs/ALL')); ?>">
                        <i class="align-middle" data-feather="activity"></i>
                        <span class="align-middle">Manage program </span>
                    </a>
                </li>
                <?php endif; ?>
                <?php endif; ?>
                <?php endif; ?>

                <?php if(isset($res['manageclasses'])): ?>
                <?php if(in_array('view',json_decode($res['manageclasses'] ,true))): ?>
                <?php if(in_array('view',json_decode($res['manageclasses'] ,true))): ?>
                <li class="sidebar-item <?php if (request()->segment(1) == 'classes') {
                                            echo 'active';
                                        } ?>">
                    <a class="sidebar-link" href="<?php echo e(url('admin/classes/list')); ?>">
                        <i class="align-middle" data-feather="activity"></i>
                        <span class="align-middle">Manage Classes </span>
                    </a>
                </li>
                <?php endif; ?>
                <?php endif; ?>
                <?php endif; ?>

                <?php if(isset($res['manageInvites'])): ?>
                <?php if(in_array('view',json_decode($res['manageInvites'] ,true))): ?>

                <li class="sidebar-item <?php echo e((isset($sidebarMenu) && $sidebarMenu=='program-invite')?'active':''); ?> ">
                    <a class="sidebar-link" href="<?php echo e(route('admin.program.invites')); ?>">
                        <i class="align-middle" data-feather="activity"></i>
                        <span class="align-middle">Program Invites </span>
                    </a>
                </li>

                <?php endif; ?>
                <?php endif; ?>



                <?php if(isset($res['managerequest'])): ?>
                <?php if(in_array('view',json_decode($res['managerequest'] ,true))): ?>

                <li class="sidebar-item <?php echo e((isset($sidebarGroup) && $sidebarGroup=='requestMenu')?'active':''); ?> ">
                    <a href="#requestMenu" data-toggle="collapse" class="sidebar-link collapsed ">
                        <i class="align-middle" data-feather="sliders"></i>
                        <span class="align-middle">Program Requests </span>
                    </a>

                    <ul id="requestMenu" class="sidebar-dropdown list-unstyled collapse <?php echo e((isset($sidebarGroup) && $sidebarGroup =='requestMenu')?'show':''); ?>" data-parent="#sidebar">

                        <li class="sidebar-item <?php echo e((isset($sidebarMenu) && $sidebarMenu=='program-invite-request')?'active':''); ?>">
                            <a class="sidebar-link" href="<?php echo e(route('admin.program.requests')); ?>">
                                <span class="align-middle">Applied Requests </span>
                            </a>
                        </li>

                        <li class="sidebar-item <?php echo e((isset($sidebarMenu) && $sidebarMenu=='replacement-requests')?'active':''); ?>">
                            <a class="sidebar-link" href="<?php echo e(route('admin.program.replacement-requests')); ?>">
                                <span class="align-middle">Replacement And Sub Requests </span>
                            </a>
                        </li>

                        <li class="sidebar-item <?php echo e((isset($sidebarMenu) && $sidebarMenu=='program-cancel-sub')?'active':''); ?>">
                            <a class="sidebar-link" href="<?php echo e(route('admin.program.cancel-sub-requests')); ?>">
                                <span class="align-middle">Substitue Cancellation Requests </span>
                            </a>
                        </li>
                    </ul>
                </li>


                <?php endif; ?>
                <?php endif; ?>

                <?php if(isset($res['managePayment'])): ?>
                <?php if(in_array('view',json_decode($res['managePayment'] ,true))): ?>
                <li class="sidebar-item <?php echo e((isset($sidebarMenu) && $sidebarMenu=='manage-payments')?'active':''); ?>">
                    <a class="sidebar-link" href="<?php echo e(route('admin.manage-payments')); ?>">
                        <i class="align-middle" data-feather="dollar-sign"></i>
                        <span class="align-middle">Manage Payments </span>
                    </a>
                </li>

                <?php endif; ?>
                <?php endif; ?>

                <?php if(isset($res['manageReview'])): ?>
                <?php if(in_array('view',json_decode($res['manageReview'] ,true))): ?>

                <li class="sidebar-item <?php echo e((isset($sidebarMenu) && $sidebarMenu=='manage-reviews')?'active':''); ?>">
                    <a class="sidebar-link" href="<?php echo e(route('admin.manage-reviews')); ?>">
                        <i class="align-middle" data-feather="file-text"></i>
                        <span class="align-middle">Manage Reviews </span>
                    </a>
                </li>

                <?php endif; ?>
                <?php endif; ?>


                <?php if(isset($res['managechat'])): ?>
                <?php if(array_key_exists('managechat',$res)): ?>
                <?php if(in_array('view',json_decode($res['managechat'] ,true))): ?>
                <li class="sidebar-item <?php if (request()->segment(1) == 'adminchat') {
                                            echo 'active';
                                        } ?>">
                    <a class="sidebar-link" href="<?php echo e(url('adminchat')); ?>">
                        <i class="align-middle" data-feather="file-text"></i>
                        <span class="align-middle">Chat </span>
                    </a>
                </li>

                <?php endif; ?>
                <?php endif; ?>
                <?php endif; ?>
                <?php if(isset($res['generalsetting'])): ?>
                <?php if(array_key_exists('generalsetting',$res)): ?>
                <?php if(in_array('view',json_decode($res['generalsetting'] ,true))): ?>
                <li class="sidebar-item">
                <li class="sidebar-item <?php if (request()->segment(1) == 'manage-resources' or request()->segment(1) == 'add-resource' or request()->segment(1) == 'edit-resource' or request()->segment(1) == 'manage-training-video' or request()->segment(1) == 'edit-form' or request()->segment(1) == 'add-form' or  request()->segment(1) == 'manage-form'  or request()->segment(1) == 'addtrainingvideo' or request()->segment(1) == 'edit-training' or request()->segment(1) == 'subject-list' or request()->segment(1) == 'manage-cbo' || request()->segment(1) == 'addcbo' or request()->segment(1) == 'edit-cbo' or request()->segment(1) == 'add-subject' or request()->segment(1) == 'edit-subject' or request()->segment(1) == 'terms-setting-list' or request()->segment(1) == 'class-list' or request()->segment(1) == 'add-class' or request()->segment(1) == 'edit-class' or request()->segment(1) == 'viewtermssetting' || request()->segment(1) == 'faqs-list' or request()->segment(1) == 'addfaqs' or request()->segment(1) == 'viewfaqs' || request()->segment(1) == 'medical-data-questions-list' or request()->segment(1) == 'add-medical-data-questions' or request()->segment(1) == 'edit-medical-data-questions' || request()->segment(1) == 'eco-footprints' || request()->segment(1) == 'trainer-medical-data-questions-list' or request()->segment(1) == 'add-trainer-medical-data-questions' or request()->segment(1) == 'class-list' or request()->segment(1) == 'add-class' or request()->segment(1) == 'edit-class' or request()->segment(1) == 'add-sub-subject' or request()->segment(1) == 'credentialing-agency' or request()->segment(1) == 'add-agency' or request()->segment(1) == 'edit-agency' or request()->segment(1) == 'add-certificates' or request()->segment(1) == 'testimonial-list') {
                                            echo 'active';
                                        } ?>">

                    <a href="#termsmgt" data-toggle="collapse" class="sidebar-link collapsed ">
                        <i class="align-middle" data-feather="sliders"></i>

                        <span class="align-middle"><?php echo e(__('messages.setting_terms')); ?></span>
                    </a>


                    <ul id="termsmgt" class="sidebar-dropdown list-unstyled collapse <?php if (request()->segment(1) == 'manage-resources' or request()->segment(1) == 'add-resource' or request()->segment(1) == 'edit-resource'  or request()->segment(1) == 'manage-training-video' or request()->segment(1) == 'edit-form'  or request()->segment(1) == 'add-form' or request()->segment(1) == 'manage-form'  or request()->segment(1) == 'addtrainingvideo' or request()->segment(1) == 'edit-training' or request()->segment(1) == 'subject-list' or request()->segment(1) == 'manage-cbo' || request()->segment(1) == 'addcbo' or request()->segment(1) == 'edit-cbo' or request()->segment(1) == 'add-subject' or request()->segment(1) == 'edit-subject' or request()->segment(1) == 'terms-setting-list' or request()->segment(1) == 'viewtermssetting' || request()->segment(1) == 'faqs-list' or request()->segment(1) == 'addfaqs' or request()->segment(1) == 'viewfaqs' or request()->segment(1) == 'manage-district' || request()->segment(1) == 'adddistrict' or request()->segment(1) == 'edit-district' or request()->segment(1) == 'class-list' or request()->segment(1) == 'add-class' or request()->segment(1) == 'edit-class' or request()->segment(1) == 'add-sub-subject' or request()->segment(1) == 'credentialing-agency' or request()->segment(1) == 'add-agency' or request()->segment(1) == 'edit-agency' or request()->segment(1) == 'add-certificates' or request()->segment(1) == 'testimonial-list') {
                                                                                            echo 'show';
                                                                                        } ?>" data-parent="#sidebar">

                        <?php if(isset($res['managedistrict'])): ?>
                        <?php if(array_key_exists('managedistrict',$res)): ?>
                        <?php if(in_array('view',json_decode($res['managedistrict'] ,true))): ?>

                        <li class="sidebar-item <?php if (request()->segment(1) == 'manage-district' || request()->segment(1) == 'adddistrict' or request()->segment(1) == 'edit-district') {
                                                    echo 'active';
                                                } ?>">
                            <a class="sidebar-link" href="<?php echo e(url('manage-district')); ?>">
                                <span class="align-middle">Manage District </span>
                            </a>
                        </li>
                        <?php endif; ?>
                        <?php endif; ?>
                        <?php endif; ?>


                        <?php if(isset($res['managecbo'])): ?>
                        <?php if(array_key_exists('managecbo',$res)): ?>
                        <?php if(in_array('view',json_decode($res['managecbo'] ,true))): ?>

                        <li class="sidebar-item <?php if (request()->segment(1) == 'manage-cbo' || request()->segment(1) == 'addcbo' or request()->segment(1) == 'edit-cbo') {
                                                    echo 'active';
                                                } ?>">
                            <a class="sidebar-link" href="<?php echo e(url('manage-cbo')); ?>">
                                <span class="align-middle">Manage CBO </span>
                            </a>
                        </li>
                        <?php endif; ?>
                        <?php endif; ?>
                        <?php endif; ?>

                        <?php if(isset($res['manageclass'])): ?>
                        <?php if(array_key_exists('manageclass',$res)): ?>
                        <?php if(in_array('view',json_decode($res['manageclass'] ,true))): ?>
                        <li class="sidebar-item <?php if (request()->segment(1) == 'class-list' or request()->segment(1) == 'add-class' or request()->segment(1) == 'edit-class') {
                                                    echo 'active';
                                                } ?>">
                            <a class="sidebar-link" href="<?php echo e(url('class-list')); ?>">
                                Manage Grade Level
                            </a>
                        </li>

                        <?php endif; ?>
                        <?php endif; ?>
                        <?php endif; ?>


                        <?php if(isset($res['managesubject'])): ?>
                        <?php if(array_key_exists('managesubject',$res)): ?>
                        <?php if(in_array('view',json_decode($res['managesubject'] ,true))): ?>
                        <li class="sidebar-item <?php if (request()->segment(1) == 'subject-list' or request()->segment(1) == 'add-subject' or request()->segment(1) == 'edit-subject' or request()->segment(1) == 'add-sub-subject') {
                                                    echo 'active';
                                                } ?>">
                            <a class="sidebar-link" href="<?php echo e(url('subject-list')); ?>">
                                Manage Subject
                            </a>
                        </li>
                        <?php endif; ?>
                        <?php endif; ?>
                        <?php endif; ?>

                        <li class="sidebar-item <?php if (request()->segment(1) == 'testimonial-list') { echo 'active';} ?>">
                            <a class="sidebar-link" href="<?php echo e(url('testimonial-list')); ?>">
                                Manage Testimonials
                            </a>
                        </li>

                        <?php if(isset($res['manageagency'])): ?>
                        <?php if(array_key_exists('manageagency',$res)): ?>
                        <?php if(in_array('view',json_decode($res['manageagency'] ,true))): ?>
                        <li class="sidebar-item <?php if (request()->segment(1) == 'credentialing-agency' or request()->segment(1) == 'add-agency' or request()->segment(1) == 'edit-agency' or request()->segment(1) == 'add-certificates') {
                                                    echo 'active';
                                                } ?>">
                            <a class="sidebar-link" href="<?php echo e(url('credentialing-agency')); ?>">
                                Manage Credentialing Agency
                            </a>
                        </li>

                        <?php endif; ?>
                        <?php endif; ?>
                        <?php endif; ?>
                      <!-- //this is for the Manage Credentialing Agency -->
                      <li class="sidebar-item <?php if (request()->segment(1) == 'additional-certificate' or request()->segment(1) == 'add-additional-certificate' or request()->segment(1) == 'edit-additional-certificate' or request()->segment(1) == 'add-certificates') {
                                                    echo 'active';
                                                } ?>">
                            <a class="sidebar-link" href="<?php echo e(url('additional_certificate')); ?>">
                               Manage Additional Certificates
                            </a>
                        </li>





                        <?php if(isset($res['trainingvideo'])): ?>
                        <?php if(array_key_exists('trainingvideo',$res)): ?>
                        <?php if(in_array('view',json_decode($res['trainingvideo'] ,true))): ?>
                        <li class="sidebar-item <?php if (request()->segment(1) == 'manage-training-video' or request()->segment(1) == 'addtrainingvideo' or request()->segment(1) == 'edit-training') {
                                                    echo 'active';
                                                } ?>">
                            <a class="sidebar-link" href="<?php echo e(url('manage-training-video')); ?>">
                                Manage Training Video
                            </a>
                        </li>

                        <?php endif; ?>
                        <?php endif; ?>
                        <?php endif; ?>


                        <?php if(isset($res['resources'])): ?>
                        <?php if(array_key_exists('resources',$res)): ?>
                        <?php if(in_array('view',json_decode($res['resources'] ,true))): ?>
                        <li class="sidebar-item <?php if (request()->segment(1) == 'manage-resources' or request()->segment(1) == 'add-resource' or request()->segment(1) == 'edit-resource') {
                                                    echo 'active';
                                                } ?>">
                            <a class="sidebar-link" href="<?php echo e(url('manage-resources')); ?>">
                                Manage Resources
                            </a>
                        </li>
                        <?php endif; ?>
                        <?php endif; ?>
                        <?php endif; ?>

                        <?php if(isset($res['manageform'])): ?>
                        <?php if(array_key_exists('manageform',$res)): ?>
                        <?php if(in_array('view',json_decode($res['manageform'] ,true))): ?>
                        <li class="sidebar-item <?php if (request()->segment(1) == 'manage-form' or request()->segment(1) == 'edit-form'  or request()->segment(1) == 'add-form' or request()->segment(1) == 'edit-form') {
                                                    echo 'active';
                                                } ?>">
                            <a class="sidebar-link" href="<?php echo e(url('manage-form')); ?>">
                                Manage form
                            </a>
                        </li>

                        <?php endif; ?>
                        <?php endif; ?>
                        <?php endif; ?>



                        <li class="sidebar-item <?php if (request()->segment(1) == 'terms-setting-list' or request()->segment(1) == 'viewtermssetting') {
                                                    echo 'active';
                                                } ?>">
                            <a class="sidebar-link" href="<?php echo e(url('terms-setting-list')); ?>">Content Setting</a>
                        </li>



                        <li class="sidebar-item <?php if (request()->segment(1) == 'faqs-list' or request()->segment(1) == 'addfaqs' or request()->segment(1) == 'viewfaqs') {
                                                    echo 'active';
                                                } ?>">
                            <a class="sidebar-link" href="<?php echo e(url('faqs-list')); ?>"><?php echo e(__('messages.faq')); ?></a>
                        </li>

                        <li class="sidebar-item <?php if (request()->segment(2) == 'budget-content-management' or request()->segment(2) == 'add-subject' or request()->segment(1) == 'edit-subject' or request()->segment(1) == 'add-sub-subject') {
                                            echo 'active';
                                        } ?>">
                            <a class="sidebar-link" href="<?php echo e(url('admin/budget-content-management')); ?>">
                                Budget & Content Management
                            </a>
                        </li>

                    </ul>
                </li>
                <?php endif; ?>
                <?php endif; ?>
                <?php endif; ?>

                <li class="sidebar-item">
                    <li class="sidebar-item <?php if (request()->segment(1) == 'list-school-management-setting' || request()->segment(1) == 'requirements-list' || request()->segment(1) == 'add-school-management-setting') { echo 'active'; } ?>">

                        <a href="#newSchool" data-toggle="collapse" class="sidebar-link collapsed ">
                            <i class="align-middle" data-feather="file-text"></i>
                            <span class="align-middle">Manage School</span>
                        </a>

                        <ul id="newSchool" class="sidebar-dropdown list-unstyled collapse <?php if (request()->segment(1) == 'list-school-management-setting' || request()->segment(1) == 'requirements-list' || request()->segment(1) == 'add-school-management-setting') { echo 'show'; } ?>" data-parent="#sidebar">

                            

                            <li class="sidebar-item <?php if (request()->segment(1) == 'requirements-list') { echo 'active'; } ?>">

                                <a class="sidebar-link" href="<?php echo e(url('requirements-list')); ?>">
                                    <span class="align-middle">Requirements</span>
                                </a>

                            </li>
                        </ul>
                    </li>
                </li>

                <li class="sidebar-item <?php if (request()->segment(2) == 'heat-map') {
                                            echo 'active';
                                        } ?>">
                    <a class="sidebar-link" href="<?php echo e(route('admin.heatMap')); ?>">
                        <i class="align-middle" data-feather="file-text"></i>
                        <span class="align-middle">Heat Map </span>
                    </a>
                </li>
            </ul>
        </div>
    </nav>
<?php /**PATH D:\whizara\whizara\resources\views/admin/layouts/sidebar.blade.php ENDPATH**/ ?>