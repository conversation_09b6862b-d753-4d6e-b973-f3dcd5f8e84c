<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddDistrictSchoolIdAndIsVisibleToSchoolToPlatformSchoolRequirementsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('platform_school_requirements', function (Blueprint $table) {
            $table->string('district_school_id', 255)->nullable()->after('school_id');
            $table->boolean('is_visible_to_school')->default(0)->after('status');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('platform_school_requirements', function (Blueprint $table) {
            $table->dropColumn('district_school_id');
            $table->dropColumn('is_visible_to_school');
        });
    }
}
