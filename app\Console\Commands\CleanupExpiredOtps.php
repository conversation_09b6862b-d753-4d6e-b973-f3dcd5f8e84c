<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class CleanupExpiredOtps extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:otps_cleanup';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Delete OTPs expired for more than 1 day';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $count = 0;
        Otp::where('expires_at', '<', Carbon::now())
            ->chunkById(500, function ($otps) use (&$count) {
                foreach ($otps as $otp) {
                    $otp->delete();
                    $count++;
                }
            });

        $this->info("Deleted {$count} expired OTP(s).");
    }
}
