<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class ObservationNote extends Model
{
    protected $table = 'applicant_observation_notes';
    protected $fillable = [
        'applicant_id', 'note', 'status', 'created_by'
    ];

    public function applicant()
    {
        return $this->belongsTo(OnboardingInstructor::class, 'applicant_id');
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }
}
