<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddExtraFieldsToSchoolClassBudgetsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('school_class_budgets', function (Blueprint $table) {
            $table->enum('esol_certification', ['yes', 'no'])->default('no')->after('special_education_certification');
            $table->integer('number_of_days')->nullable()->after('expected_class_size');
            $table->integer('number_of_hours')->nullable()->after('number_of_days');
            $table->string('case_load')->nullable()->after('number_of_hours');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('school_class_budgets', function (Blueprint $table) {
            $table->dropColumn(['esol_certification', 'number_of_days', 'number_of_hours', 'case_load']);
        });
    }
}
