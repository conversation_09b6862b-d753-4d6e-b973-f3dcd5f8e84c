<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ChatbotChat extends Model
{
    use SoftDeletes;

    protected $table = 'chatbot_interactions';
    protected $keyType = 'string';
    public $incrementing = false;

    protected $fillable = [
        'user_id',
        'platform',
        'user_message',
        'bot_response',
        'session_token',
        'conversation_id',
        'meta',
    ];

    protected $casts = [
        'meta' => 'array',
    ];

    protected $dates = [
        'created_at',
        'updated_at',
        'deleted_at',
    ];
}
