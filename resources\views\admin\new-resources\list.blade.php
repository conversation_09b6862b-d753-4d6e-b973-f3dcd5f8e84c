@extends('admin.layouts.master')

@section('title') Resources | Whizara @endsection

@section('content')
<main class="content">
    <div class="container-fluid p-0">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ url('admin-dashboard') }}" class="text-primary">{{ __('messages.dashboard') }}</a></li>
                <li class="breadcrumb-item active" aria-current="page">Manage Marketplace</li>
                <li class="breadcrumb-item active" aria-current="page">Manage Resources</li>
            </ol>
        </nav>
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h3 class="mb-0">Resources</h3>
            <a href="{{ route('admin.manage-resources.create') }}" class="btn btn-primary">Add Resource</a>
        </div>

        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped" id="resourcesTable" style="width:100%">
                        <thead class="thead-dark">
                            <tr>
                                <th>ID</th>
                                <th>Title</th>
                                <th>Tag</th>
                                <th>Created By</th>
                                <th>Created At</th>
                                <th>Updated By</th>
                                <th>Updated At</th>
                                <th>Status</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody id="resourcesBody">
                            {{-- {!! view('admin.new-resources.list-components', ['resources' => []]) !!} --}}
                            @php
                                $resources = $resources ?? collect();
                                $resources = $resources->map(function($resource) {
                                    $resource->tags = json_decode($resource->tags, true);
                                    return $resource;
                                });
                            @endphp
                            @foreach($resources as $resource)
                                <tr>
                                    <td>{{ $resource->id }}</td>
                                    <td>{{ $resource->title }}</td>
                                    <td> @if(is_array($resource->tags)) {{ implode(', ', $resource->tags) }} @else - @endif</td>
                                    <td>{{ optional($resource->createdBy)->first_name ?? '-' }}</td>
                                    <td>{{ $resource->created_at }}</td>
                                    <td>{{ optional($resource->updatedBy)->first_name ?? '-' }}</td>
                                    <td>{{ $resource->updated_at }}</td>
                                    <td>
                                        <select class="form-control status-select" data-id="{{ $resource->id }}">
                                            <option value="1" {{ $resource->status == 1 ? 'selected' : '' }}>Active</option>
                                            <option value="0" {{ $resource->status == 0 ? 'selected' : '' }}>Inactive</option>
                                        </select>
                                    </td>
                                    <td>
                                        <div class="w-100 d-flex justify-content-center align-items-center">
                                            {{-- <a href="{{ route('admin.manage-resources.show', ['manage_resource' => $resource->id]) }}">
                                                <button class="btn btn-rounded btn-block btn-xs btn-outline-info"><i class="fa fa-eye" aria-hidden="true"></i></button>
                                            </a>
                                            &nbsp; --}}
                                            <a href="{{ route('admin.manage-resources.edit', ['manage_resource' => $resource->id]) }}">
                                                <button class="btn btn-rounded btn-block btn-xs btn-outline-secondary"><i class="fa fa-pencil" aria-hidden="true"></i></button>
                                            </a>
                                            &nbsp;
                                            <a class="btn-delete" href="javascript:void(0);" data-id="{{ $resource->id }}">
                                                <button class="btn btn-rounded btn-block btn-xs btn-outline-danger"><i class="fa fa-trash" aria-hidden="true"></i></button>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>


</main>
@endsection

@section('scripts')
<script src="{{asset('js/sweetalert2.all.min.js')}}"></script>
<script src="https://cdn.ckeditor.com/4.22.1/standard/ckeditor.js"></script>
<script>
    let tableInstance = null;

    function loadTable() {
        $.get("{{ route('admin.manage-resources.index') }}", { ajax: 1 }, function(res){
            if(res.success){
                $('#resourcesBody').html(res.view);
                if (tableInstance) { tableInstance.destroy(); }
                tableInstance = $('#resourcesTable').DataTable();
            }
        });
    }

    function resetForm(){
        $('#resourceForm')[0].reset();
        $('#resource_id').val('');
        if (CKEDITOR.instances['summary']) CKEDITOR.instances['summary'].setData('');
        if (CKEDITOR.instances['content']) CKEDITOR.instances['content'].setData('');
    }

    function openModal(data){
        resetForm();
        if(data){
            $('#resource_id').val(data.id);
            $('#title').val(data.title);
            if (CKEDITOR.instances['summary']) CKEDITOR.instances['summary'].setData(data.summary || '');
            if (CKEDITOR.instances['content']) CKEDITOR.instances['content'].setData(data.content || '');
            $('#status').val(data.status);
            $('#modalTitle').text('Edit Resource');
        } else {
            $('#modalTitle').text('Add Resource');
        }
        $('#resourceModal').modal('show');
    }

    $(document).ready(function(){
        loadTable();
        tableInstance = $('#resourcesTable').DataTable();

        // No modal usage anymore

        // No form submission on index page

        $(document).on('click', '.btn-delete', function(e) {
            e.preventDefault();
            const id = $(this).data('id');
            const currentRow = $(this);

            swal({
                title: "Delete?",
                text: "Please ensure and then confirm!",
                type: "warning",
                showCancelButton: true,
                confirmButtonText: "Yes, delete it!",
                cancelButtonText: "No, cancel!",
                reverseButtons: true
            }).then(function (result) {
                if (result.value === true) {
                    $.ajaxSetup({
                        headers: {
                            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                        }
                    });

                    $.ajax({
                        url: "{{ route('admin.manage-resources.destroy', ['manage_resource' => 'ID']) }}".replace('ID', id),
                        method: 'POST',
                        data: {
                            _token: "{{ csrf_token() }}",
                            _method: 'DELETE'
                        }
                    }).done(function(res) {
                        if (res.success) {
                            loadTable();
                            swal("Deleted!", res.message || "Resource has been deleted successfully.", "success");
                        } else {
                            swal("Error!", res.message || "Something went wrong.", "error");
                        }
                    }).fail(function() {
                        swal("Error!", "Failed to delete resource.", "error");
                    });
                }
            });
        });

        $(document).on('change', '.status-select', function(){
            const id = $(this).data('id');
            const status = $(this).val();
            $.post("{{ route('admin.manage-resources.status', ['id' => 'ID']) }}".replace('ID', id), { _token: "{{ csrf_token() }}", status })
                .done(function(res){ loadTable(); alertify.success(res.message); });
        });
    });
</script>
@endsection


