@extends('admin.layouts.master')
@section('title') Add Instructor | Whizara @endsection
@section('content')
<style>
    .inpersonrate, .onlinerate{
        display: none;
    }
</style>
{{-- MAIN SECTION START --}}
<main class="content">
    <div class="container-fluid p-0">
        {{-- BREADCRUMB START --}}
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{url('admin-dashboard')}}" class="text-primary">{{ __('messages.dashboard') }}</a></li>
                <li class="breadcrumb-item active float-right" aria-current="page"><a href="{{url('admin/k12connections/manage-instructor/ALL')}}">Manage Applicant</a></li>
                <li class="breadcrumb-item active" aria-current="page">Add Applicant</li>

            </ol>
        </nav>
        {{-- BREADCRUMB END --}}
        {{-- Add New Instructor Start --}}
        <form id="add_instructor_form" enctype='multipart/form-data'>
            <div class="row justify-content-center">
                <div class="col-lg-12 col-md-9">
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="card">
                                <div class="card-header border-bottom"><h5 class="mb-0">{{ __('Add Applicant') }}</h5></div>
                                {{ csrf_field() }}
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6 form-group">
                                            <label class="form-label">{{ __('First Name') }}<span style="color: red" >*</span></label>
                                            <input type="text" id="first_name" maxlength="30" class="form-control" name="first_name" placeholder="Enter first name">
                                            <span id="first_name_error" class="err"></span>
                                        </div>
                                        <div class="col-md-6 form-group">
                                            <label class="form-label">{{ __('Last Name') }}<span style="color: red" >*</span></label>
                                            <input type="text" id="last_name" maxlength="30" class="form-control" name="last_name" placeholder="Enter last name">
                                            <span id="last_name_error" class="err"></span>
                                        </div>
                                        <div class="col-md-6 form-group">
                                            <label class="form-label">{{ __('Email') }}<span style="color: red" >*</span></label>
                                            <input type="text" id="email" maxlength="50" placeholder="Enter email" class="form-control" name="email">
                                            <span id="email_error" class="err"></span>
                                        </div>
                                        {{-- <div class="col-md-6 form-group">
                                            <label class="form-label">Phone Number<span style="color: red" >*</span></label>
                                            <input type="text" class="form-control" id="phone" name="phone" maxlength="10" oninput="this.value = this.value.replace(/[^0-9]/g, '');" placeholder="Enter phone number">
                                            <span id="phone_error" class="err"></span>
                                        </div> --}}
                                        <div class="col-md-6 form-group" id="staterow">
                                            <label class="form-label">State</label>
                                            <select name="state[]" data-placeholder="Select State" id="state" class="form-control select2" multiple>
                                                <option value="">Select State</option>
                                                @if(!empty($states) && $states->count())
                                                    @foreach($states as $key => $data)
                                                        <option value="{{ $data->name }}">{{ $data->name }}</option>
                                                    @endforeach
                                                @endif
                                            </select>
                                            <span id="state_error" class="err"></span>
                                        </div>
                                        <div class="col-md-6 form-group">
                                            <label class="form-label">City</label>
                                            <input type="text" id="city" class="form-control" placeholder="Enter city" name="city" maxlength="50">
                                            <span id="city_error" class="err"></span>
                                        </div>
                                        <div class="col-md-6 form-group profileStatus">
                                            <label class="form-label">Select Profile Status<span style="color: red" >*</span></label>
                                            <select name="profilestatusedit" id="profilestatusedit" class="form-control" onchange="changefeild()">
                                                <option value="">Select Profile Status</option>
                                                <option value="16">Approve as online</option>
                                                <option value="17">Approve as in-person</option>
                                                <option value="20">Approve as Both</option>
                                            </select>
                                            <span id="profilestatusedit_error" class="err"></span>
                                        </div>
                                        <div class="col-md-6 form-group onlinerate">
                                            <label class="form-label">Online Hourly Rate</label>
                                            <input type="number" id="onlinerate" class="form-control" placeholder="Enter online hourly rate" name="onlinerate">
                                            <span id="onlinerate_error" class="err"></span>
                                        </div>
                                        <div class="col-md-6 form-group inpersonrate">
                                            <label class="form-label">In-Person Hourly Rate</label>
                                            <input type="number" placeholder="Enter in-person hourly rate" id="inpersonrate" class="form-control" name="inpersonrate">
                                            <span id="inpersonrate_error" class="err"></span>
                                        </div>
                                        {{-- <div class="col-md-6 form-group mt-3">
                                            <label class="form-label col-lg-4 col-md-4 col-4" for="is_sub">Sub Instructor</label>
                                            <input type="checkbox" name="is_sub" id="is_sub" value="1" class="mt-2">
                                            <span id="is_sub_error" class="err"></span>
                                        </div> --}}
                                        <div class="col-md-12 form-group workauth">
                                            <label class="form-label">Please specify work authorization (e.g US Citizen, Green Card etc)</label>
                                            <textarea class="form-control" placeholder="Enter specify" id="specify" name="specify"></textarea>
                                            <span id="specify_error" class="err"></span>
                                        </div>
                                        {{-- <div class="col-md-6 form-group">
                                            <label class="form-label">{{ __('messages.profile_image') }}</label>
                                            <input type="file" name="file_data" id="file_data" accept="image/png, image/jpg, image/jpeg" class="form-control news-file">
                                            <span id="file_data_error" class="err"></span>
                                        </div>
                                        <div class="col-md-12 form-group">
                                            <label class="form-label">Profile Description</label>
                                            <textarea name="about" id="about" placeholder="Enter description" rows="4" class="form-control"></textarea>
                                            <span id="about_error" class="err"></span>
                                        </div> --}}
                                        <div class="col-md-12 d-flex justify-content-md-end justify-content-between mt-3">
                                            <a href="{{redirect()->getUrlGenerator()->previous()}}" class="btn btn-secondary mr-2">{{ __('messages.cancel') }}</a>
                                            <button type="button" id="add_instructor_btn" class="btn btn-primary">{{ __('messages.add') }}</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
        {{-- Add New Instructor Start --}}
    </div>
</main>
{{-- MAIN SECTION END --}}
@endsection
@section('scripts')
<script>
    function changefeild(){
        var profilestatus= $('#profilestatusedit').val();
        if(profilestatus=='16'){
            $('.inpersonrate').css('display','none');
            $('.onlinerate').css('display','block');
            $('#inpersonrate_error').html('');
            $('#inpersonrate').val('');

        }else if(profilestatus=='17'){
            $('.inpersonrate').css('display','block');
            $('.onlinerate').css('display','none');
            $('#onlinerate_error').html('');
            $('#onlinerate').val('');
        }else if(profilestatus=='20'){
            $('.inpersonrate').css('display','block');
            $('.onlinerate').css('display','block');
        }else{
            $('.inpersonrate').css('display','none');
            $('.onlinerate').css('display','none');
        }
    }
</script>
@endsection