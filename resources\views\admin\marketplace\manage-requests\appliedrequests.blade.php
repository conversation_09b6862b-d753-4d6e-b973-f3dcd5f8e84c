@extends('admin.layouts.master')
@section('title')Applied Requests List | Whizara @endsection
@section('content')
<style>
    th{
        text-align: left;
    }
    .truncate-text {
        display: inline-block;
        max-width: 150px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        vertical-align: middle;
        cursor: pointer;
    }
</style>
{{-- MAIN SECTION START --}}
<main class="content">
    <div class="container-fluid p-0">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item " aria-current="page">Manage Marketplace</li>
                <li class="breadcrumb-item " aria-current="page">Applied Requests List</li>
            </ol>
        </nav>

        <div class="table-responsive" id="applied-requests-table">
            <table class="table table-striped admin-dataTable" style="width:100%">
                <thead class="thead-dark">
                    <tr>
                        <th>Requirement</th>
                        <th>Educator Name</th>
                        <th>Educator Email</th>
                        <th>Requirement Start Date</th>
                        <th>Invite ID</th>
                        <th>Offer Description</th>
                        <th>Status</th>
                        <th>Action</th>
                    </tr>
                </thead>
                <tbody>
                    @if(!empty($appliedRequests) && $appliedRequests->count())
                        @foreach ($appliedRequests as $invite)
                            <tr>
                                <td class="text-center"><a href="{{ url('admin/k12connections/view-requirements/'.encrypt_str($invite->requirement->id)) }}">{{ $invite->requirement->id }}</a></td>
                                <td><a href="{{ url('admin/k12connections/manage-educator', $invite->user->id) }}">{{ $invite->user->first_name }} {{ $invite->user->last_name }}</a></td>
                                <td>{{ $invite->user->email }}</td>
                                <td>{{ $invite->requirement->start_date }}</td>
                                <td>{{ $invite->id }}</td>
                                <td>
                                    <span class="truncate-text" title="{{ $invite->offer_description }}">
                                        {{ $invite->offer_description }}
                                    </span>
                                </td>
                                <td>
                                    <select name="status" id="status" class="form-control" data-inviteId="{{ $invite->id }}" data-field="status" onchange="changeInviteStatus('{{ route('admin.marketplace-updateAppliedRequestStatus') }}', '{{ $invite->id }}', this.value)">
                                        <option value="pending" {{ $invite->status == 'pending' ? 'selected' : '' }}>Pending</option>
                                        <option value="accepted" {{ $invite->status == 'accepted' ? 'selected' : '' }}>Accepted</option>
                                        <option value="rejected" {{ $invite->status == 'rejected' ? 'selected' : '' }}>Rejected</option>
                                        <option value="withdraw" {{ $invite->status == 'withdraw' ? 'selected' : '' }}>Withdrawn</option>
                                    </select>
                                </td>
                                <td>
                                    <button class="btn btn-sm btn-primary" @if($invite->status != 'pending') disabled @endif onclick="withdrawInvite('{{ route('admin.marketplace-updateAppliedRequestStatus') }}', '{{ $invite->id }}')">Withdraw</button>
                                </td>
                            </tr>
                        @endforeach
                    @else
                        <tr>
                            <td colspan="10">No data found.</td>
                        </tr>
                    @endif
                </tbody>
            </table>
        </div>
        {{-- END --}}

        {{-- EDIT PROFILE SECTION END --}}
    </div>
</main>
{{-- MAIN SECTION END --}}
@endsection

@section('scripts')
<script>
    // Change Invite Status
    async function changeInviteStatus(url, id, status) {
        try {
            const response = await $.ajax({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                url: url,
                method: "POST",
                data: {
                    invite_id: id,
                    status: status
                }
            });
            if (response) {
                alertify.success(response.message);
            } else {
                alertify.error(response.message);
            }
        } catch (error) {
            alertify.error(error.responseJSON.message);
        }
    }

    // Withdraw Invite
    async function withdrawInvite(url, id) {
        try {
            const response = await $.ajax({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                url: url,
                method: "POST",
                data: {
                    invite_id: id,
                    status: 'withdraw'
                }
            });

            if (response) {
                alertify.success(response.message);
                $(`[data-inviteId="${id}"data-field="status"]`).html('Withdrawn');
            } else {
                alertify.error(response.message);
            }
        } catch (error) {
            alertify.error(error.responseJSON.message);
        }
    }
</script>
@endsection
