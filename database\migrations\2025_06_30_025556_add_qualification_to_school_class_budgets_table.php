<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddQualificationToSchoolClassBudgetsTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('school_class_budgets', function (Blueprint $table) {
            $table->string('qualification')->nullable()->after('tags');
            // You can adjust 'after' to place it where you want in the table.
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('school_class_budgets', function (Blueprint $table) {
            $table->dropColumn('qualification');
        });
    }
}
