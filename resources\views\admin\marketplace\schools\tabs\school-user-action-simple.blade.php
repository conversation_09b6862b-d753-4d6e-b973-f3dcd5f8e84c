{{-- SIMPLE SCHOOL USERS ACTION ACCORDION --}}
<div class="card">
    <div class="card-header">School User Actions</div>
    <div class="card-body">
        @if($schoolUserAction && $schoolUserAction->count() > 0)
            @foreach ($schoolUserAction as $index => $action)
                <div class="card mb-3">
                    <div class="card-header" id="heading{{ $index }}">
                        <h6 class="mb-0">
                            <button class="btn btn-link text-left w-100 text-decoration-none" type="button" data-bs-toggle="collapse" data-bs-target="#collapse{{ $index }}" aria-expanded="false" aria-controls="collapse{{ $index }}">
                                <i class="fas fa-user me-2"></i>
                                {{ $action->user->first_name ?? 'N/A' }} ({{ $action->user->email ?? 'N/A' }})
                                <i class="fas fa-chevron-down float-end mt-1"></i>
                            </button>
                        </h6>
                    </div>
                    <div id="collapse{{ $index }}" class="collapse" aria-labelledby="heading{{ $index }}">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <strong>User Details:</strong>
                                    <ul class="list-unstyled mt-2">
                                        <li><strong>Name:</strong> {{ $action->user->first_name ?? 'N/A' }}</li>
                                        <li><strong>Email:</strong> {{ $action->user->email ?? 'N/A' }}</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <strong>Action Details:</strong>
                                    <ul class="list-unstyled mt-2">
                                        <li><strong>Action:</strong> <span class="badge bg-primary">{{ $action->action->action_key ?? 'N/A' }}</span></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            @endforeach
        @else
            <p class="text-muted">No user actions found.</p>
        @endif
    </div>
</div>

<script>
// Simple accordion toggle
document.addEventListener('DOMContentLoaded', function() {
    const buttons = document.querySelectorAll('[data-bs-toggle="collapse"]');
    buttons.forEach(button => {
        button.addEventListener('click', function() {
            const target = document.querySelector(this.getAttribute('data-bs-target'));
            const icon = this.querySelector('.fa-chevron-down, .fa-chevron-up');
            
            if (target.classList.contains('show')) {
                target.classList.remove('show');
                if (icon) icon.classList.replace('fa-chevron-up', 'fa-chevron-down');
            } else {
                target.classList.add('show');
                if (icon) icon.classList.replace('fa-chevron-down', 'fa-chevron-up');
            }
        });
    });
});
</script>
