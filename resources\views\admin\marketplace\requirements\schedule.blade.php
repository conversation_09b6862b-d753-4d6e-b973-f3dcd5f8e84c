@php
    $daysOfWeek = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
    $scheduleData = [];

    $requirement = $requirement ?? new \stdClass();
    // Defaults when adding new requirement
    $requirement->regular_days = $requirement->regular_days ?? null;
    $requirement->schedule_1_days = $requirement->schedule_1_days ?? null;
    $requirement->schedule_type = $requirement->schedule_type ?? null;
    $requirement->no_class_dates = $requirement->no_class_dates ?? [];

    $regularDays = [];
    if (!empty($requirement->regular_days)) {
        $regularDays = is_string($requirement->regular_days) ? json_decode($requirement->regular_days, true) : $requirement->regular_days;
    }
    $alternatingDays = [];
    if (!empty($requirement->schedule_1_days)) {
        $alternatingDays = is_string($requirement->schedule_1_days) ? json_decode($requirement->schedule_1_days, true) : $requirement->schedule_1_days;
    }

    // Decide which one to use
    $activeScheduleDays = [];
    if (!empty($requirement->schedule_type)) {
        if ($requirement->schedule_type == 'regular') {
            $activeScheduleDays = $regularDays;
        } else {
            $activeScheduleDays = $alternatingDays;
        }
        foreach ($activeScheduleDays as $entry) {
            $scheduleData[$entry['day']][] = $entry;
        }
    }

    $noClassDates = [];
    if (!empty($requirement->no_class_dates)) {
        $noClassDates = is_string($requirement->no_class_dates) ? json_decode($requirement->no_class_dates, true) : $requirement->no_class_dates;
    }
@endphp

<div class="col-md-12 form-group {{ (isset($requirement->schedule_type) && $requirement->schedule_type != 'other') ? '' : 'd-none' }}" id="normal-schedule">
    <div><h5 class="mt-3 mb-3">Class Details</h5></div>

    {{-- No class dates --}}
    @if (!empty($noClassDates))
        @foreach($noClassDates as $index => $date)
        <div id="no-class-dates-wrapper">
            <div class="row mb-2 no-class-dates align-items-end">
                <div class="col-md-5">
                    <label>No Class Start date</label>
                    <input type="text" class="form-control no-class-datepicker" name="no_class_start_date[]" placeholder="MM/DD/YYYY" min="{{ now()->toDateString() }}" value="{{ $date['start_date'] }}">
                </div>
                <div class="col-md-5">
                    <label>No Class End date</label>
                    <input type="text" class="form-control no-class-datepicker" name="no_class_end_date[]" placeholder="MM/DD/YYYY" min="{{ now()->toDateString() }}" value="{{ $date['end_date'] }}">
                </div>
                <div class="col-md-2">
                    <a href="javascript:void(0);" class="btn btn-danger" onclick="removeRequirementNoClassDate(this)">-</a>
                </div>
            </div>
        </div>
        @endforeach
    @else
        <div id="no-class-dates-wrapper">
            <div class="row mb-2 no-class-dates align-items-end">
                <div class="col-md-5">
                    <label>No Class Start date</label>
                    <input type="text" class="form-control no-class-datepicker" name="no_class_start_date[]" placeholder="MM/DD/YYYY" min="{{ now()->toDateString() }}">
                </div>
                <div class="col-md-5">
                    <label>No Class End date</label>
                    <input type="text" class="form-control no-class-datepicker" name="no_class_end_date[]" placeholder="MM/DD/YYYY" min="{{ now()->toDateString() }}">
                </div>
                <div class="col-md-2">
                    <a href="javascript:void(0);" class="btn btn-success" onclick="addRequirementNoClassDate()">+</a>
                </div>
            </div>
        </div>
    @endif
    {{-- No class dates --}}
    <div class="row">
        <div class="col-md-3 form-group mb-0 text-center"><label>Time Slot</label></div>
        <div class="col-md-3 form-group mb-0 text-center"><label>From</label></div>
        <div class="col-md-3 form-group mb-0 text-center"><label>To</label></div>
        <div class="col-md-3 form-group mb-0 text-center"><label>Actions</label></div>
    </div>

    @foreach($daysOfWeek as $day)
        <fieldset id="weekday-{{ $loop->iteration }}">
            <div class="day-block" data-day="{{ strtolower($day) }}">
                @if(isset($scheduleData[$day]))
                    @foreach($scheduleData[$day] as $index => $slot)
                        <div class="row mt-2 time-slot">
                            <div class="col-md-3 form-group">
                                <input type="text" name="{{ strtolower($day) }}[{{ $index }}][day]" value="{{ $day }}" class="form-control" readonly>
                            </div>
                            <div class="col-md-3 form-group">
                                <input type="text" name="{{ strtolower($day) }}[{{ $index }}][start_time]" class="form-control timepicker" placeholder="Start Time" value="{{ $slot['start_time'] }}">
                            </div>
                            <div class="col-md-3 form-group">
                                <input type="text" name="{{ strtolower($day) }}[{{ $index }}][end_time]" class="form-control timepicker" placeholder="End Time" value="{{ $slot['end_time'] }}">
                            </div>
                            <div class="col-md-3 form-group text-center">
                                <button type="button" class="btn btn-sm btn-primary add-time-slot" data-day="{{ strtolower($day) }}">+</button>
                                <button type="button" class="btn btn-sm btn-danger remove-time-slot" data-day="{{ strtolower($day) }}">-</button>
                            </div>
                        </div>
                    @endforeach
                @else
                    {{-- Empty form row for fresh add --}}
                    <div class="row mt-2 time-slot">
                        <div class="col-md-3 form-group">
                            <input type="text" name="{{ strtolower($day) }}[0][day]" value="{{ $day }}" class="form-control" readonly>
                        </div>
                        <div class="col-md-3 form-group">
                            <input type="text" name="{{ strtolower($day) }}[0][start_time]" class="form-control timepicker" placeholder="Start Time">
                        </div>
                        <div class="col-md-3 form-group">
                            <input type="text" name="{{ strtolower($day) }}[0][end_time]" class="form-control timepicker" placeholder="End Time">
                        </div>
                        <div class="col-md-3 form-group text-center">
                            <button type="button" class="btn btn-sm btn-primary add-time-slot" data-day="{{ strtolower($day) }}">+</button>
                            <button type="button" class="btn btn-sm btn-danger remove-time-slot" data-day="{{ strtolower($day) }}">-</button>
                        </div>
                    </div>
                @endif

                <div class="extra-time-slots"></div>
            </div>
        </fieldset>
    @endforeach
</div>
