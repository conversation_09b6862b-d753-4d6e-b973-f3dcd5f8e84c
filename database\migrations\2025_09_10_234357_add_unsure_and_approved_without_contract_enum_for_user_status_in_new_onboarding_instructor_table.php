<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class AddUnsureAndApprovedWithoutContractEnumForUserStatusInNewOnboardingInstructorTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('new_onboarding_instructor', function (Blueprint $table) {
            DB::statement("ALTER TABLE new_onboarding_instructor MODIFY COLUMN user_status ENUM('InProgress','UnderReview','ChangeRequested','Active','Declined','Approved','Withdraw','Unsure','ApprovedWithoutContract') DEFAULT 'InProgress'");
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('new_onboarding_instructor', function (Blueprint $table) {
            DB::statement("ALTER TABLE new_onboarding_instructor MODIFY COLUMN user_status ENUM('InProgress','UnderReview','ChangeRequested','Active','Declined','Approved','Withdraw') DEFAULT 'InProgress'");
        });
    }
}
