<?php

namespace App;
use Illuminate\Database\Eloquent\Model;

class ShortlistInstructorModel extends Model
{
    protected $table = 'school_shortlist_instructor';

    protected $fillable = ['requirement_id', 'school_id', 'user_id', 'status', 'updated_by'];

    public function user()
    {
        return $this->hasOne(OnboardingInstructor::class, 'id','user_id');
    }

    public function requirements()
    {
        return $this->hasMany(NewSchoolPostRequirementModel::class, 'id', 'requirement_id');
    }
}


?>