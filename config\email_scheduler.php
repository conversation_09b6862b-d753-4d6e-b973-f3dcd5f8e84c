<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Predefined Events
    |--------------------------------------------------------------------------
    */
    'events' => [
        'user_created',
        'requirement_created',
        'requirement_invite',
        'requirement_proposal',
        'first_login',
        'class_created',
        'class_completed',
        'payment_received',
        'contract_signed',
    ],

    /*
    |--------------------------------------------------------------------------
    | Recipient Types (Global)
    |--------------------------------------------------------------------------
    */
    'recipient_types' => [
        'all_educator' => 'All Educators',
        'applicant' => 'Applicant related to event',
        'admin' => 'Admin user role (user:super_admin)',
        'all_schools' => 'All Schools',
        'school' => 'School related to event',
        'school_role' => 'School user with specific role (school_role:coordinator)',
    ],

    /*
    |--------------------------------------------------------------------------
    | Models Map with Metadata
    |--------------------------------------------------------------------------
    */
    'models' => [
        'school_user' => [
            'class' => App\Models\v1\SchoolUser::class,
            'recipient_fields' => [
                'email' => 'School User',
            ],
            'condition_fields' => [
                'created_at' => [
                    'label' => 'Created At',
                    'type' => 'date',
                ],
                'status' => [
                    'label' => 'Status',
                    'type' => 'enum',
                    'options' => ['Active', 'In Active'],
                ],
                'must_reset_password' => [
                    'label' => 'Password Reseted',
                    'type' => 'boolean',
                ],
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Default Operators
    |--------------------------------------------------------------------------
    */
    'operators' => [
        '=' => 'Equals',
        '!=' => 'Not Equals',
        '>' => 'Greater than',
        '<' => 'Less than',
        '>=' => 'Greater than or equal',
        '<=' => 'Less than or equal',
        'in' => 'In array / relation',
        'not_in' => 'Not in array / relation',
    ],

    /*
    |--------------------------------------------------------------------------
    | Logical Operators
    |--------------------------------------------------------------------------
    */
    'logical_operators' => [
        'AND' => 'AND',
        'OR' => 'OR',
    ],

    /*
    |--------------------------------------------------------------------------
    | Recurrence Options
    |--------------------------------------------------------------------------
    */
    'recurrence' => [
        'daily' => 'Daily',
        'weekly' => 'Weekly',
        'monthly' => 'Monthly',
        'yearly' => 'Yearly',
    ],

];
