<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class ModifyUpdatedByColumnInSchoolShortlistInstructorTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Change column type to string (VARCHAR 255 by default)
        DB::statement("ALTER TABLE school_shortlist_instructor MODIFY COLUMN updated_by VARCHAR(255) NULL");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // Revert back to unsignedBigInteger
        DB::statement("ALTER TABLE school_shortlist_instructor MODIFY COLUMN updated_by BIGINT UNSIGNED NULL");
    }
}