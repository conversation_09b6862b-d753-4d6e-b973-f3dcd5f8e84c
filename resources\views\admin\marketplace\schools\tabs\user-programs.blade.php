{{-- User Programs Table --}}
<div class="card">
    <div class="card-header">User Programs</div>
    <div class="card-body p-0">
        @if($programs->count() > 0)
        <div class="table-responsive">
            <table class="table table-striped table-bordered mb-0">
                <thead class="thead-dark">
                    <tr>
                        <th scope="col" style="text-align: left;">Program Name</th>
                        <th scope="col" style="text-align: left;">Status</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($programs as $program)
                        <tr>
                            <td>{{ $program->name }}</td>
                            <td>{{ $program->status }}</td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
        @else
            <p class="text-muted m-3">No programs added yet.</p>
        @endif
    </div>
</div>
@section('scripts')
@endsection