<?php

namespace App;
use App\Classes;

use Illuminate\Database\Eloquent\Model;

class InstructorThirdStepOnboardingModel extends Model
{
    protected $table = 'onboarding_instructor_teaching_preferences';

    protected $appends = ['grade_level_names'];
    public function getGradeLevelNamesAttribute()
    {
        $ids = explode(',', $this->i_prefer_to_teach);
        return Classes::whereIn('id', $ids)->pluck('class_name')->toArray();
    }
    public function subjects()
    {
        return $this->hasMany(InstructorSubjectsThirdStepOnboardingModel::class, 'step_id', 'id');
    }
}
