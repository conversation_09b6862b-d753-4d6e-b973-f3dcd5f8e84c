<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class AlterFileUrlColumnAsNullableInSchoolRequirementContractVersionsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('school_requirement_contract_versions', function (Blueprint $table) {
            DB::statement("ALTER TABLE school_requirement_contract_versions MODIFY file_url VARCHAR(255) NULL");
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('school_requirement_contract_versions', function (Blueprint $table) {
            DB::statement("ALTER TABLE school_requirement_contract_versions MODIFY file_url VARCHAR(255) NOT NULL");
        });
    }
}
