@extends('admin.layouts.master')

@section('title') View Resource | Whizara @endsection

@section('content')
<style>
    .card .card-body.signature {
        height: 450px;
        overflow-y: scroll;
        width: 90%;
        margin: 0 auto;
    }
    .profile_img_box {
        border: 2px solid #e9ecef;
        border-radius: 10px;
        padding: 15px;
        background: #f8f9fa;
    }
    .profile_img_box img {
        border-radius: 8px;
        max-height: 200px;
        object-fit: cover;
    }
    .info-card {
        background: #fff;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    .info-label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 5px;
    }
    .info-value {
        color: #6c757d;
        margin-bottom: 15px;
    }
    .tag-badge {
        display: inline-block;
        background: #007bff;
        color: white;
        padding: 4px 12px;
        border-radius: 15px;
        font-size: 12px;
        margin: 2px;
    }
    .status-active {
        background: #28a745;
        color: white;
        padding: 4px 12px;
        border-radius: 15px;
        font-size: 12px;
    }
    .status-inactive {
        background: #dc3545;
        color: white;
        padding: 4px 12px;
        border-radius: 15px;
        font-size: 12px;
    }
</style>

<main class="content">
    <div class="container-fluid p-0">
        <!-- BREADCRUMB START -->
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ url('admin-dashboard') }}" class="text-primary">{{ __('messages.dashboard') }}</a></li>
                <li class="breadcrumb-item"><a href="{{ route('admin.manage-resources.index') }}" class="text-primary">Manage Resources</a></li>
                <li class="breadcrumb-item active" aria-current="page">View Resource</li>
            </ol>
        </nav>
        <!-- BREADCRUMB END -->

        <!-- PROFILE DETAILS SECTION START -->
        <div class="row justify-content-center">
            @if($resource->image)
            <div class="col-lg-2 col-md-3 col-6">
                <div class="profile_img_box text-center mb-3">
                    <img src="{{ generateSignedUrl($resource->image) }}" alt="Resource Image" class="img-fluid">
                </div>
            </div>
            @endif
            
            <div class="col-lg-{{ $resource->image ? '10' : '12' }} col-md-{{ $resource->image ? '9' : '12' }} col-{{ $resource->image ? '6' : '12' }}">
                <div class="info-card">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="info-label">Title</div>
                            <div class="info-value">{{ $resource->title }}</div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-label">Status</div>
                            <div class="info-value">
                                <span class="{{ $resource->status == 1 ? 'status-active' : 'status-inactive' }}">
                                    {{ $resource->status == 1 ? 'Active' : 'Inactive' }}
                                </span>
                            </div>
                        </div>
                    </div>
                    
                    @if($resource->image_caption)
                    <div class="row">
                        <div class="col-md-12">
                            <div class="info-label">Image Caption</div>
                            <div class="info-value">{{ $resource->image_caption }}</div>
                        </div>
                    </div>
                    @endif
                    
                    <div class="row">
                        <div class="col-md-12">
                            <div class="info-label">Tags</div>
                            <div class="info-value">
                                @if($resource->tags)
                                    @php
                                        $tags = json_decode($resource->tags, true);
                                        if (!is_array($tags)) {
                                            $tags = [];
                                        }
                                    @endphp
                                    @if(count($tags) > 0)
                                        @foreach($tags as $tag)
                                            <span class="tag-badge">{{ $tag }}</span>
                                        @endforeach
                                    @else
                                        <span class="text-muted">No tags assigned</span>
                                    @endif
                                @else
                                    <span class="text-muted">No tags assigned</span>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- PROFILE DETAILS SECTION END -->

        <!-- CONTENT SECTIONS START -->
        <div class="row">
            @if($resource->summary)
            <div class="col-lg-6 col-md-12">
                <div class="info-card">
                    <h5 class="mb-3">Summary</h5>
                    <div class="card-body signature">
                        {!! $resource->summary !!}
                    </div>
                </div>
            </div>
            @endif
            
            @if($resource->content)
            <div class="col-lg-{{ $resource->summary ? '6' : '12' }} col-md-12">
                <div class="info-card">
                    <h5 class="mb-3">Content</h5>
                    <div class="card-body signature">
                        {!! $resource->content !!}
                    </div>
                </div>
            </div>
            @endif
        </div>
        <!-- CONTENT SECTIONS END -->

        <!-- METADATA SECTION START -->
        <div class="row">
            <div class="col-lg-12">
                <div class="info-card">
                    <h5 class="mb-3">Resource Information</h5>
                    <div class="row">
                        <div class="col-md-3">
                            <div class="info-label">Created By</div>
                            <div class="info-value">
                                {{ optional($resource->createdBy)->first_name ?? 'Unknown' }} 
                                {{ optional($resource->createdBy)->last_name ?? '' }}
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-label">Created At</div>
                            <div class="info-value">{{ $resource->created_at->format('M d, Y h:i A') }}</div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-label">Updated By</div>
                            <div class="info-value">
                                {{ optional($resource->updatedBy)->first_name ?? 'Unknown' }} 
                                {{ optional($resource->updatedBy)->last_name ?? '' }}
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-label">Updated At</div>
                            <div class="info-value">{{ $resource->updated_at->format('M d, Y h:i A') }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- METADATA SECTION END -->

        <!-- ACTION BUTTONS START -->
        <div class="row">
            <div class="col-lg-12">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <a href="{{ route('admin.manage-resources.index') }}" class="btn btn-secondary">
                        <i class="fa fa-arrow-left"></i> Back to List
                    </a>
                    <div>
                        <a href="{{ route('admin.manage-resources.edit', ['manage_resource' => $resource->id]) }}" class="btn btn-primary">
                            <i class="fa fa-edit"></i> Edit Resource
                        </a>
                        <a class="btn btn-danger btn-delete" href="javascript:void(0);" data-id="{{ $resource->id }}">
                            <i class="fa fa-trash"></i> Delete Resource
                        </a>
                    </div>
                </div>
            </div>
        </div>
        <!-- ACTION BUTTONS END -->
    </div>
</main>
@endsection

@section('scripts')
<script src="{{asset('js/sweetalert2.all.min.js')}}"></script>
<script>
$(document).ready(function() {
    // Delete functionality
    $('.btn-delete').on('click', function(e) {
        e.preventDefault();
        const id = $(this).data('id');
        
        swal({
            title: "Delete?",
            text: "Please ensure and then confirm!",
            type: "warning",
            showCancelButton: true,
            confirmButtonText: "Yes, delete it!",
            cancelButtonText: "No, cancel!",
            reverseButtons: true
        }).then(function (result) {
            if (result.value === true) {
                $.ajaxSetup({
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    }
                });
                
                $.ajax({
                    url: "{{ route('admin.manage-resources.destroy', ['manage_resource' => 'ID']) }}".replace('ID', id),
                    method: 'POST',
                    data: {
                        _token: "{{ csrf_token() }}",
                        _method: 'DELETE'
                    }
                }).done(function(res) {
                    if (res.success) {
                        swal("Deleted!", res.message || "Resource has been deleted successfully.", "success");
                        setTimeout(function() {
                            window.location.href = "{{ route('admin.manage-resources.index') }}";
                        }, 1500);
                    } else {
                        swal("Error!", res.message || "Something went wrong.", "error");
                    }
                }).fail(function() {
                    swal("Error!", "Failed to delete resource.", "error");
                });
            }
        });
    });
});
</script>
@endsection
