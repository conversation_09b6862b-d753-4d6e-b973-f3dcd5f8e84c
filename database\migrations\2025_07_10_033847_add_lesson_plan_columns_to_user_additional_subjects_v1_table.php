<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddLessonPlanColumnsToUserAdditionalSubjectsV1Table extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('user_additional_subjects_v1', function (Blueprint $table) {
            $table->longText('lesson_plan_note')->nullable()->after('proficiency')->comment('Note for the uploaded lesson plan');
            $table->string('file_name')->nullable()->after('lesson_plan_note')->comment('Original name of the uploaded file');
            $table->string('file_url')->nullable()->after('file_name')->comment('URL path of the uploaded file');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('user_additional_subjects_v1', function (Blueprint $table) {
            $table->dropColumn(['lesson_plan_note', 'file_name', 'file_url']);
        });
    }
}
