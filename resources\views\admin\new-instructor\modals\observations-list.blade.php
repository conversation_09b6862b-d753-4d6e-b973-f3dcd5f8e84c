<div class="modal-content">
    <div class="modal-body">
        {{-- Add Note Form --}}
        <form id="observationNoteForm">
            @csrf
            <input type="hidden" name="applicant_id" id="applicantId" value="{{ $applicantId ?? '' }}">
            <div class="mb-3">
                <label for="note" class="form-label">Add Observation Note</label>
                <textarea class="form-control" name="note" id="note" rows="3" required></textarea>
            </div>
            <button type="submit" class="btn btn-primary btn-sm mb-3">Save Note</button>
        </form>

        {{-- Notes Table --}}
        <div class="table-responsive">
            <table id="observationNotesTable" class="table table-striped admin-dataTable" style="width:100%">
                <thead>
                    <tr>
                        <th class="text-left">Id</th>
                        <th class="text-left">Note</th>
                        <th class="text-left">Status</th>
                        <th class="text-left">Created By</th>
                        <th class="text-left">Created At</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse ($observationNotes as $note)
                        <tr>
                            <td>{{ $note->id }}</td>
                            <td>{{ $note->note }}</td>
                            <td>{{ $note->status }}</td>
                            <td>{{ $note->createdBy?->first_name }} {{ $note->createdBy?->last_name }}</td>
                            <td>{{ $note->created_at }}</td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="5" class="text-center">No observation notes found.</td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
    </div>
</div>

<script>
    // Store Observation Note
    $("#observationNoteForm").submit(function (e) {
        e.preventDefault();
        let formData = $(this).serialize();
        $.ajax({
            url: "/admin/k12connections/store-observation-note",
            type: "POST",
            data: formData,
            success: function (response) {
                if (response.status) {
                    alertify.success(response.message);
                    // Reset form
                    $("#observationNoteForm")[0].reset();
                    $("#observationNotesTable tbody tr td[colspan='5']").closest("tr").remove();
                    // Append new row manually (using response data)
                    let newRow = `
                        <tr>
                            <td>${response.data.id}</td>
                            <td>${response.data.note}</td>
                            <td>${response.data.status ?? ''}</td>
                            <td>${response.data.created_by_name ?? ''}</td>
                            <td>${response.data.created_at}</td>
                        </tr>
                    `;
                    $("#observationNotesTable tbody").prepend(newRow); // add to top
                } else {
                    alertify.error(response.message);
                }
            },
            error: function () {
                alertify.error("Failed to add observation note. Please try again.");
            }
        });
    });
</script>