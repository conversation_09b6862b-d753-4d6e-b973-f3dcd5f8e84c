<section class="p-2">
    <div class="d-flex justify-content-between align-items-center">
        <h5 class="mb-0 px-3"> {{ $is_main_invite ? 'Main Instructor Invite' : ($is_stand_by_invite ? 'Standby Instructor Invite' : 'Sub Instructor Invite') }} </h5>
        <button type="button" class="btn border-0 bg-transparent fa-2x" id="close-invite-dialog" data-dismiss="modal" aria-label="Close">×</button>
    </div>

    <div class="card-header border-bottom">
        <h5 class="mb-0">Program Details</h5>
        <div class="card-body p-0">
            <ul class="list-group list-group-flush">
                <li class="list-group-item d-flex">
                    <span class="col-lg-3 col-md-3 col-4 pl-0">Grade Level :</span>
                    <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0">{{ implode(',', $requirement->grade_level_names) }}
                    </h6>

                    <span class="col-lg-3 col-md-3 col-4 pl-0">Format:</span>
                    <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0">{{ $requirement->delivery_mode }}</h6>
                </li>
                <li class="list-group-item d-flex">
                    <span class="col-lg-3 col-md-3 col-4 pl-0">Subject Area:</span>
                    <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0"> {{ $requirement->subject->subjectArea->subject_area ?? '' }}
                    </h6>

                    <span class="col-lg-3 col-md-3 col-4 pl-0">Subject:</span>
                    <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0">{{ $requirement->subject->title ?? '' }}</h6>
                </li>
                <li class="list-group-item d-flex">
                    @if($requirement->delivery_mode != 'online')
                    <span class="col-lg-3 col-md-3 col-4 pl-0">Location :</span>
                    <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0">{{ $requirement->address }}
                    </h6>
                    @endif

                    <span class="col pl-0"> Schedule Date and Time :</span>
                    <h6 class="col pr-0 mb-0"> {{ date('m-d-Y', strtotime(@$requirement->start_date)) }} - {{ date('m-d-Y', strtotime(@$requirement->end_date)) }}
                    @foreach($requirement->detailed_schedule as $day => $slots)
                            <br>
                            <strong>{{ $day }}:</strong>
                            @foreach($slots as $slot)
                                {{ $slot['start_time'] }} - {{ $slot['end_time'] }}@if(!$loop->last), @endif
                            @endforeach
                    @endforeach
                    </h6>
                </li>
                <li class="list-group-item">
                    <div class="row">
                        <!-- Deadline Date -->
                        <div class="col-lg-4 col-md-4 col-12">
                            <label for="deadline-d" class="form-label">Deadline Date</label>
                            <input type="text" name="deadline-date" id="deadline-date" class="form-control deadline-datepicker">
                        </div>

                        <!-- Deadline Time -->
                        <div class="col-lg-4 col-md-4 col-12">
                            <label for="deadline-time" class="form-label">Deadline Time</label>
                            <input type="text" name="deadline-time" id="deadline-time" class="form-control deadline-time-picker">
                        </div>

                        <!-- Time Zone -->
                        <div class="col-lg-4 col-md-4 col-12">
                            <label for="timezone" class="form-label">Time Zone</label>
                            @php
                                $timezone = $requirement->timezone->name;

                                // Replace only the 2nd "/"
                                $pos = strpos($timezone, '/', strpos($timezone, '/') + 1);
                                if ($pos !== false) {
                                    $timezone = substr_replace($timezone, ' ', $pos, 1);
                                }


                                // Replace "_" with " "
                                $timezone = str_replace('_', ' ', $timezone);
                            @endphp
                            <input type="text" name="timezone" id="timezone" class="form-control" value="{{ $timezone }}" readonly>
                        </div>
                        <input
                            name="type"
                            id="type"
                            hidden
                            @if($is_main_invite) value="main" @endif
                            @if($is_stand_by_invite) value="stand_by" @endif
                            @if($is_sub_invite) value="sub" @endif
                            @if($is_replacement_invite) value="replacement" @endif
                        >
                    </div>
                </li>
            </ul>



            {{-- Invitation History  --}}

            <div class="accordion my-3" id="invitesAccordion">
                <div class="accordion-item">
                    <span class="accordion-header" id="headingInvites">
                        <a class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                            data-bs-target="#collapseInvites" aria-expanded="false" aria-controls="collapseInvites">
                            Invitation History
                            @if($requirement->invites->count() > 0)
                                <span class="ms-2">({{ $requirement->invites->count() }})</span>
                            @endif
                        </a>
                    </span>
                    <div id="collapseInvites" class="accordion-collapse collapse" aria-labelledby="headingInvites"
                        data-bs-parent="#invitesAccordion">
                        <div class="accordion-body p-0">
                            @if($requirement->invites->count() > 0)
                                <div class="table-responsive">
                                    <table class="table table-striped table-bordered mb-0">
                                        <thead class="table-light">
                                            <tr>
                                                <th>Educator</th>
                                                <th>Email</th>
                                                <th>Deadline</th>
                                                <th>Status</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($requirement->invites as $invite)
                                                @php
                                                    $deadline = \Carbon\Carbon::parse($invite->deadline_date . ' ' . $invite->deadline_time);
                                                    $now = now();
                                                    $status = $invite->status ?? 'pending';
                                                    if ($deadline->isPast() && $status === 'pending') {
                                                        $status = 'expired';
                                                    }
                                                    if ($invite->status === 'accepted') {
                                                        $badgeClass = 'bg-success';
                                                    } elseif ($invite->status === 'rejected') {
                                                        $badgeClass = 'bg-danger';
                                                    } elseif ($invite->status === 'expired') {
                                                        $badgeClass = 'bg-secondary';
                                                    } elseif ($invite->status === 'pending') {
                                                        $badgeClass = 'bg-warning';
                                                    } else {
                                                        $badgeClass = 'bg-info';
                                                    }
                                                @endphp
                                                <tr>
                                                    <td><strong>{{ ($invite->educator->first_name . ' ' . $invite->educator->last_name) ?? 'Unknown' }}</strong></td>
                                                    <td><a href="#" onclick="$('#educator-search').val('{{ $invite->educator->email ?? 'Unknown' }}'); window.educatorDataTable.draw()">{{ $invite->educator->email ?? 'Unknown' }}</a></td>
                                                    <td>{{ \Carbon\Carbon::parse($invite->deadline_date)->format('m-d-Y') }} {{ \Carbon\Carbon::parse($invite->deadline_time)->format('h:i A') }}</td>
                                                    <td><span class="badge {{ $badgeClass }}">{{ ucfirst($status) }}</span></td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            @else
                                <p class="m-3 text-muted">No invitation history.</p>
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            <form id="instructor-filter-form">
                <input type="hidden" name="requirement_id" value="{{ $requirement->id }}">
                <div class="col">
                    <label class="form-label d-block">Filters </label>
                    <div class="custom-control custom-checkbox custom-control-inline mb-3">
                        <input type="checkbox" class="custom-control-input" id="Grade" name="Grade" onclick="educatorInviteFilter('Grade');" value="Grade" checked>
                        <label class="custom-control-label" for="Grade">Grade Level</label>
                    </div>
                    <div class="custom-control custom-checkbox custom-control-inline mb-3">
                        <input type="checkbox" class="custom-control-input" id="Format" name="Format" value="Format" onclick="educatorInviteFilter('Format');" checked>
                        <label class="custom-control-label" for="Format">Format</label>
                    </div>
                    <div class="custom-control custom-checkbox custom-control-inline mb-3" >
                        <input type="checkbox" class="custom-control-input" id="Subject" name="Subject" value="Subject" onclick="educatorInviteFilter('Subject');" checked>
                        <label class="custom-control-label" for="Subject">Subject</label>
                    </div>
                    @if($requirement->delivery_mode !== 'online')
                    <div class="custom-control custom-checkbox custom-control-inline mb-3">
                        <input type="checkbox" class="custom-control-input" id="Location" name="Location" onclick="educatorInviteFilter('Location');" value="Location" checked>
                        <label class="custom-control-label" for="Location">Location</label>
                    </div>
                    @endif

                    <div class="custom-control custom-checkbox custom-control-inline mb-3">
                        <input type="checkbox" class="custom-control-input" id="Availability" name="Availability" onclick="educatorInviteFilter('Availability');" value="Availability" checked>
                        <label class="custom-control-label" for="Availability">Availability</label>
                    </div>
                    <div class="custom-control custom-checkbox custom-control-inline mb-3">
                        <input type="checkbox" class="custom-control-input" id="conflicting" name="conflicting" onclick="educatorInviteFilter('conflicting');" value="conflicting">
                        <label class="custom-control-label" for="conflicting">Conflicting Scheduled</label>
                    </div>
                    <div class="custom-control custom-checkbox custom-control-inline mb-3" >
                        <input type="checkbox" class="custom-control-input" id="Sub" name="Sub" value="Sub" onclick="educatorInviteFilter('Sub');" @if($is_sub_invite) checked @endif>
                        <label class="custom-control-label" for="Sub">Sub Instructors</label>
                    </div>
                    {{-- @if($requirement->credential_check === 'credentialed')
                    <div class="custom-control custom-checkbox custom-control-inline mb-3" >
                        <input type="checkbox" class="custom-control-input" id="Credentialed" name="Credentialed" value="Credentialed" onclick="educatorInviteFilter('Credentialed');" checked >
                        <label class="custom-control-label" for="Credentialed">Credentialed Instructors</label>
                    </div>
                    @endif
                    @if($requirement->special_education_certificate === 'yes')
                    <div class="custom-control custom-checkbox custom-control-inline mb-3" >
                        <input type="checkbox" class="custom-control-input" id="Special-Education" name="Special-Education" value="Special-Education" onclick="educatorInviteFilter('Special-Education');" checked>
                        <label class="custom-control-label" for="Special-Education">Special Education</label>
                    </div>
                    @endif --}}


                    @if ($requirement->background_checks)
                    <div class="custom-control custom-checkbox custom-control-inline mb-3">
                        <input type="checkbox" class="custom-control-input" id="backgroundcheck" name="backgroundcheck"
                            value="backgroundcheck" onclick="educatorInviteFilter('backgroundcheck');">
                        <label class="custom-control-label" for="backgroundcheck">Background
                            Checks</label>
                    </div>
                   @endif
                </div>
        </form>
            <!-- Search Input -->
            <div class="row mb-3">
                <div class="col-md-6">
                    <label for="educator-search" class="form-label">Search Educators</label>
                    <input type="text" id="educator-search" class="form-control" placeholder="Search by name, email, or location...">
                </div>
                <div class="col-md-6 d-flex align-items-end">
                    <button type="button" class="btn btn-secondary" onclick="clearAllFilters()">Clear All Filters</button>
                </div>
            </div>

            <div class="table-responsive">
                <table id="dataTable2" class="table table-striped" style="width:100%">
                    <thead class="thead-dark">
                        <tr>
                            <th><input type="checkbox" id="select-all"></th>
                            <th>Name</th>
                            <th>Email</th>
                            @if($requirement->delivery_mode !== 'online')
                                <th>Distance</th>
                            @endif
                            <th>Grade</th>
                            <th>Availability</th>
                            <th>Programs</th>
                            <th>Format</th>
                            <th>Cost</th>
                        </tr>
                    </thead>
                    <tbody>

                    </tbody>
                </table>
            </div>

            <div class="row">
                <div class="col-md-12 d-flex justify-content-center">
                    <button type="button" class="btn btn-primary" onclick="sendInvite()">Send Invite</button>
                </div>
            </div>
        </div>
    </div>
</section>

<script>
$(".deadline-datepicker").each(function () {
    var $datepicker = $(this);
    $datepicker.attr("placeholder", "MM/DD/YYYY");
    $datepicker.datepicker({
        format: "mm/dd/yyyy",
        autoclose: true,
        startDate: new Date()
    }).on('keypress paste', function (e) {
        e.preventDefault();
        return false;
    });
});

$(".deadline-time-picker").each(function () {
    var $timepicker = $(this);
    $timepicker.attr("placeholder", "HH:MM AM/PM");
    $('.deadline-time-picker').timepicker({
        'placeholder': 'h:i A',
        'timeFormat': 'h:i A',
        'step': 15,
        'listWidth': 1,
    }).attr('placeholder', 'HH:MM AM/PM');
});

$(document).ready(function () {
    // Destroy existing DataTable if it exists
    // Handle select all
    $('#select-all').on('click', function() {
        var checked = this.checked;
        $('.row-select').each(function() {
            this.checked = checked;
        });
    });

    if ($.fn.DataTable.isDataTable("#dataTable2")) {
        $("#dataTable2").DataTable().destroy();
        $("#dataTable2").empty();
    }

    // Initialize DataTable with custom configuration
    window.educatorDataTable = $('#dataTable2').DataTable({
        'processing': true,
        'serverSide': true,
        'serverMethod': 'get',
        'autoWidth': true,
        'searching': false, // We'll use custom search
        'ordering': true,
        "lengthMenu": [[5, 10, 25, 50], [5, 10, 25, 50]],
        'drawCallback': function (settings) {
            var table = $(this).DataTable();
            var showPagination = table.page.info().recordsTotal > 5;
            $('#dataTable2_paginate').toggle(showPagination);
        },
        'ajax': {
            'headers': {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            'url': '/admin/k12connections/requirements/invite-search',
            'data': function (data) {
                // Add filter form data
                var filterData = $('#instructor-filter-form').serializeArray();
                $.each(filterData, function (index, item) {
                    data[item.name] = item.value;
                });

                // Add custom search
                var searchValue = $('#educator-search').val();
                if (searchValue) {
                    data.search = { value: searchValue };
                }
            },
            'error': function(xhr, error, thrown) {
                console.error('DataTable AJAX error:', error);
                alertify.error('Error loading educator data. Please try again.');
            }
        },
        'columns': [
            {
                data: 'id',
                orderable: false,
                searchable: false,
                render: function (data, type, row) {
                    return '<input type="checkbox" class="row-select" value="'+ data +'">';
                }
            },
            { data: 'name' },
            { data: 'email' },
            @if($requirement->delivery_mode !== 'online')
                { data: 'distance' },
            @endif
            { data: 'grade' },
            { data: 'availability' },
            { data: 'programs' },
            { data: 'format' },
            { data: 'cost' }
        ],
        'order': [[0, 'asc']]
    });

    // Custom search functionality
    $('#educator-search').on('keyup', function() {
        window.educatorDataTable.draw();
    });

    // Trigger initial load
    educatorInviteFilter('initial');
});

// Function to clear all filters
function clearAllFilters() {
    $('#educator-search').val('');
    $('#instructor-filter-form input[type="checkbox"]').each(function() {
        var defaultChecked = ['Grade', 'Format', 'Subject', 'Location', 'Availability', 'conflicting', 'Sub'];
        if (defaultChecked.includes($(this).val())) {
            $(this).prop('checked', false);
        } else {
            $(this).prop('checked', true);
        }
    });
    window.educatorDataTable.draw();
}


function educatorInviteFilter(filterType) {
    if (filterType === 'initial') {
        // Trigger initial load
        window.educatorDataTable.draw();
    } else {
        // Redraw the table with updated filters
        window.educatorDataTable.draw();
    }
}

function sendInvite() {
    var selectedIds = [];
    $('.row-select:checked').each(function() {
        selectedIds.push($(this).val());
    });

    if (selectedIds.length === 0) {
        alertify.error("Please select at least one educator.");
        return;
    }

    var requirementId = {{ $requirement->id }};
    var deadlineDate = $('#deadline-date').val();
    var deadlineTime = $('#deadline-time').val();
    var timezone = $('#timezone').val();
    var type = $('input#type').val();
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    $.ajax({
        url: "{{ route('admin.marketplace-sendInvite') }}",
        type: "POST",
        data: {
            educator_ids: selectedIds, // 🔹 send as array
            requirement_id: requirementId,
            deadline_date: deadlineDate,
            deadline_time: deadlineTime,
            timezone: timezone,
            type: type
        },
        dataType: "json",
        success: function (response) {
            if (response.success) {
                alertify.success(response.message);
                $('#close-invite-dialog').click();
                window.educatorDataTable.destroy();
                delete window.educatorDataTable;
            } else {
                alertify.error(response.message);
            }
        },
        error: function (xhr, status, error) {
            alertify.error(error);
        }
    });
}

</script>