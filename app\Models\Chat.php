<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Chat extends Model
{
    use SoftDeletes;

    protected $table = 'messages';
    protected $keyType = 'string';
    public $incrementing = false;

    protected $fillable = [
        'sender_id',
        'sender_type',
        'recipient_id',
        'recipient_type',
        'content',
        'message_type',
        'reference_id',
        'parent_id',
        'status',
        'delivered_at',
        'read_at',
    ];

    protected $dates = [
        'delivered_at',
        'read_at',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    /**
     * Attachments related to the message
     */
    public function attachments(): BelongsToMany
    {
        return $this->belongsToMany(Attachment::class, 'message_attachments', 'message_id', 'attachment_id');
    }

    /**
     * Parent message (for threads/replies)
     */
    public function parent()
    {
        return $this->belongsTo(Chat::class, 'parent_id');
    }

    /**
     * Child messages (replies)
     */
    public function children()
    {
        return $this->hasMany(Chat::class, 'parent_id');
    }

    // Relationship with PlatformSchoolRequirements
    public function getSenderUserAttribute()
    {
        return $this->resolveUser($this->sender_type, $this->sender_id);
    }

    public function getRecipientUserAttribute()
    {
        return $this->resolveUser($this->recipient_type, $this->recipient_id);
    }
    private function resolveUser($role, $id)
    {
        switch ($role) {
            case 'admin':
                return \App\User::find($id);
            case 'educator':
                return \App\OnboardingInstructor::with('step5')->find($id);
            case 'school':
                return \App\Models\v1\SchoolUser::find($id);
            default:
                return null;
        }
    }
    public function requirement()
    {
        return $this->belongsTo(PlatformSchoolRequirements::class, 'referance_id');
    }
}

