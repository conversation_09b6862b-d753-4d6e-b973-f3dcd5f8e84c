@extends('admin.layouts.master')

@section('title', 'Edit Email Layout | Whizara')

@section('content')
<div class="container-fluid">
    <h3 class="mb-4">Edit Layout - {{ $layout->title }}</h3>

    <form action="{{ route('admin.email-template.layout.update', $layout->id) }}" method="POST">
        @csrf

        <div class="form-group">
            <label>Title</label>
            <input type="text" name="title" value="{{ old('title', $layout->title) }}" class="form-control">
        </div>

        <div class="form-group">
            <label>Slug</label>
            <input type="text" name="slug" readonly value="{{ old('slug', $layout->slug) }}" class="form-control">
        </div>

        <div class="form-group">
            <label>Tags</label>
            <div id="tags-container" style="cursor: text; min-height: 45px;">
                <div class="chips d-flex mb-2" style="gap: 3px" id="tag-chips">
                    @if(is_array($layout->tags))
                    @foreach($layout->tags as $tag)
                    <span class="badge bg-primary me-1 mb-1">
                        {{$tag}}
                        <span class="p-1 text-white cursor-pointer remove-tag" style="opacity: 0.7" aria-label="Remove" aria-hidden="true" onclick="($(this).parent().remove())">x</span>
                        <input type="hidden" name="tags[]" value="{{$tag}}">
                    </span>
                    @endforeach
                    @endif
                </div>
                <input type="text" id="tag-input" class="form-control flex-grow-1" placeholder="Type and press Enter">
            </div>
        </div>
        <div class="form-group">
        </div>

        <div class="form-group">
            <label>Header HTML</label>
            <textarea name="header_html" rows="4" class="form-control">{{ old('header_html', $layout->header_html) }}</textarea>
        </div>

        <div class="form-group">
            <label>Footer HTML</label>
            <textarea name="footer_html" rows="4" class="form-control">{{ old('footer_html', $layout->footer_html) }}</textarea>
        </div>

        <button type="submit" class="btn btn-success">Save Layout</button>
    </form>
</div>
@endsection

@push('scripts')
<script>
    function createTag(label) {
        if (!label) return;

        // prevent duplicates
        let exists = false;
        $("#tags-container input[name='tags[]']").each(function() {
            if ($(this).val().toLowerCase() === label.toLowerCase()) {
                exists = true;
                return false;
            }
        });
        if (exists) return;

        const $tag = $(`
            <span class="badge bg-primary me-1 mb-1">
                ${label}
                <span class="p-1 text-white cursor-pointer remove-tag" style="opacity: 0.7" aria-label="Remove" aria-hidden="true">x</span>
                <input type="hidden" name="tags[]" value="${label}">
            </span>
        `);

        $tag.find(".remove-tag").on("click", function () {
            $tag.remove();
        });

        $("#tag-chips").append($tag);
    }

    document.addEventListener("DOMContentLoaded", function() {
        CKEDITOR.replace('header_html');
        CKEDITOR.replace('footer_html');
        $("#tag-input").on("keydown", function (e) {
            const value = $(this).val().trim();

            if ((e.key === "Enter" || e.key === ",") && value !== "") {
                e.preventDefault();
                createTag(value);
                $(this).val("");
            }

            if (e.key === "Backspace" && value === "") {
                const $lastTag = $("#tags-container .badge").last();
                if ($lastTag.length) $lastTag.remove();
            }
        });

        $("#tags-container").on("click", function () {
            $("#tag-input").focus();
        });
    })
</script>