<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSchoolsBudgetV2Table extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('school_budget_v2', function (Blueprint $table) {
            $table->id();

            $table->string('name')->nullable()->comment('Optional label for this global budget configuration');

            // All decimal fields default to 0.00
            $table->decimal('in_person', 10, 2)->default(0)->comment('Pay increment for in-person teaching');
            $table->decimal('case_management', 10, 2)->default(0)->comment('Additional pay for case management duties');
            $table->decimal('bilingual_inc', 10, 2)->default(0)->comment('Bilingual increment');
            $table->decimal('sped_rec_comp', 10, 2)->default(0)->comment('Special education recruitment compensation');

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('school_budget_v2');
    }
}
