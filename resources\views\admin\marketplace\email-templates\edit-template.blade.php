@extends('admin.layouts.master')

@section('title', 'Edit Email Template | Whizara')

@section('content')
<div class="container-fluid">
    <h3 class="mb-4">Edit Template - {{ $template->title }}</h3>

    <ul class="nav nav-tabs mb-3" id="templateTabs" role="tablist">
        <li class="nav-item" role="presentation">
            <button class="nav-link active" id="edit-tab" data-bs-toggle="tab" data-bs-target="#edit" type="button" role="tab">Edit</button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="preview-tab" data-bs-toggle="tab" data-bs-target="#preview" type="button" role="tab">Live Preview</button>
        </li>
    </ul>

    <div class="tab-content" id="templateTabsContent">
        {{-- Edit Form --}}
        <div class="tab-pane fade show active" id="edit" role="tabpanel" aria-labelledby="edit-tab">
            <form action="{{ route('admin.email-template.template.update', $template->id) }}" method="POST">
                @csrf

                <div class="form-group mb-3">
                    <label>Title</label>
                    <input type="text" name="title" id="title" value="{{ old('title', $template->title) }}" class="form-control">
                </div>

                <div class="form-group mb-3">
                    <label>Slug</label>
                    <input type="text" name="slug" id="slug" readonly value="{{ old('slug', $template->slug) }}" class="form-control">
                </div>

                <div class="form-group mb-3">
                    <label>Subject</label>
                    <input type="text" name="subject" id="subject" value="{{ old('subject', $template->subject) }}" class="form-control">
                </div>

                <div class="form-group mb-3">
                    <label>Tags</label>
                    <div id="tags-container" style="cursor: text; min-height: 45px;">
                        <div class="chips d-flex mb-2" style="gap: 3px" id="tag-chips">
                            @if(is_array($template->tags))
                            @foreach($template->tags as $tag)
                            <span class="badge bg-primary me-1 mb-1">
                                {{$tag}}
                                <span class="p-1 text-white cursor-pointer remove-tag" style="opacity: 0.7" aria-label="Remove" aria-hidden="true" onclick="($(this).parent().remove())">x</span>
                                <input type="hidden" name="tags[]" value="{{$tag}}">
                            </span>
                            @endforeach
                            @endif
                        </div>
                        <input type="text" id="tag-input" class="form-control flex-grow-1" placeholder="Type and press Enter">
                    </div>
                </div>

                <div class="form-group mb-3">
                    <label>Layout</label>
                    <select name="layout_id" id="layout_id" disabled class="form-control">
                        <option value="">-- None --</option>
                        @foreach($layouts as $layout)
                            <option value="{{ $layout->id }}" data-header="{{ $layout->header }}" data-footer="{{ $layout->footer }}"
                                {{ $template->layout_id == $layout->id ? 'selected' : '' }}>
                                {{ $layout->title }}
                            </option>
                        @endforeach
                    </select>
                </div>

                <div class="form-group mb-3">
                    <label>Body HTML</label>
                    <textarea name="body_html" id="body_html" rows="6" class="form-control">{{ old('body_html', $template->body_html) }}</textarea>
                </div>

                <button type="submit" class="btn btn-success">Save Template</button>
            </form>
        </div>

        {{-- Preview --}}
        <div class="tab-pane fade" id="preview" role="tabpanel" aria-labelledby="preview-tab">
            <h5 class="mb-3">Email Preview</h5>
            <div class="border rounded p-3" style="background:#f8f9fa;">
                <h6 class="text-muted">Subject: <span id="preview-subject">{{ $template->subject }}</span></h6>
                <div id="preview-header" class="mb-3 border-bottom pb-2">
                    {!! optional($template->layout)->header_html !!}
                </div>
                <div id="preview-body" class="mb-3">
                    {!! $template->body_html !!}
                </div>
                <div id="preview-footer" class="border-top pt-2">
                    {!! optional($template->layout)->footer_html !!}
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    let editor;

    function updatePreview() {
        const subject = $("#subject").val();
        const layout = $("#layout_id option:selected");
        const body = editor.getData();

        $("#preview-subject").text(subject);
        $("#preview-body").html(body);
    }

    function createTag(label) {
        if (!label) return;
        let exists = false;
        $("#tags-container input[name='tags[]']").each(function() {
            if ($(this).val().toLowerCase() === label.toLowerCase()) {
                exists = true;
                return false;
            }
        });
        if (exists) return;

        const $tag = $(`
            <span class="badge bg-primary me-1 mb-1">
                ${label}
                <span class="p-1 text-white cursor-pointer remove-tag" style="opacity: 0.7" aria-label="Remove" aria-hidden="true">x</span>
                <input type="hidden" name="tags[]" value="${label}">
            </span>
        `);

        $tag.find(".remove-tag").on("click", function () {
            $tag.remove();
        });

        $("#tag-chips").append($tag);
    }

    document.addEventListener("DOMContentLoaded", function() {
        editor = CKEDITOR.replace('body_html');

        editor.on('change', updatePreview);
        $("#subject, #layout_id").on("input change", updatePreview);

        // Tag input handling
        $("#tag-input").on("keydown", function (e) {
            const value = $(this).val().trim();
            if ((e.key === "Enter" || e.key === ",") && value !== "") {
                e.preventDefault();
                createTag(value);
                $(this).val("");
            }
            if (e.key === "Backspace" && value === "") {
                const $lastTag = $("#tags-container .badge").last();
                if ($lastTag.length) $lastTag.remove();
            }
        });
        $("#tags-container").on("click", function () {
            $("#tag-input").focus();
        });

        // Initial preview load
        editor.on("instanceReady", updatePreview);
        updatePreview();
    });
</script>
@endsection
