<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSchoolRequirementContractVersionsTable extends Migration
{
    public function up()
    {
        Schema::create('school_requirement_contract_versions', function (Blueprint $table) {
        $table->id();

        $table->unsignedBigInteger('school_requirement_contract_id');
        $table->foreign('school_requirement_contract_id', 'sr_contract_ver_fk')
            ->references('id')
            ->on('school_requirement_contracts')
            ->onDelete('cascade');

        $table->string('file_url');
        $table->string('version_number')->default('v1.0');
        $table->text('notes')->nullable();

        // Polymorphic user tracking (shortened index names)
        $table->unsignedBigInteger('created_by_id');
        $table->string('created_by_type');
        $table->index(['created_by_type', 'created_by_id'], 'sr_contract_ver_created_by_idx');

        $table->unsignedBigInteger('updated_by_id');
        $table->string('updated_by_type');
        $table->index(['updated_by_type', 'updated_by_id'], 'sr_contract_ver_updated_by_idx');

        $table->timestamps();
        $table->softDeletes();
    });
    }

    public function down()
    {
        Schema::dropIfExists('school_requirement_contract_versions');
    }
}
