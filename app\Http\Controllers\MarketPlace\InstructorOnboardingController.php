<?php

namespace App\Http\Controllers\MarketPlace;

use App\Http\Controllers\Controller;
use App\InstructorFifthStepOnboardingModel;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;
use Illuminate\Support\Facades\DB;
use Auth;

use App\Models\v1\SubjectArea;
use App\Models\v1\UserAvailability;
use App\Models\v1\UserAdditionalSubject;
use App\Models\v1\UserNotificationPreference;
use App\Models\v1\UserOnboardingFinalization;
use App\OnboardingInstructorLocation;
use Illuminate\Support\Facades\Http;

class InstructorOnboardingController extends Controller
{

    protected $allowedSteps = [
        "availibility",
        "location",
        "subjects",
        "sub",
        "profile",
        "phone",
        "notification"
    ];
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $user = Auth::guard('instructor')->user();

        if (!$user) {
            return response()->json(['message' => 'Unauthorized'], 401);
        }
        $userOnboarding = UserOnboardingFinalization::firstOrCreate(['user_id' => $user->id]);
        $availability = UserAvailability::where('user_id', $user->id)->get();
        $userSubjects = UserAdditionalSubject::where('user_id', $user->id)->get();
        $notificationPreferences = UserNotificationPreference::where('user_id', $user->id)->get();
        $locations = OnboardingInstructorLocation::where('user_id', $user->id)->first();
        $profile = InstructorFifthStepOnboardingModel::where('user_id', $user->id)->first();
        $progress = $userOnboarding->progress_stats;

        return response()->json([
            'message' => 'Instructor onboarding steps retrieved successfully.',
            'data' => [
                'availability' =>  $availability,
                'subjects' => $userSubjects,
                'onboarding' => $userOnboarding,
                'notifications' => $notificationPreferences,
                'locations' => $locations,
                'profile' => $profile,
                'progress' => $progress,
            ],
        ]);
    }


    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $step = $request->query('step');
        $userId = Auth::guard('instructor')->user()->id;
        $userEducator = Auth::guard('instructor')->user()->educator;
        $inPersonUser = Auth::guard('instructor')->user()->teach !== 'online';
        $finalization = UserOnboardingFinalization::firstOrCreate(['user_id' => $userId]);

        if (!$step || !in_array($step, $this->allowedSteps)) {
            throw ValidationException::withMessages([
                'step' => "Invalid or missing onboarding step: '{$step}'"
            ]);
        }

        $data = null;
        $progress = $finalization->progress ?? [];
        $finalizationPayload = [];

        try {
            DB::transaction(function () use ($request, $step, $userId, &$data, &$progress, &$finalizationPayload, &$finalization) {
                switch ($step) {
                    case 'availibility':
                        $validated = $request->validate([
                            'timezone' => 'required',
                            'availability' => 'required|array',
                            'hours_per_week' => 'required|integer|min:0',
                            'availability.*.id' => 'nullable|integer|exists:user_availability_v1,id',
                            'availability.*.day_of_week' => 'required|in:sunday,monday,tuesday,wednesday,thursday,friday,saturday',
                            'availability.*.start_time' => 'required|date_format:H:i:s',
                            'availability.*.end_time' => 'required|date_format:H:i:s|after:availability.*.start_time',
                        ]);

                        $submittedIds = [];
                        foreach ($validated['availability'] as $slot) {
                            if (isset($slot['id'])) {
                                $existing = UserAvailability::where('user_id', $userId)->where('id', $slot['id'])->first();
                                if ($existing) {
                                    $existing->update(['day_of_week' => $slot['day_of_week'], 'start_time' => $slot['start_time'], 'end_time' => $slot['end_time']]);
                                    $submittedIds[] = $existing->id;
                                }
                            } else {
                                $new = UserAvailability::create(['user_id' => $userId, 'day_of_week' => $slot['day_of_week'], 'start_time' => $slot['start_time'], 'end_time' => $slot['end_time']]);
                                $submittedIds[] = $new->id;
                            }
                        }
                        UserAvailability::where('user_id', $userId)->whereNotIn('id', $submittedIds)->delete();
                        $progress['availibility'] = 'completed';
                        $finalizationPayload['hours_per_week'] = $validated['hours_per_week'];
                        $finalizationPayload['timezone'] = $validated['timezone'];
                        $data = UserAvailability::where('user_id', $userId)->get();
                        break;

                    case 'subjects':
                        $check = $request->validate(['add_more_subjects' => 'required|in:yes,no']);
                        $progress['subjects'] = 'completed';
                        $finalizationPayload['add_more_subjects'] = ($check['add_more_subjects'] === 'yes') ? 1 : 0;

                        if ($check['add_more_subjects'] === 'yes') {
                            $validated = $request->validate([
                                'subjects' => 'required|array',
                                'subjects.*.subject_code' => 'nullable|string|max:50',
                                'subjects.*.subject_name' => 'nullable|string|max:255',
                                'subjects.*.proficiency' => 'required|integer|between:1,5',
                                'subjects.*.lesson_plan' => 'nullable',
                                'subjects.*.file_name' => 'nullable|string',
                                'subjects.*.file_url' => 'nullable|string',
                                'subjects.*.status' => 'nullable|in:pending,approved,rejected,accepted,declined',
                            ]);

                            $submittedCodes = [];
                            foreach ($validated['subjects'] as $key => $subject) {
                                $file = $request->file('subjects.' . $key . '.file');
                                $fileNames = $subject['file_url'] ?? null;
                                $originalName = $subject['file_name'] ?? null;

                                if ($file && $file->isValid()) {
                                    $originalName = $file->getClientOriginalName();
                                    $fullPath = 'uploads/marketplace/lesson_planning/' . $originalName;
                                    uploads3image($fullPath, $file);
                                    $fileNames = $fullPath;
                                }

                                UserAdditionalSubject::updateOrCreate(
                                    ['user_id' => $userId, 'subject_code' => $subject['subject_code']],
                                    ['subject_name' => $subject['subject_name'], 'proficiency' => $subject['proficiency'], 'lesson_plan_note' => $subject['description'] ?? null, 'file_name' => $originalName, 'file_url' => $fileNames, 'status' => $subject['status'] ?? 'pending']
                                );
                                $submittedCodes[] = $subject['subject_code'];
                            }
                            UserAdditionalSubject::where('user_id', $userId)->whereNotIn('subject_code', $submittedCodes)->delete();
                            $data = UserAdditionalSubject::where('user_id', $userId)->get();
                        } else {
                            UserAdditionalSubject::where('user_id', $userId)->delete();
                            $data = [];
                        }
                        break;

                    case 'sub':
                        $validated = $request->validate(['open_to_substitute_opportunity' => 'required|in:yes,no']);
                        $progress['sub'] = 'completed';
                        $finalizationPayload['open_to_substitute_opportunity'] = ($validated['open_to_substitute_opportunity'] == 'yes') ? 1 : 0;
                        $data = $finalization;
                        break;

                    case 'phone':
                        $validated = $request->validate([
                            'contact_phone_number' => 'required|string|max:20',
                            'contact_phone_country_code' => 'required|string|max:5',
                        ]);
                        $progress['phone'] = 'completed';
                        $finalizationPayload['phone_number'] = $validated['contact_phone_number'];
                        $finalizationPayload['country_code'] = $validated['contact_phone_country_code'];
                        $data = $finalization;
                        break;

                    case 'notification':
                        $validated = $request->validate([
                            'notifications' => 'required|array',
                            'notifications.*.notification_type' => 'required|string',
                            'notifications.*.in_app_notification' => 'nullable|in:on',
                            'notifications.*.email_notification' => 'nullable|in:on',
                            'notifications.*.realtime_email_notification' => 'nullable|in:on',
                            'notifications.*.daily_summary_email' => 'nullable|in:on',
                        ]);

                        foreach ($validated['notifications'] as $notification) {
                            UserNotificationPreference::updateOrCreate(
                                ['user_id' => $userId, 'notification_type' => $notification['notification_type']],
                                ['in_app_notifications' => isset($notification['in_app_notification']) ? 1 : 0, 'email_notifications' => isset($notification['email_notification']) ? 1 : 0, 'realtime_email_notifications' => isset($notification['realtime_email_notification']) ? 1 : 0, 'daily_summary_emails' => isset($notification['daily_summary_email']) ? 1 : 0]
                            );
                        }
                        $progress['notification'] = 'completed';
                        $data = UserNotificationPreference::where('user_id', $userId)->get();
                        break;

                    case 'location':
                        $validated = $request->validate([
                            'address' => 'required|string',
                            'radius' => 'required|integer|min:1',
                            'lat' => 'required|string',
                            'lng' => 'required|string',
                        ]);
                        OnboardingInstructorLocation::updateOrCreate(
                            ['user_id' => $userId],
                            ['location' => $validated['address'], 'address' => $validated['address'], 'radius' => $validated['radius'], 'lat' => $validated['lat'], 'lng' => $validated['lng']]
                        );
                        $progress['location'] = 'completed';
                        $data = OnboardingInstructorLocation::where('user_id', $userId)->get();
                        break;

                    case 'profile':
                        $validated = $request->validate([
                            'profile_image' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
                            'profile_title' => 'nullable|string',
                            'profile_tags' => 'nullable|string',
                            'profile_description' => 'nullable|string',
                            'video' => 'nullable|string',
                            'video_name' => 'nullable|string',
                            'video_source' => 'nullable|string',
                            'processing_video' => 'nullable|boolean',
                        ]);

                        $payload = [
                            'profile_title' => $validated['profile_title'] ?? null,
                            'profile_tags' => $validated['profile_tags'] ?? null,
                            'description' => $validated['profile_description'] ?? null,
                        ];

                        if ($request->hasFile("profile_image")) {
                            $image = $request->file("profile_image");
                            $filename = 'uploads/admin/' . uniqid() . '_' . $image->getClientOriginalName();
                            uploads3image($filename, $image);
                            $payload['profile_image'] = $filename;
                        }
                        InstructorFifthStepOnboardingModel::updateOrCreate(['user_id' => $userId], $payload);
                        $progress['profile'] = 'completed';
                        $data = InstructorFifthStepOnboardingModel::where('user_id', $userId)->first();
                        break;

                    default:
                        throw ValidationException::withMessages([
                            'step' => "Invalid onboarding step: '{$step}'"
                        ]);
                }
            });

            $finalization->update(array_merge(
                ['progress' => $progress],
                $finalizationPayload
            ));

            // Check for completion based on the user's educator type
            $allStepsCompleted = false;
            $requiredSteps = (strpos($userEducator, 'whizara educator') !== false) ?
                ['availibility', 'phone', 'subjects', 'profile', 'notification'] :
                ['availibility', 'phone', 'subjects', 'sub', 'notification'];

            if ($inPersonUser) {
                array_unshift($requiredSteps, 'location');
            }

            $allStepsCompleted = collect($requiredSteps)->every(fn($stepName) => isset($progress[$stepName]) && $progress[$stepName] === 'completed');
            if ($allStepsCompleted) {
                $finalization->update(['status' => 'completed']);
            }

            return response()->json([
                'message' => ucfirst($step) . ' saved successfully.',
                'data' => $data,
                'progress' => $finalization->progress_stats
            ], 201);

        } catch (ValidationException $e) {
            // Re-throw ValidationException as it's a specific, expected error.
            throw $e;
        } catch (\Exception $e) {
            // Catch any other unexpected exceptions.
            // You might log this error for debugging.

            // Return a generic error response.
            return response()->json([
                'message' => 'An error occurred while saving your data. Please try again.',
                'error' => $e->getMessage()
            ], 500);
        }
    }


    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }
}
