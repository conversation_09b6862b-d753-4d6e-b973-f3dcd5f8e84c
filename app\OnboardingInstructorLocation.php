<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class OnboardingInstructorLocation extends Model
{
    protected $table = 'onboarding_instructor_locations';
    protected $fillable = [
        'user_id',
        'location',
        'radius',
        'lat',
        'lng',
        'address',
    ];

    public function user()
    {
        return $this->belongsTo(OnboardingInstructor::class, 'user_id');
    }
}
