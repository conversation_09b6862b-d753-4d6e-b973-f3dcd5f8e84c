<?php

namespace App\Http\Controllers\MarketPlace;

use App\InstructorFifthStepOnboardingModel;
use App\Models\v1\UserOnboardingFinalization;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

use Session;
use Auth;

class UserProfileController extends Controller
{
    public function instructorProfile() {
        $instructor = Auth::guard('instructor')->user();
        $profile = InstructorFifthStepOnboardingModel::where('user_id', $instructor->id)->select('profile_image')->first();
        $userOnboarding = UserOnboardingFinalization::select('status', 'updated_at')->where('user_id', $instructor->id)->first();
        $instructor['onboarding_status'] = $userOnboarding ? $userOnboarding->status : 'incomplete';
        $instructor['onboarding_updated_at'] = $userOnboarding ? $userOnboarding->updated_at : null;
        $instructor['profile_image'] = optional($profile)['profile_image'];
        return response()->json([ "success" => true, "data" => $instructor ]);
    }

    public function sessionLogout() {
        Auth::guard('instructor')->logout();
        Session::forget('instructor');
        return response()->json([ "success" => true, "message" => "Session ended" ]);
    }

    public function userOpportunities() {
        return file_get_contents(public_path('k12connections/app.html'));
    }
}