<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateEmailScheduleConditionsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('email_schedule_conditions', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('schedule_id')->comment('Reference to email_schedules');
            $table->unsignedBigInteger('condition_id')->comment('Reference to email_conditions');
            $table->timestamps();

            $table->foreign('schedule_id')->references('id')->on('email_schedules')->onDelete('cascade');
            $table->foreign('condition_id')->references('id')->on('email_conditions')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('email_schedule_conditions');
    }
}
