<?php

namespace App\Http\Controllers\WEB;
use App\Http\Controllers\Controller;
use DB;
use Validator;
use Session;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\ValidationException;
use App\Http\Requests;
use Symfony\Component\HttpFoundation\Response;
use App\Users;
use App\FaqsModel;
use App\Resources;
use Hash;
use Mail;
use Auth;
DB::enableQueryLog();


class HomeController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        if(Auth::user()) {
            if(session('userewlogin'))
            { 
                Session::forget('schoolloginsession');
                return redirect("/web-dashboard");
            }else{
                return redirect("/k12connections/sign-in");  
            }
        }else{
            return redirect("/k12connections/sign-in");
        }
        
        // return view('web.index');
    }

    public function k12userLogin()
    {
        if(Auth::user()) {
            if(session('instructorlogin'))
            {
                return redirect("/k12connections/dashboard");
            }else{
                return redirect("/k12connections/sign-in");  
            }
        } else {
            return redirect("/k12connections/sign-in");  
        }
    }

    public function faq()
    {

        $data['faqs'] = FaqsModel::where("type",'Instructor')->where("status",'1')->get();
        return view('web.user.faq.faq')->with($data);
    }

    public function resourceDetails($type){
        $data['resource'] = Resources::where("type",$type)->where("status",'1') ->orderBy("title", "asc")->get();
        return view('web.user.resource')->with($data);
    }

    public function resources(){
        $data['resource'] = Resources::where("status",'1') ->orderBy("title", "asc")->get();
        return view('web.user.resource')->with($data);
    }


    public function school_resources(){
        $data['resource'] = Resources::where("status",'1') ->orderBy("title", "asc")->get();
        return view('school.resource')->with($data);
    }

    public function school_faq()
    {

        $data['faqs'] = FaqsModel::where("type",'school')->where("status",'1')->get();
        return view('school.faq')->with($data);
    }

    public function get_user(){
        if(Auth::user()->password){
            $pass=Auth::user()->password;
        }else{
            $pass='123456';
        }
        return json_encode(['email'=>Auth::user()->email,'password'=>'123456']);
       }

    public function signedS3Url(Request $request) {
    // Step 1: Validate input
        $validator = Validator::make($request->all(), [
            'filePath' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Invalid input.',
                'errors' => $validator->errors(),
            ], Response::HTTP_UNPROCESSABLE_ENTITY);
        }

        try {
            // Step 2: Check if the file exists in S3 (optional but recommended)
            $filePath = $request->input('filePath');

            if (!Storage::disk('s3')->exists($filePath)) {
                return response()->json([
                    'message' => 'File not found on S3.',
                ], Response::HTTP_NOT_FOUND);
            }

            // Step 3: Generate the signed URL
            $url = Storage::disk('s3')->temporaryUrl(
                $filePath,
                now()->addMinutes(15)
            );

            return response()->json(['url' => $url]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to generate signed URL.',
                'error' => $e->getMessage(),
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}