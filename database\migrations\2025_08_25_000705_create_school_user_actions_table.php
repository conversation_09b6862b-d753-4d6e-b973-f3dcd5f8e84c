<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class CreateSchoolUserActionsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('school_user_actions', function (Blueprint $table) {
            $table->unsignedBigInteger('user_id')->comment('FK: school_users.id');
            $table->unsignedBigInteger('action_id')->comment('FK: core_actions.id');

            $table->primary(['user_id', 'action_id']);

            $table->foreign('user_id')->references('id')->on('school_users')->onDelete('cascade');
            $table->foreign('action_id')->references('id')->on('core_actions')->onDelete('cascade');
        });

        DB::statement("ALTER TABLE `school_user_actions` COMMENT = 'Stores actual action-level permissions for each school user'");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('school_user_actions');
    }
}
