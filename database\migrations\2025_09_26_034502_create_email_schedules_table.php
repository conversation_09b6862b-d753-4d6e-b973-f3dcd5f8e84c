<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateEmailSchedulesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('email_schedules', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('name')->comment('Schedule name');
            $table->enum('status',['enabled','disabled'])->default('enabled')->comment('Schedule status');
            $table->enum('recurrence',['once','daily','weekly','monthly','yearly'])->default('once')->comment('Recurring type for scheduled emails');
            $table->string('event_name')->nullable()->comment('Event selected for immediate trigger');
            $table->json('recipients_to')->nullable()->comment('Array of recipients to send email to, e.g., ["all_educator","school","user:super_admin"]');
            $table->json('recipients_cc')->nullable()->comment('Array of CC recipients');
            $table->unsignedBigInteger('created_by')->nullable()->comment('Admin user who created the schedule');
            $table->timestamps();

            $table->foreign('created_by')->references('id')->on('users')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('email_schedules');
    }
}
