<?php

namespace App\Models\v1;

use Illuminate\Database\Eloquent\Model;
use App\OnboardingInstructor;
use App\OnboardingInstructorLocation;

class UserOnboardingFinalization extends Model
{
    protected $table = 'user_onboarding_finalization_v1';

    protected $fillable = [
        'user_id',
        'hours_per_week',
        'add_more_subjects',
        'open_to_substitute_opportunity',
        'phone_number',
        'country_code',
        'progress',
        'timezone',
        'status',
    ];

    protected $casts = [
        'progress' => 'array',
    ];

    public function instructor()
    {
        return $this->belongsTo(OnboardingInstructor::class, 'user_id');
    }

    public function additionalSubjects()
    {
        return $this->hasMany(UserAdditionalSubject::class, 'user_id', 'user_id');
    }

    public function availability()
    {
        return $this->hasMany(UserAvailability::class, 'user_id', 'user_id');
    }

    public function notificationPreference()
    {
        return $this->hasMany(UserNotificationPreference::class, 'user_id', 'user_id');
    }

    public function locations()
    {
        return $this->hasMany(OnboardingInstructorLocation::class, 'user_id', 'user_id');
    }

    public function getProgressStatsAttribute()
    {
        $progress = $this->progress ?? [];

        if (!is_array($progress)) {
            $progress = json_decode($progress, true) ?? [];
        }

        $total = 5;
        $completed = collect($progress)->filter(fn ($v) => $v === 'completed')->count();
        $percentage = $total > 0 ? round(($completed / $total) * 100, 2) : 0;

        return [
            'completed' => $completed,
            'total' => $total,
            'percentage' => $percentage,
        ];
    }
}
