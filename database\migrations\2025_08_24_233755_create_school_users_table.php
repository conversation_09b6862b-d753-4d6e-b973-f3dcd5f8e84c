<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class CreateSchoolUsersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('school_users', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('school_id')->comment('FK: schools.id');
            $table->string('job_title')->nullable()->comment('Job title of the user in the school');
            // User profile
            $table->string('first_name', 100)->comment('User first name');
            $table->string('last_name', 100)->comment('User last name');
            $table->enum('gender', ['male', 'female', 'other'])->nullable()->comment('User gender');
            $table->string('profile_image')->nullable()->comment('Profile image URL');

            // Contact information
            $table->string('email', 191)->comment('User email, unique within a school');
            $table->string('phone_number', 20)->nullable()->comment('Optional phone number');

            // Notifications preferences
            $table->boolean('email_notification')->default(false)->comment('Receive email notifications');
            $table->boolean('app_notification')->default(true)->comment('Receive app notifications');

            // Authentication
            $table->string('password')->comment('Password hash');
            $table->boolean('must_reset_password')->default(true)->comment('Force password reset at first login');
            $table->string('reset_password_token', 100)->nullable()->comment('Password reset token');
            $table->dateTime('reset_password_expires_at')->nullable()->comment('Token expiry time');

            // Flags & status
            $table->boolean('is_main_admin')->default(false)->comment('Indicates main admin of school');
            $table->boolean('is_user_invited')->default(false)->comment('Indicates that user invited or not');
            $table->enum('status', ['active', 'inactive', 'suspended'])->default('active')->comment('User account status');

            // Security metadata
            $table->dateTime('last_login_at')->nullable()->comment('Last login timestamp');
            $table->unsignedInteger('failed_attempts')->default(0)->comment('Failed login attempts counter');
            $table->dateTime('locked_until')->nullable()->comment('Account locked until this timestamp');

            // Soft delete & timestamps
            $table->softDeletes();
            $table->timestamps();

            // Constraints & indexes
            $table->unique(['school_id', 'email']);
            $table->foreign('school_id')->references('id')->on('schools')->onDelete('cascade');
            $table->index(['school_id', 'email', 'status']);
        });

        // Table-level comment
        DB::statement("ALTER TABLE `school_users` COMMENT = 'Stores all users under a school (admins, proctors, staff, etc.)'");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('school_users');
    }
}
