@if ($useModal)
  <!-- Modal Trigger -->
  <button class="btn btn-primary my-4" data-bs-toggle="modal" data-bs-target="#{{ $modalId }}">
    {{ $triggerButtonLabel }}
  </button>

  <!-- Modal -->
  <div class="modal fade" id="{{ $modalId }}" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
      <div class="modal-content border-0 rounded-3 shadow-lg">
        <div class="modal-header">
          <h5 class="modal-title">{{ $modalTitle }}</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
        </div>
        <div class="modal-body">
          @include('components.marketplace.educator._progress-stepper', ['steps' => $steps, 'useModal' => true, 'progress' => $progress, 'requiredCompletedCount' => $requiredCompletedCount, 'requiredCount' => $requiredCount])
        </div>
      </div>
    </div>
  </div>

  @once
  @push('scripts')
  <script>
  $(function() {
    $('#{{ $modalId }}').on('shown.bs.modal', function() {
      $('#{{ $modalId }} .btn-outline-primary').first().focus();
    })
  });
  </script>
  @endpush
  @endonce

@else
  <!-- Inline -->
  <div class="shadow rounded-xl p-4" id="{{ $modalId }}">
    <h5 class="mb-3">{{ isset($modalTitle) ? $modalTitle : '' }}</h5>
    @include('components.marketplace.educator._progress-stepper', ['steps' => $steps, 'useModal' => false, 'progress' => $progress, 'requiredCompletedCount' => $requiredCompletedCount, 'requiredCount' => $requiredCount])
  </div>
  
  @once
  @push('scripts')
  <script>
  $(function() {
    $('#{{ $modalId }}').on('shown.bs.modal', function() {
      $('#{{ $modalId }} .btn-outline-primary').first().focus();
    })
  });
  </script>
  @endpush
  @endonce
@endif
