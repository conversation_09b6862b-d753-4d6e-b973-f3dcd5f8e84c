<?php

namespace App\Models\v1;

use Illuminate\Database\Eloquent\Model;
use App\OnboardingInstructor;

class UserAdditionalSubject extends Model
{
    protected $table = 'user_additional_subjects_v1';

    protected $fillable = [
        'user_id',
        'subject_code',
        'subject_name',
        'proficiency',
        'lesson_plan_note',
        'file_name',
        'file_url',
        'status',
    ];

    public function instructor()
    {
        return $this->belongsTo(OnboardingInstructor::class, 'user_id');
    }
}
