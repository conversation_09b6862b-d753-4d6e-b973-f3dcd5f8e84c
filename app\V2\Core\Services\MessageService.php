<?php
namespace App\V2\Core\Services;

use App\Models\{Cha<PERSON>, Attachment, ChatbotChat};
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

class MessageService
{
    #region Message Thread
    public function FetchThreads(String $userId, String $type) {
        // Step 1: Get all relevant chats
        $chats = Chat::with(['requirement'])
            ->where(function ($q) use ($userId, $type) {
                $q->where(function ($sub) use ($userId, $type) {
                    $sub->where('sender_id', $userId)
                        ->where('sender_type', $type);
                })->orWhere(function ($sub) use ($userId, $type) {
                    $sub->where('recipient_id', $userId)
                        ->where('recipient_type', $type);
                });
            })
            ->orderByDesc('created_at')
            ->get();

        // Step 2: Group chats
        $groupedChats = $chats->groupBy(function ($chat) {
            if (in_array($chat->message_type, ['requirement', 'class'])) {
                return $chat->message_type . '_' . $chat->reference_id;
            }
            return $chat->sender_id < $chat->recipient_id
                ? $chat->sender_id . '_' . $chat->recipient_id
                : $chat->recipient_id . '_' . $chat->sender_id;
        });

        // Step 3: Fetch all unseen messages once
        $unseenMessages = Chat::where('recipient_id', $userId)
            ->where('recipient_type', $type)
            ->whereNull('read_at')
            ->get()
            ->groupBy(function ($chat) {
                if (in_array($chat->message_type, ['requirement', 'class'])) {
                    return $chat->message_type . '_' . $chat->reference_id;
                }
                return $chat->sender_id < $chat->recipient_id
                    ? $chat->sender_id . '_' . $chat->recipient_id
                    : $chat->recipient_id . '_' . $chat->sender_id;
            });

        // Step 4: Map grouped chats to API-friendly format
        return $groupedChats->map(function ($group) use ($unseenMessages, $userId, $type) {
            $chat = $group->first();
            $firstMessageId = $group->sortBy('created_at')->first()->id;

            // Resolve names
            $senderName = optional($chat->senderUser)->name
                ?? trim(optional($chat->senderUser)->first_name . ' ' . optional($chat->senderUser)->last_name);
            $recipientName = optional($chat->recipientUser)->name
                ?? trim(optional($chat->recipientUser)->first_name . ' ' . optional($chat->recipientUser)->last_name);

            // Determine name
            $fileName = null;
            if (in_array($chat->message_type, ['requirement', 'class'])) {
                $title = '';
                $name = $chat->requirement
                    ? $chat->requirement->name . ' (' . $senderName . ')'
                    : 'Group (' . $senderName . ')';
                $groupKey = $chat->message_type . '_' . $chat->reference_id;
            } else {
                if(($chat->sender_id == $userId && $chat->sender_type == $type)) {
                    $senderName = 'You';
                    $name = $recipientName;
                    $title = $this->getTitleBasedOnRole($chat->recipient_type, $chat->recipientUser);
                    $fileName = $this->getAvtarBasedOnRole($chat->recipient_type, $chat->recipientUser);
                } else {
                     $name = $senderName;
                    $title = $this->getTitleBasedOnRole($chat->sender_type, $chat->senderUser);
                    $fileName = $this->getAvtarBasedOnRole($chat->sender_type, $chat->senderUser);
                }
                $groupKey = $chat->sender_id < $chat->recipient_id
                    ? $chat->sender_id . '_' . $chat->recipient_id
                    : $chat->recipient_id . '_' . $chat->sender_id;
            }

            // Unseen count
            $unseenCount = isset($unseenMessages[$groupKey]) ? $unseenMessages[$groupKey]->count() : 0;

            return [
                'id'             => $firstMessageId,
                'name'           => $name,
                'title'          => $title,
                'type'           => $chat->message_type,
                'avtar'          => $fileName,
                'last_message'   => $chat,
                'last_message_by'=> $senderName,
                'unseen_count'   => $unseenCount,
                'time'           => $chat->created_at,
            ];
        })->values();


    }
    #endregion

    #region Messages By Thread
    public function MessagesByThread(String $threadId, $userId, String $type) {
        $message = Chat::findOrFail($threadId);
        // Group chat
        if (in_array($message->message_type, ['requirement', 'class']) && $message->reference_id) {
            $chats = Chat::where('message_type', $message->message_type)
                ->where('reference_id', $message->reference_id)
                ->where(function ($q) use ($userId, $type) {
                    $q->where('sender_id', $userId)
                        ->where('sender_type', $type)
                        ->orWhere('recipient_id', $userId)
                        ->where('recipient_type', $type);
                })
                ->orderBy('created_at')
                ->get();
            Chat::whereIn('id', $chats->pluck('id'))
                ->where('message_type', $message->message_type)
                ->where('reference_id', $message->reference_id)
                ->where('recipient_id', $userId)
                ->where('recipient_type', $type)
                ->whereNull('read_at')
                ->update([
                    'read_at' => now(),
                ]);
        } else { // 1-to-1 chat
            $chats = Chat::where(function ($q) use ($message) {
                    $q->where('sender_id', $message->sender_id)
                        ->where('sender_type', $message->sender_type)
                        ->whereNull('reference_id')
                        ->where('recipient_id', $message->recipient_id)
                        ->where('recipient_type', $message->recipient_type);
                })
                ->orWhere(function ($q) use ($message) {
                    $q->where('sender_id', $message->recipient_id)
                        ->where('sender_type', $message->recipient_type)
                        ->whereNull('reference_id')
                        ->where('recipient_id', $message->sender_id)
                        ->where('recipient_type', $message->sender_type);
                })
                ->orderBy('created_at')
                ->get();
                Chat::whereIn('id', $chats->pluck('id'))
                    ->where('recipient_id', $userId)
                    ->where('recipient_type', $type)
                    ->whereNull('read_at')
                    ->update([
                        'read_at' => now(),
                        'status'  => 'readed' // 👈 update status also
                    ]);
        }


        return $chats->map(function ($chat) {
            return [
                'id'             => $chat->id,
                'parent'         => $chat->parent,
                'status'         => $chat->status,
                'content'        => $chat->content,
                'requirement'    => $chat->requirement,
                'reference_id'   => $chat->reference_id,
                'message_type'   => $chat->message_type,
                'sender_avtar'   => $this->getAvtarBasedOnRole($chat->sender_type, $chat->senderUser),
                'sender_id'      => optional($chat->senderUser)->id,
                'sender_role'    => $chat->sender_type,
                'sender_name'    => optional($chat->senderUser)->name
                ?? trim(optional($chat->senderUser)->first_name . ' ' . optional($chat->senderUser)->last_name),
                'recipient_avtar'=> $this->getAvtarBasedOnRole($chat->recipient_type, $chat->recipientUser),
                'recipient_id'   => optional($chat->recipientUser)->id,
                'recipient_role' => $chat->recipient_type,
                'recipient_name' => optional($chat->recipientUser)->name
                ?? trim(optional($chat->recipientUser)->first_name . ' ' . optional($chat->recipientUser)->last_name),
                'read_at'        => $chat->read_at,
                'time'           => $chat->created_at,
                'delivered_at'   => $chat->delivered_at,
            ];
        });
    }
    #endregion

    private function getAvtarBasedOnRole(String $type, $profile) {
        $fileName = null;
        if($type == 'educator') {
            $fileName = $profile->image;
        } else if($type == 'school') {
            $fileName = optional($profile)->profile_image;
        } else if ($type == 'admin') {
            $fileName = $profile->image;
        }
        return $fileName;
    }
    private function getTitleBasedOnRole(String $type, $profile) {
        $title = null;
        if($type == 'educator') {
            $title = $profile->step5->profile_title;
        } else if($type == 'school') {
            $title = optional($profile)->job_title;
        } else if ($type == 'admin') {
            $title = 'Whizara';
        }
        return $title;
    }

}