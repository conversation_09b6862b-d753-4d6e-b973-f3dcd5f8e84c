<?php

namespace App\Models\v1;

use Illuminate\Database\Eloquent\Model;

class EmailScheduler extends Model
{
    protected $table = 'email_schedules';
    protected $fillable = [
        'name', 'status', 'recurrence', 'event_name', 'recipients_to', 'recipients_cc', 'created_by'
    ];

    protected $casts = [
        'recipients_to' => 'array',
        'recipients_cc' => 'array',
    ];

    public function templates()
    {
        return $this->belongsToMany(EmailTemplate::class, 'email_schedules_template', 'schedule_id', 'template_id')
                    ->withTimestamps();
    }

    public function conditions()
    {
        return $this->belongsToMany(EmailCondition::class, 'email_schedule_conditions', 'schedule_id', 'condition_id')
                    ->withTimestamps();
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }
}
