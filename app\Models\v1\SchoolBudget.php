<?php

namespace App\Models\v1;

use Illuminate\Database\Eloquent\Model;

class SchoolBudget extends Model
{
    protected $table = 'school_budget_v2';

    protected $fillable = [
        'name',
        'in_person',
        'case_management',
        'bilingual_inc',
        'sped_rec_comp',
    ];

    protected $casts = [
        'in_person' => 'float',
        'case_management' => 'float',
        'bilingual_inc' => 'float',
        'sped_rec_comp' => 'float',
    ];

    public function subjectBudgets()
    {
        return $this->hasMany(SchoolSubjectBudget::class, 'school_budget_id');
    }
}
