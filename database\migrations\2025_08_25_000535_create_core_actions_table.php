<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class CreateCoreActionsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('core_actions', function (Blueprint $table) {
            $table->id();
            $table->string('action_key', 100)->unique()->comment('Unique action identifier, e.g. manage_staff');
            $table->string('description')->nullable()->comment('Human readable label for action');
            $table->timestamps();
        });

        DB::statement("ALTER TABLE `core_actions` COMMENT = 'Global master list of possible actions/permissions'");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('core_actions');
    }
}
