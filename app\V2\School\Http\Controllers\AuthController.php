<?php

namespace App\V2\School\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\v1\SchoolUser;
use App\Schools;
use Exception;
use DB;
use App\V2\Core\Services\OtpService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth as FacadesAuth;
use Illuminate\Support\Facades\DB as FacadesDB;
use Illuminate\Support\Facades\Hash as FacadesHash;
use Illuminate\Support\Facades\Mail as FacadesMail;
use Illuminate\Support\Facades\Session as FacadesSession;
use GuzzleHttp\Client;
use Illuminate\Validation\ValidationException;
use App\V2\Core\Helpers\ApiResponse;

class AuthController extends Controller
{

    protected OtpService $otpService;

    public function __construct(OtpService $otpService)
    {
        $this->otpService = $otpService;
    }

    /**
     * School Login
     */
    public function login(Request $request)
    {
        try {
            $request->validate([
                'email' => 'required|email',
                'password' => 'required'
            ], [
                'email.required' => 'Email field is required',
                'password.required' => 'Password field is required',
            ]);
        } catch (ValidationException $e) {
            return ApiResponse::error("Validation failed", 422, $e->errors());
        }
        $email = $request->email;
        $password = $request->password;

        $user = SchoolUser::where('email', $email)->first();
        if (!$user) {
            return ApiResponse::error("Login failed, please check email id", 401);
        }

        if ((bool) $user->must_reset_password) {
            return ApiResponse::error("For security reasons, you must reset your password before accessing the system.", 403, [ 'must_reset_password' => true ]);
        }
        // Check user type, credentials and status
        if (!FacadesHash::check($password, $user->password)) {
            return ApiResponse::error("Login failed. Please check your password", 401);
        }
        if ($user->status == 'inactive') {
            return ApiResponse::error("Your account is deactivated", 403);
        }
        if ($user->status == 'suspended') {
            return ApiResponse::error("Your account is suspended", 403);
        }
        // Login and session handling
        FacadesSession::forget('userewlogin');
        FacadesAuth::guard('platform_school')->login($user);
        session([
            'schoolloginsession' => [
                'id'    => $user->id,
                'name'  => $user->first_name,
                'email' => $user->email,
                // 'type'  => $user->type
            ]
        ]);

        // Fetch chat token
        $chatToken = null;
        try {
            $baseUrl = config('services.node.url', 'http://localhost:8000');
            $apiUrl  = "$baseUrl/user/token?userId={$user->id}&userType=school";

            $client   = new Client();
            $response = $client->get($apiUrl, ['headers' => ['Accept' => 'application/json']]);

            $chatToken = $response->getBody()->getContents();
            if (!empty($chatToken)) {
                session(['chatToken' => $chatToken]);
            }
        } catch (Exception $e) {
            return ApiResponse::error("Error fetching chat token", 500, config('app.debug') ? [$e->getMessage()] : []);
        }

        session(['user_timezone' => $request->input('timezone')]);

        return ApiResponse::success([
            'user'      => $user,
            'chatToken' => $chatToken
        ], "Login successful");
    }

    /**
     * Get logged in school user profile
     */
    public function profile()
    {
        $sessionData = session('schoolloginsession');

        if (!$sessionData || !isset($sessionData['id'])) {
            return ApiResponse::error("User not logged in", 401);
        }

        $user = SchoolUser::with('school')->find($sessionData['id']);

        if (!$user) {
            return ApiResponse::error("User not found", 404);
        }

        return ApiResponse::success($user, "Profile retrieved successfully");
    }

    public function resetPassword(Request $request)
    {
        try {
            $request->validate([
                'email'           => 'required|email',
                'currentPassword' => 'required',
                'password'        => 'required|min:8',
            ], [
                'email.required'           => 'Email field is required',
                'currentPassword.required' => 'Current password field is required',
                'password.required'        => 'Password field is required',
            ]);
        } catch (ValidationException $e) {
            return ApiResponse::error("Validation failed", 422, $e->errors());
        }

        $user = SchoolUser::where('email', $request->email)->first();

        if (!$user) {
            return ApiResponse::error("No account found with this email address", 404);
        }

        // Verify current password
        if (!FacadesHash::check($request->currentPassword, $user->password)) {
            FacadesSession::forget('schoolloginsession');
            return ApiResponse::error("Your current password is incorrect", 401);
        }

        // Status checks
        if ($user->status === 'inactive') {
            return ApiResponse::error("Your account is deactivated", 403);
        }
        if ($user->status === 'suspended') {
            return ApiResponse::error("Your account is suspended", 403);
        }

        // Update password
        $user->update([
            'password'           => FacadesHash::make($request->password),
            'must_reset_password'=> 0,
        ]);

        // Optionally log them in after reset
        FacadesAuth::guard('platform_school')->login($user);

        session([
            'schoolloginsession' => [
                'id'    => $user->id,
                'name'  => $user->first_name,
                'email' => $user->email,
            ],
            'user_timezone' => $request->input('timezone'),
        ]);
        $chatToken = null;
        try {
            $baseUrl = config('services.node.url', 'http://localhost:8000');
            $apiUrl  = "$baseUrl/user/token?userId={$user->id}&userType=school";

            $client   = new Client();
            $response = $client->get($apiUrl, ['headers' => ['Accept' => 'application/json']]);

            $chatToken = $response->getBody()->getContents();
            if (!empty($chatToken)) {
                session(['chatToken' => $chatToken]);
            }
        } catch (Exception $e) {
            return ApiResponse::error("Error fetching chat token", 500, config('app.debug') ? [$e->getMessage()] : []);
        }


        return ApiResponse::success([
            'user' => $user,
            'chatToken' => $chatToken,
        ], "Password has been reset successfully");
    }

    public function checkResetRequired(Request $request)
    {
        try {
            $request->validate([
                'email' => 'required|email',
            ], [
                'email.required' => 'Email field is required',
            ]);
        } catch (ValidationException $e) {
            return ApiResponse::error("Validation failed", 422, $e->errors());
        }

        $user = SchoolUser::where('email', $request->email)->first();

        if (!$user) {
            return ApiResponse::error("No account found with this email address", 404);
        }

        // Status checks (optional, same as in login/reset)
        if ($user->status === 'inactive') {
            return ApiResponse::error("Your account is deactivated", 403);
        }
        if ($user->status === 'suspended') {
            return ApiResponse::error("Your account is suspended", 403);
        }

        return ApiResponse::success([
            'mustResetPassword' => (bool) $user->must_reset_password,
        ], "Password reset requirement checked successfully");
    }

    /**
     * Send OTP for forgot password or verification
     */
    public function sendOtp(Request $request)
    {
        try {
            $request->validate(['email' => 'required|email']);
        } catch (ValidationException $e) {
            return ApiResponse::error("Validation failed", 422, $e->errors());
        }

        $user = SchoolUser::where('email', $request->email)->first();
        if (!$user) {
            return ApiResponse::error("No account found with this email", 404);
        }

        $otp = $this->otpService->generate($user->id, 'school');

        $template = FacadesDB::table("tbl_email_templates")->where("title", 'whizara-schools:forget-password-otp')->first();
        $body = str_replace(['{{NAME}}', '{{OTP_CODE}}'], [$user->first_name . ' ' . $user->last_name, $otp->otp_code], $template->description);

        FacadesMail::send('template', ['template' => $body], function ($message) use ($user, $template) {
            $message->to($user->email)->subject($template->subject);
        });

        return ApiResponse::success([], "OTP sent successfully");
    }

    /**
     * Resend OTP (extend expiry if valid, else create new)
     */
    public function resendOtp(Request $request)
    {
        try {
            $request->validate(['email' => 'required|email']);
        } catch (ValidationException $e) {
            return ApiResponse::error("Validation failed", 422, $e->errors());
        }

        $user = SchoolUser::where('email', $request->email)->first();
        if (!$user) {
            return ApiResponse::error("No account found with this email", 404);
        }

        $otp = $this->otpService->resend($user->id, 'school');

        $template = FacadesDB::table("tbl_email_templates")->where("title", 'whizara-schools:forget-password-otp')->first();
        $body = str_replace(['{{NAME}}', '{{OTP_CODE}}'], [$user->first_name . ' ' . $user->last_name, $otp->otp_code], $template->description);

        FacadesMail::send('template', ['template' => $body], function ($message) use ($user, $template) {
            $message->to($user->email)->subject($template->subject);
        });

        return ApiResponse::success([], "OTP resent successfully");
    }

    /**
     * Verify OTP (store flag in session for reset)
     */
    public function verifyOtp(Request $request)
    {
        try {
            $request->validate([
                'email' => 'required|email',
                'otp'   => 'required|string',
            ]);
        } catch (ValidationException $e) {
            return ApiResponse::error("Validation failed", 422, $e->errors());
        }

        $user = SchoolUser::where('email', $request->email)->first();
        if (!$user) {
            return ApiResponse::error("No account found with this email", 404);
        }

        $valid = $this->otpService->verify($user->id, $request->otp, 'school');
        if (!$valid) {
            return ApiResponse::error("Invalid or expired OTP", 400);
        }

        // Add session flag for OTP verification
        FacadesSession::put("otp_verified_{$user->id}", true);

        return ApiResponse::success([], "OTP verified successfully");
    }

    /**
     * Reset password with verified OTP
     */
    public function resetPasswordWithOtp(Request $request)
    {
        try {
            $request->validate([
                'email'    => 'required|email',
                'password' => 'required|min:8',
            ]);
        } catch (ValidationException $e) {
            return ApiResponse::error("Validation failed", 422, $e->errors());
        }

        $user = SchoolUser::where('email', $request->email)->first();
        if (!$user) {
            return ApiResponse::error("No account found with this email", 404);
        }

        // Ensure OTP verified
        if (!FacadesSession::pull("otp_verified_{$user->id}")) {
            return ApiResponse::error("OTP verification required", 403);
        }

        // Update password
        $user->update([
            'password'           => FacadesHash::make($request->password),
            'must_reset_password'=> 0,
        ]);

        return ApiResponse::success([], "Password reset successfully");
    }

    public function sessionLogout() {
        FacadesSession::forget('schoolloginsession');
        FacadesAuth::guard('platform_school')->logout();
        return response()->json([ "success" => true, "message" => "Session ended" ]);
    }
}
