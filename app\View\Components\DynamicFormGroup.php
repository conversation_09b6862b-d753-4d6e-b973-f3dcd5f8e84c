<?php

namespace App\View\Components;

use Illuminate\View\Component;

class DynamicFormGroup extends Component
{
    public $fields;
    public $index;
    /**
     * Create a new component instance.
     *
     * @return void
     */
    public function __construct(array $fields, $index = null)
    {
        $this->fields = $fields;
        $this->index = $index;
    }

    /**
     * Get the view / contents that represent the component.
     *
     * @return \Illuminate\View\View|string
     */
    public function render()
    {
        return view('components.dynamic-form-group');
    }
}
